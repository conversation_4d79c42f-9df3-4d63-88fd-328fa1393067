"""
Unit tests for the services processor module.
"""

import pytest
from unittest.mock import patch, MagicMock

from freshconnect.processors.services import ServiceProcessor, fetch_service_items, get_service_categories, analyze_service_catalog


@pytest.mark.unit
@pytest.mark.processors
class TestServiceProcessor:
    """Tests for the ServiceProcessor class."""

    def test_init(self, mock_api_client, mock_db):
        """Test initialization of the processor."""
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)
        assert processor.api_client is mock_api_client
        assert processor.db is mock_db

    def test_fetch_and_store_service_items(self, mock_api_client, mock_db):
        """Test fetching and storing service items."""
        # Create processor with mocks
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)

        # Call the method
        count = processor.fetch_and_store_service_items()

        # Verify the API was called
        mock_api_client.get_all_pages.assert_called_once_with('service_catalog/items')

        # Verify the database was updated
        assert count == 2  # Two items in the mock response

    def test_fetch_and_store_service_items_with_max(self, mock_api_client, mock_db):
        """Test fetching and storing service items with a maximum limit."""
        # Create processor with mocks
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)

        # Call the method with max_items=1
        count = processor.fetch_and_store_service_items(max_items=1)

        # Verify the API was called
        mock_api_client.get_all_pages.assert_called_once_with('service_catalog/items')

        # Verify only one item was processed
        assert count == 1

    def test_get_service_item_categories(self, mock_api_client, mock_db):
        """Test getting service item categories."""
        # Create processor with mocks
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)

        # Call the method
        categories = processor.get_service_item_categories()

        # Verify the result
        assert len(categories) == 2
        assert "Test Category" in categories
        assert "Another Category" in categories

    def test_get_items_by_category(self, mock_api_client, mock_db):
        """Test getting items by category."""
        # Create processor with mocks
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)

        # Call the method
        items = processor.get_items_by_category("Test Category")

        # Verify the result
        assert len(items) == 1
        assert items[0]["id"] == 1
        assert items[0]["name"] == "Test Service Item 1"

    def test_analyze_service_catalog(self, mock_api_client, mock_db):
        """Test analyzing the service catalog."""
        # Create processor with mocks
        processor = ServiceProcessor(api_client=mock_api_client, db=mock_db)

        # Call the method
        analysis = processor.analyze_service_catalog()

        # Verify the result
        assert analysis is not None
        assert analysis["total_items"] == 2
        assert len(analysis["categories"]) == 2
        assert analysis["categories"]["Test Category"] == 1
        assert analysis["categories"]["Another Category"] == 1
        assert analysis["visibility"]["all"] == 1
        assert analysis["visibility"]["agents"] == 1


@pytest.mark.unit
@pytest.mark.processors
class TestServiceFunctions:
    """Tests for the service processor functions."""

    @patch('freshconnect.processors.services.ServiceProcessor')
    def test_fetch_service_items(self, mock_processor_class):
        """Test the fetch_service_items function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.fetch_and_store_service_items.return_value = 2
        mock_processor_class.return_value = mock_processor

        # Call the function
        count = fetch_service_items(max_items=10)

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.fetch_and_store_service_items.assert_called_once_with(10)
        assert count == 2

    @patch('freshconnect.processors.services.ServiceProcessor')
    def test_get_service_categories(self, mock_processor_class):
        """Test the get_service_categories function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.get_service_item_categories.return_value = ["Category 1", "Category 2"]
        mock_processor_class.return_value = mock_processor

        # Call the function
        categories = get_service_categories()

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.get_service_item_categories.assert_called_once()
        assert categories == ["Category 1", "Category 2"]

    @patch('freshconnect.processors.services.ServiceProcessor')
    def test_analyze_service_catalog(self, mock_processor_class):
        """Test the analyze_service_catalog function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.analyze_service_catalog.return_value = {"total_items": 2}
        mock_processor_class.return_value = mock_processor

        # Call the function
        analysis = analyze_service_catalog()

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.analyze_service_catalog.assert_called_once()
        assert analysis == {"total_items": 2}
