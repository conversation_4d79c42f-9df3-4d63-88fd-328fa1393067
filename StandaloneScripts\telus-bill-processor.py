import pdfplumber
import pandas as pd
import re
from datetime import datetime

def extract_pdf_text(pdf_path):
    text_content = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text_content += page.extract_text() + "\n"
    return text_content

def extract_phone_lines(text_content):
    phone_lines = []
    # Find sections with charges for each line
    sections = re.findall(r"(Charges for (\d{3} \d{3}-\d{4}).*?(?=Charges for|\Z))", 
                         text_content, re.DOTALL)
    
    for section in sections:
        section_text = section[0]
        phone_number = section[1]
        
        # Extract monthly charge and plan type
        plan_match = re.search(r"Corporate Complete (\d{2,3})\s+\$(\d+\.\d{2})", section_text)
        if plan_match:
            plan_type = f"Corporate Complete {plan_match.group(1)}"
            monthly_charge = float(plan_match.group(2))
        else:
            plan_type = "N/A"
            monthly_charge = 0.0
            
        # Extract taxes
        taxes = sum([float(x) for x in re.findall(r"(?:GST|HST-ON|PST-BC)\s+\$(\d+\.\d{2})", section_text)])
        
        # Extract total
        total_match = re.search(r"Total for.*?with taxes.*?\$(\d+\.\d{2})", section_text)
        total = float(total_match.group(1)) if total_match else 0.0
        
        phone_lines.append({
            'Phone Number': phone_number,
            'Plan Type': plan_type,
            'Monthly Charge': monthly_charge,
            'Taxes': taxes,
            'Total': total
        })
    
    return pd.DataFrame(phone_lines)

def extract_hardware_credits(text_content):
    hardware_credits = []
    
    # Find device balance summary section
    device_section = re.search(r"Summary of Device Balance by user.*?Corporate Advantage", 
                             text_content, re.DOTALL)
    
    if device_section:
        # Extract device balance entries
        pattern = r"(\d{3} \d{3}-\d{4})\s+(\d+\.\d{2})\s+-(\d+\.\d{2})\s+(\d+\.\d{2})\s+(\w+\s+\d+,\s+\d{4})"
        matches = re.finditer(pattern, device_section.group())
        
        for match in matches:
            hardware_credits.append({
                'Phone Number': match.group(1),
                'Starting Balance': float(match.group(2)),
                'Monthly Credit': float(match.group(3)),
                'Current Balance': float(match.group(4)),
                'End Date': match.group(5)
            })
    
    return pd.DataFrame(hardware_credits)

def extract_usage_data(text_content):
    usage_data = []
    
    # Find all usage sections
    sections = re.finditer(r"Usage charges.*?(?=Mobile services|\Z)", text_content, re.DOTALL)
    
    for section in sections:
        section_text = section.group()
        
        # Extract phone number
        phone_match = re.search(r"for (\d{3} \d{3}-\d{4})", section_text)
        if not phone_match:
            continue
            
        phone_number = phone_match.group(1)
        
        # Extract data usage
        data_match = re.search(r"Data Usage.*?Total used ([\d,]+\.?\d*) \((?:MB|GB)\)", 
                             section_text, re.DOTALL)
        data_usage = float(data_match.group(1).replace(',', '')) if data_match else 0.0
        
        # Extract minutes
        minutes_match = re.search(r"Local Airtime.*?Total used ([\d,]+\.?\d*) \(MIN\)", 
                                section_text, re.DOTALL)
        minutes = float(minutes_match.group(1).replace(',', '')) if minutes_match else 0.0
        
        # Extract SMS
        sms_pattern = r"Text Msg.*?Total used (\d+) \(Msg\)"
        sent_sms = re.search(rf"Text Msg - Sent.*?{sms_pattern}", section_text, re.DOTALL)
        received_sms = re.search(rf"Text Msg - Received.*?{sms_pattern}", section_text, re.DOTALL)
        
        usage_data.append({
            'Phone Number': phone_number,
            'Data Usage (MB)': data_usage,
            'Voice Minutes': minutes,
            'SMS Sent': int(sent_sms.group(1)) if sent_sms else 0,
            'SMS Received': int(received_sms.group(1)) if received_sms else 0
        })
    
    return pd.DataFrame(usage_data)

def process_telus_bill(pdf_path):
    # Extract text from PDF
    text_content = extract_pdf_text(pdf_path)
    
    # Process data and create Excel file
    output_file = 'telus_bill_analysis.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Process and write phone lines sheet
        phone_lines_df = extract_phone_lines(text_content)
        phone_lines_df.to_excel(writer, sheet_name='Phone Lines', index=False)
        
        # Process and write hardware credits sheet
        hardware_credits_df = extract_hardware_credits(text_content)
        hardware_credits_df.to_excel(writer, sheet_name='Hardware Credits', index=False)
        
        # Process and write usage data sheet
        usage_df = extract_usage_data(text_content)
        usage_df.to_excel(writer, sheet_name='Usage Data', index=False)
        
        # Auto-adjust column widths
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column = [cell for cell in column]
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(cell.value)
                    except:
                        pass
                adjusted_width = (max_length + 2)
                worksheet.column_dimensions[column[0].column_letter].width = adjusted_width

    return output_file

if __name__ == "__main__":
    pdf_path = "Storage\\PDF\\1900087393.pdf"  # Replace with your PDF path
    output_file = process_telus_bill(pdf_path)
    print(f"Bill data processed and saved to {output_file}")
