#!/usr/bin/env python3
"""
FreshService Departments Fetcher

This script fetches all departments from FreshService API and saves them to a JSON file.
It includes department IDs, names, descriptions, and other relevant information.

Usage:
    python fetch_departments.py

The script will create a JSON file with all department information.
"""

import sys
import os
import json
import requests
from datetime import datetime
from dotenv import load_dotenv


class FreshServiceDepartmentFetcher:
    """Class to handle FreshService API calls for departments."""
    
    def __init__(self):
        """Initialize the FreshService client."""
        # Load environment variables
        load_dotenv()
        
        # Get API credentials from environment
        self.api_key = os.getenv('FRESHSERVICE_API_KEY')
        self.domain = os.getenv('FRESHSERVICE_DOMAIN', 'kaservicedesk')

        if not self.api_key:
            raise ValueError("FRESHSERVICE_API_KEY not found in environment variables")

        # Set up API configuration - handle domain properly
        if self.domain.endswith('.freshservice.com'):
            self.base_url = f"https://{self.domain}/api/v2"
        else:
            self.base_url = f"https://{self.domain}.freshservice.com/api/v2"
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.auth = (self.api_key, 'X')
        
        print(f"Initialized FreshService client for domain: {self.domain}")
    
    def _make_request(self, method, endpoint, params=None, retry_count=0, max_retries=3):
        """
        Make an API request with rate limiting and retry logic.
        
        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint
            params (dict): Query parameters
            retry_count (int): Current retry attempt
            max_retries (int): Maximum number of retries
            
        Returns:
            requests.Response: API response
        """
        url = f"{self.base_url}/{endpoint}"
        retry_delay = 2  # seconds
        
        try:
            print(f"Making {method} request to: {url}")
            
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, auth=self.auth, params=params)
            else:
                response = requests.request(method, url, headers=self.headers, auth=self.auth, json=params)
            
            # Check rate limit headers and wait if needed
            rate_limit_remaining = response.headers.get('X-Ratelimit-Remaining')
            if rate_limit_remaining:
                try:
                    remaining = int(rate_limit_remaining)
                    print(f"Rate limit remaining: {remaining}")
                    
                    # If remaining requests is less than 65, wait 10 seconds
                    if remaining < 65:
                        print(f"Rate limit remaining ({remaining}) is below 65. Waiting 10 seconds...")
                        sys.stdout.flush()  # Force output to display immediately
                        import time
                        time.sleep(10)
                        print(f"Rate limit wait completed. Continuing...")
                        sys.stdout.flush()  # Force output to display immediately
                except ValueError:
                    pass  # Ignore if header value is not a valid integer
            
            # Handle rate limiting (429 status)
            if response.status_code == 429:
                if retry_count < max_retries:
                    print(f"Rate limited (429). Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                    sys.stdout.flush()  # Force output to display immediately
                    import time
                    time.sleep(retry_delay)
                    return self._make_request(method, endpoint, params, retry_count + 1)
                else:
                    raise requests.exceptions.RequestException("Rate limit exceeded after maximum retries")
            
            # Handle other HTTP errors
            if not response.ok:
                print(f"API request failed with status {response.status_code}: {response.text}")
                response.raise_for_status()
            
            return response
            
        except requests.exceptions.RequestException as e:
            if retry_count < max_retries:
                print(f"Request failed: {e}. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                import time
                time.sleep(retry_delay)
                return self._make_request(method, endpoint, params, retry_count + 1)
            else:
                raise
    
    def get_departments(self):
        """
        Fetch all departments from FreshService.
        
        Returns:
            list: List of department dictionaries
        """
        print("Fetching departments from FreshService...")
        
        departments = []
        page = 1
        per_page = 100  # Maximum allowed by FreshService
        
        while True:
            params = {
                'page': page,
                'per_page': per_page
            }
            
            try:
                response = self._make_request('GET', 'departments', params)
                data = response.json()
                
                # Extract departments from response
                page_departments = data.get('departments', [])
                
                if not page_departments:
                    break  # No more departments
                
                departments.extend(page_departments)
                print(f"Fetched {len(page_departments)} departments from page {page}")
                
                # Check if there are more pages
                if len(page_departments) < per_page:
                    break  # Last page
                
                page += 1
                
            except Exception as e:
                print(f"Error fetching departments on page {page}: {e}")
                break
        
        print(f"Total departments fetched: {len(departments)}")
        return departments


def save_departments_to_json(departments, filename=None):
    """
    Save departments data to a JSON file.
    
    Args:
        departments (list): List of department dictionaries
        filename (str): Output filename (optional)
    """
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"freshservice_departments_{timestamp}.json"
    
    # Create structured data
    output_data = {
        'metadata': {
            'total_departments': len(departments),
            'fetch_timestamp': datetime.now().isoformat(),
            'api_version': 'v2'
        },
        'departments': departments
    }
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\nDepartments saved to: {filename}")
        
        # Print summary
        print(f"\nSUMMARY:")
        print(f"Total departments: {len(departments)}")
        
        # Show some sample departments
        if departments:
            print(f"\nSample departments:")
            for i, dept in enumerate(departments[:5], 1):
                dept_id = dept.get('id', 'N/A')
                dept_name = dept.get('name', 'N/A')
                dept_description = dept.get('description', 'N/A')
                print(f"  {i}. ID: {dept_id}, Name: {dept_name}")
                if dept_description and dept_description != 'N/A':
                    print(f"     Description: {dept_description}")
        
        sys.stdout.flush()  # Force output to display immediately
        
    except Exception as e:
        print(f"Error saving departments to file: {e}")
        sys.stdout.flush()


def main():
    """Main function to fetch departments."""
    print("FreshService Departments Fetcher")
    print("=" * 50)
    sys.stdout.flush()  # Force output to display immediately
    
    try:
        # Initialize the fetcher
        print("\nStep 1: Initializing FreshService client...")
        sys.stdout.flush()  # Force output to display immediately
        fetcher = FreshServiceDepartmentFetcher()
        
        # Fetch departments
        print("\nStep 2: Fetching departments...")
        sys.stdout.flush()  # Force output to display immediately
        departments = fetcher.get_departments()
        
        if not departments:
            print("No departments found.")
            sys.exit(1)
        
        # Save results
        print("\nStep 3: Saving results...")
        sys.stdout.flush()  # Force output to display immediately
        save_departments_to_json(departments)
        
        print("\n" + "=" * 50)
        print("Process completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
