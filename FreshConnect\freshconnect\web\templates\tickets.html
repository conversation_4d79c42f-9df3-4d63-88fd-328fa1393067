{% extends "base.html" %}

{% block title %}FreshConnect - Tickets{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-ticket-alt me-2"></i>Tickets</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#fetchTicketsModal">
                <i class="fas fa-sync-alt me-1"></i> Fetch Tickets
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#fetchHistoryModal">
                <i class="fas fa-history me-1"></i> Fetch History
            </button>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-filter me-1"></i> Filter
            </button>
            <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                <li><a class="dropdown-item" href="#" data-filter="all">All Tickets</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-filter="open">Open</a></li>
                <li><a class="dropdown-item" href="#" data-filter="pending">Pending</a></li>
                <li><a class="dropdown-item" href="#" data-filter="resolved">Resolved</a></li>
                <li><a class="dropdown-item" href="#" data-filter="closed">Closed</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Search and Filter Bar -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" placeholder="Search tickets...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">All Statuses</option>
                            <option value="2">Open</option>
                            <option value="3">Pending</option>
                            <option value="4">Resolved</option>
                            <option value="5">Closed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="priorityFilter">
                            <option value="">All Priorities</option>
                            <option value="1">Low</option>
                            <option value="2">Medium</option>
                            <option value="3">High</option>
                            <option value="4">Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Apply</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tickets Table -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Subject</th>
                                <th>Requester</th>
                                <th>Status</th>
                                <th>Priority</th>
                                <th>Group</th>
                                <th>Agent</th>
                                <th>Created</th>
                                <th>Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ticket in tickets|default([]) %}
                            <tr>
                                <td><a href="{{ url_for('ticket_detail', ticket_id=ticket.id) }}">#{{ ticket.id }}</a></td>
                                <td>{{ ticket.subject }}</td>
                                <td>{{ ticket.requester_name }}</td>
                                <td>
                                    {% if ticket.status == 2 %}
                                    <span class="badge bg-primary">Open</span>
                                    {% elif ticket.status == 3 %}
                                    <span class="badge bg-warning">Pending</span>
                                    {% elif ticket.status == 4 %}
                                    <span class="badge bg-success">Resolved</span>
                                    {% elif ticket.status == 5 %}
                                    <span class="badge bg-secondary">Closed</span>
                                    {% else %}
                                    <span class="badge bg-info">Other</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if ticket.priority == 1 %}
                                    <span class="badge bg-success">Low</span>
                                    {% elif ticket.priority == 2 %}
                                    <span class="badge bg-info">Medium</span>
                                    {% elif ticket.priority == 3 %}
                                    <span class="badge bg-warning">High</span>
                                    {% elif ticket.priority == 4 %}
                                    <span class="badge bg-danger">Urgent</span>
                                    {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                    {% endif %}
                                </td>
                                <td>{{ ticket.group_name|default('None') }}</td>
                                <td>{{ ticket.agent_name|default('Unassigned') }}</td>
                                <td>{{ ticket.created_at|default('') }}</td>
                                <td>{{ ticket.updated_at|default('') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('ticket_detail', ticket_id=ticket.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-secondary fetch-history-btn" data-ticket-id="{{ ticket.id }}">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="10" class="text-center">No tickets found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if pagination and pagination.total_pages > 1 %}
                <nav aria-label="Ticket pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item {% if pagination.current_page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('tickets', page=pagination.current_page-1) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        {% for page in range(1, pagination.total_pages + 1) %}
                        <li class="page-item {% if page == pagination.current_page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('tickets', page=page) }}">{{ page }}</a>
                        </li>
                        {% endfor %}
                        
                        <li class="page-item {% if pagination.current_page == pagination.total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('tickets', page=pagination.current_page+1) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Fetch Tickets Modal -->
<div class="modal fade" id="fetchTicketsModal" tabindex="-1" aria-labelledby="fetchTicketsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fetchTicketsModalLabel">Fetch Tickets</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('fetch_tickets') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="updatedSince" class="form-label">Updated Since</label>
                        <input type="datetime-local" class="form-control" id="updatedSince" name="updated_since">
                        <div class="form-text">Leave blank to use the default time range (7 days)</div>
                    </div>
                    <div class="mb-3">
                        <label for="maxTickets" class="form-label">Maximum Tickets</label>
                        <input type="number" class="form-control" id="maxTickets" name="max_tickets" min="1" value="100">
                        <div class="form-text">Maximum number of tickets to fetch</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Fetch Tickets</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Fetch History Modal -->
<div class="modal fade" id="fetchHistoryModal" tabindex="-1" aria-labelledby="fetchHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fetchHistoryModalLabel">Fetch Ticket History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('fetch_history') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="history_type" id="specificTicket" value="specific" checked>
                            <label class="form-check-label" for="specificTicket">
                                Fetch history for a specific ticket
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="history_type" id="allTickets" value="all">
                            <label class="form-check-label" for="allTickets">
                                Fetch history for all tickets
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="ticketIdField">
                        <label for="ticketId" class="form-label">Ticket ID</label>
                        <input type="number" class="form-control" id="ticketId" name="ticket_id" min="1">
                    </div>
                    
                    <div class="mb-3" id="maxTicketsField" style="display: none;">
                        <label for="maxTicketsHistory" class="form-label">Maximum Tickets</label>
                        <input type="number" class="form-control" id="maxTicketsHistory" name="max_tickets" min="1" value="50">
                        <div class="form-text">Maximum number of tickets to process</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Fetch History</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle fields in the fetch history modal based on selection
        document.querySelectorAll('input[name="history_type"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                const ticketIdField = document.getElementById('ticketIdField');
                const maxTicketsField = document.getElementById('maxTicketsField');
                
                if (this.value === 'specific') {
                    ticketIdField.style.display = 'block';
                    maxTicketsField.style.display = 'none';
                } else {
                    ticketIdField.style.display = 'none';
                    maxTicketsField.style.display = 'block';
                }
            });
        });
        
        // Handle fetch history button clicks
        document.querySelectorAll('.fetch-history-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const ticketId = this.getAttribute('data-ticket-id');
                document.getElementById('ticketId').value = ticketId;
                document.getElementById('specificTicket').checked = true;
                document.getElementById('ticketIdField').style.display = 'block';
                document.getElementById('maxTicketsField').style.display = 'none';
                
                const modal = new bootstrap.Modal(document.getElementById('fetchHistoryModal'));
                modal.show();
            });
        });
        
        // Handle filter dropdown clicks
        document.querySelectorAll('[data-filter]').forEach(function(item) {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const filter = this.getAttribute('data-filter');
                
                // Set the appropriate filter values
                if (filter === 'all') {
                    document.getElementById('statusFilter').value = '';
                } else if (filter === 'open') {
                    document.getElementById('statusFilter').value = '2';
                } else if (filter === 'pending') {
                    document.getElementById('statusFilter').value = '3';
                } else if (filter === 'resolved') {
                    document.getElementById('statusFilter').value = '4';
                } else if (filter === 'closed') {
                    document.getElementById('statusFilter').value = '5';
                }
                
                // Submit the form
                document.getElementById('searchForm').submit();
            });
        });
    });
</script>
{% endblock %}
