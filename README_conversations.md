# FreshService Ticket Conversations Fetcher

A standalone Python script that reads an Excel file containing phishing tickets and fetches the conversation history for each ticket from FreshService. Results are stored in a single JSON file.

## Features

- Reads Excel files with ticket numbers
- Fetches complete conversation history for each ticket
- Includes ticket details along with conversations
- <PERSON>les errors gracefully and continues processing
- Saves all results to a single JSON file
- Limits processing to top 10 tickets for testing
- Supports various Excel column naming conventions

## Prerequisites

- Python 3.6 or higher
- FreshService API access
- Valid FreshService API key
- Excel file with ticket numbers

## Installation

1. Install required dependencies:
```bash
pip install -r requirements_conversations.txt
```

2. Set up your FreshService credentials (same as other scripts):
```bash
export FRESHSERVICE_DOMAIN="your-domain.freshservice.com"
export FRESHSERVICE_API_KEY="your_api_key_here"
```

Or use a .env file with these variables.

## Usage

```bash
python fetch_ticket_conversations.py <excel_file_path>
```

### Example:
```bash
python fetch_ticket_conversations.py phishing_tickets.xlsx
```

## Excel File Requirements

The Excel file should contain a column with ticket numbers. The script will automatically detect columns with these names (case insensitive):
- "ticket number"
- "ticket_number" 
- Any column containing "ticket", "id", or "number"

### Example Excel Structure:
| Ticket Number | Subject | Date | Status |
|---------------|---------|------|--------|
| 12345 | Phishing Email Report | 2023-01-15 | Closed |
| 12346 | Suspicious Link | 2023-01-16 | Resolved |

## Output

The script creates a JSON file named `ticket_conversations_YYYYMMDD_HHMMSS.json` containing:

```json
{
  "metadata": {
    "total_tickets": 10,
    "fetch_timestamp": "2023-01-15T10:30:00",
    "domain": "your-domain.freshservice.com"
  },
  "tickets": [
    {
      "ticket_id": 12345,
      "ticket_details": {
        "id": 12345,
        "subject": "Phishing Email Report",
        "status": 5,
        "priority": 2,
        "created_at": "2023-01-15T09:00:00Z",
        "updated_at": "2023-01-15T15:30:00Z"
      },
      "conversations": [
        {
          "id": 67890,
          "body": "User reported suspicious email...",
          "body_text": "User reported suspicious email...",
          "incoming": true,
          "private": false,
          "user_id": 123,
          "created_at": "2023-01-15T09:05:00Z",
          "updated_at": "2023-01-15T09:05:00Z"
        }
      ],
      "conversation_count": 5,
      "fetch_status": "success"
    }
  ]
}
```

## Error Handling

The script includes comprehensive error handling:
- Invalid or missing Excel files
- Missing ticket number columns
- API authentication errors
- Network connectivity issues
- Individual ticket fetch failures (continues with other tickets)

## Limitations

- Currently limited to top 10 tickets for testing
- Requires valid FreshService API credentials
- Excel file must be in .xlsx format
- Large conversation histories may take time to fetch

## Customization

To modify the number of tickets processed, change the `limit=10` parameter in the `read_excel_file()` function call in the `main()` function.

## Troubleshooting

### Common Issues

1. **"Could not find 'ticket number' column"**
   - Check that your Excel file has a column with ticket numbers
   - Ensure the column header contains "ticket", "number", or "id"

2. **"File not found"**
   - Verify the Excel file path is correct
   - Ensure the file exists and is accessible

3. **"No valid ticket numbers found"**
   - Check that the ticket number column contains valid numeric values
   - Ensure there are no empty rows at the top of your Excel file

4. **API errors**
   - Verify your FreshService credentials are correct
   - Check that your API key has permission to read tickets and conversations

## Output Files

- `ticket_conversations_YYYYMMDD_HHMMSS.json` - Main results file with all ticket data and conversations
