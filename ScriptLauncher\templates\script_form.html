{% extends "base.html" %}

{% block title %}Script Launcher - {{ script.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas {% if script.type == 'python' %}fa-python{% elif script.type == 'powershell' %}fa-terminal{% else %}fa-code{% endif %} me-2"></i>
        {{ script.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('index') }}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Scripts
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Script Parameters</h5>
            </div>
            <div class="card-body">
                <p class="card-text">{{ script.description }}</p>

                <form id="scriptForm" data-script-id="{{ script.id }}" enctype="multipart/form-data">
                    {% if script.parameters %}
                        {% for param in script.parameters %}
                            <div class="mb-3">
                                <label for="{{ param.name }}" class="form-label">{{ param.label }}</label>

                                {% if param.type == 'text' %}
                                    <input type="text" class="form-control" id="{{ param.name }}" name="{{ param.name }}"
                                           value="{{ param.default|default('') }}"
                                           {% if param.required %}required{% endif %}>

                                {% elif param.type == 'number' %}
                                    <input type="number" class="form-control" id="{{ param.name }}" name="{{ param.name }}"
                                           value="{{ param.default|default('') }}"
                                           {% if param.required %}required{% endif %}>

                                {% elif param.type == 'checkbox' %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="{{ param.name }}" name="{{ param.name }}"
                                               {% if param.default %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ param.name }}">
                                            {{ param.label }}
                                        </label>
                                    </div>

                                {% elif param.type == 'select' %}
                                    <select class="form-select" id="{{ param.name }}" name="{{ param.name }}"
                                            {% if param.required %}required{% endif %}>
                                        {% for option in param.options %}
                                            <option value="{{ option.value }}" {% if option.value == param.default %}selected{% endif %}>
                                                {{ option.label }}
                                            </option>
                                        {% endfor %}
                                    </select>

                                {% elif param.type == 'file' %}
                                    <input type="file" class="form-control" id="{{ param.name }}" name="{{ param.name }}"
                                           {% if param.required %}required{% endif %}>

                                {% endif %}

                                {% if param.help %}
                                    <div class="form-text">{{ param.help }}</div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> This script does not require any parameters.
                        </div>
                    {% endif %}

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-play me-1"></i> Run Script
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Script Information</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <strong>Type:</strong>
                        <span class="badge bg-secondary">{{ script.type|capitalize }}</span>
                    </li>
                    <li class="list-group-item">
                        <strong>Category:</strong>
                        <span class="badge bg-primary">{{ script.category }}</span>
                    </li>
                    <li class="list-group-item">
                        <strong>Path:</strong>
                        <code>{{ script.path }}</code>
                    </li>
                </ul>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Output</h5>
            </div>
            <div class="card-body">
                <div id="outputContainer" class="d-none">
                    <div class="alert alert-success mb-3" id="outputStatus">
                        <i class="fas fa-check-circle me-2"></i> <span id="outputMessage"></span>
                    </div>
                    <div class="mb-3">
                        <label for="scriptOutput" class="form-label">Script Output:</label>
                        <pre id="scriptOutput" class="form-control" style="height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>
                <div id="loadingContainer" class="d-none text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Running script, please wait...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#scriptForm').on('submit', function(e) {
            e.preventDefault();

            // Show loading indicator
            $('#loadingContainer').removeClass('d-none');
            $('#outputContainer').addClass('d-none');

            // Get script ID
            const scriptId = $(this).data('script-id');

            // Create FormData object for file uploads
            const formData = new FormData(this);

            // Submit form data
            $.ajax({
                url: '/run_script/' + scriptId,
                type: 'POST',
                data: formData,
                processData: false,  // Don't process the data
                contentType: false,  // Don't set content type
                success: function(response) {
                    // Hide loading indicator
                    $('#loadingContainer').addClass('d-none');

                    // Show output container
                    $('#outputContainer').removeClass('d-none');

                    if (response.success) {
                        // Show success message
                        $('#outputStatus').removeClass('alert-danger').addClass('alert-success');
                        $('#outputMessage').text(response.message);

                        // Show output
                        $('#scriptOutput').text(response.output);
                    } else {
                        // Show error message
                        $('#outputStatus').removeClass('alert-success').addClass('alert-danger');
                        $('#outputMessage').text(response.message);

                        // Show error output if available
                        $('#scriptOutput').text(response.output || 'No output available');
                    }
                },
                error: function(xhr, status, error) {
                    // Hide loading indicator
                    $('#loadingContainer').addClass('d-none');

                    // Show output container with error
                    $('#outputContainer').removeClass('d-none');
                    $('#outputStatus').removeClass('alert-success').addClass('alert-danger');
                    $('#outputMessage').text('Error: ' + error);
                    $('#scriptOutput').text('Failed to run script. Please try again.');
                }
            });
        });
    });
</script>
{% endblock %}
