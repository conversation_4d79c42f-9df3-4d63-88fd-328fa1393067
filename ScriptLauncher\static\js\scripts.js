/**
 * Script Launcher JavaScript
 */

$(document).ready(function() {
    // Enable tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Enable popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Search functionality
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        const searchTerm = $('#searchInput').val().toLowerCase();
        
        $('.script-card').each(function() {
            const scriptName = $(this).find('.card-title').text().toLowerCase();
            const scriptDesc = $(this).find('.card-text').text().toLowerCase();
            const scriptCategory = $(this).find('.badge').text().toLowerCase();
            
            if (scriptName.includes(searchTerm) || 
                scriptDesc.includes(searchTerm) || 
                scriptCategory.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
    
    // Clear search
    $('#clearSearch').on('click', function() {
        $('#searchInput').val('');
        $('.script-card').show();
    });
    
    // Category filter
    $('.category-filter').on('click', function(e) {
        e.preventDefault();
        const category = $(this).data('category');
        
        if (category === 'all') {
            $('.script-card').show();
        } else {
            $('.script-card').each(function() {
                const scriptCategory = $(this).find('.badge').text();
                
                if (scriptCategory === category) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });
});
