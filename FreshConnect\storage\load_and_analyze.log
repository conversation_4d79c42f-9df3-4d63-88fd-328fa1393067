2025-04-18 13:19:49,139 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:19:49
2025-04-18 13:19:49,139 - __main__ - INFO - Looking back 1 days for tickets and 10 minutes for agent activity
2025-04-18 13:19:49,140 - __main__ - INFO - Fetching tickets updated since 2025-04-17T17:19:49.140085
2025-04-18 13:19:49,141 - freshconnect.api.freshservice - ERROR - No API key provided. Set FRESHSERVICE_API_KEY environment variable.
2025-04-18 13:19:49,141 - __main__ - ERROR - Error in main process: API key is required for FreshService API access
Traceback (most recent call last):
  File "C:\Scripts\FreshConnect\load_and_analyze.py", line 108, in <module>
    main()
  File "C:\Scripts\FreshConnect\load_and_analyze.py", line 64, in main
    ticket_count = fetch_tickets(updated_since=updated_since)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\processors\tickets.py", line 191, in fetch_tickets
    processor = TicketProcessor()
                ^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\processors\tickets.py", line 26, in __init__
    self.api_client = api_client or FreshServiceClient()
                                    ^^^^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\api\freshservice.py", line 35, in __init__
    raise ValueError("API key is required for FreshService API access")
ValueError: API key is required for FreshService API access
2025-04-18 13:20:50,518 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:20:50
2025-04-18 13:20:50,518 - __main__ - INFO - Looking back 1 days for tickets and 10 minutes for agent activity
2025-04-18 13:20:50,519 - __main__ - INFO - Fetching tickets updated since 2025-04-17T13:20:50-04:00
2025-04-18 13:20:50,519 - freshconnect.api.freshservice - ERROR - No API key provided. Set FRESHSERVICE_API_KEY environment variable.
2025-04-18 13:20:50,519 - __main__ - ERROR - Error in main process: API key is required for FreshService API access
Traceback (most recent call last):
  File "C:\Scripts\FreshConnect\load_and_analyze.py", line 119, in <module>
    main()
  File "C:\Scripts\FreshConnect\load_and_analyze.py", line 75, in main
    ticket_count = fetch_tickets(updated_since=updated_since)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\processors\tickets.py", line 191, in fetch_tickets
    processor = TicketProcessor()
                ^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\processors\tickets.py", line 26, in __init__
    self.api_client = api_client or FreshServiceClient()
                                    ^^^^^^^^^^^^^^^^^^^^
  File "C:\Scripts\FreshConnect\freshconnect\api\freshservice.py", line 35, in __init__
    raise ValueError("API key is required for FreshService API access")
ValueError: API key is required for FreshService API access
2025-04-18 13:21:44,850 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:21:44
2025-04-18 13:21:44,851 - __main__ - INFO - Looking back 1 days for tickets and 10 minutes for agent activity
2025-04-18 13:21:44,851 - __main__ - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:44,862 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:21:44,864 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:21:44,865 - freshconnect.processors.tickets - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:44,865 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:45,121 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 1)
2025-04-18 13:21:45,621 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:45,819 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 2)
2025-04-18 13:21:46,321 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:46,505 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 3)
2025-04-18 13:21:47,007 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:47,212 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 4)
2025-04-18 13:21:47,713 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:47,903 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 5)
2025-04-18 13:21:48,404 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:48,591 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 6)
2025-04-18 13:21:49,093 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:49,275 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 7)
2025-04-18 13:21:49,776 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:49,976 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 8)
2025-04-18 13:21:50,477 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:50,689 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 9)
2025-04-18 13:21:51,191 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:51,388 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 10)
2025-04-18 13:21:51,888 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:21:44-04:00
2025-04-18 13:21:52,068 - freshconnect.processors.tickets - INFO - Fetched 25 tickets (page 11)
2025-04-18 13:21:55,021 - freshconnect.data.backends.sqlite_backend - INFO - Cleared table 'tickets' and inserted 325 items
2025-04-18 13:21:55,021 - freshconnect.processors.tickets - INFO - Stored 325 tickets in the database
2025-04-18 13:21:55,022 - __main__ - INFO - Fetched and stored 325 tickets
2025-04-18 13:21:55,023 - __main__ - INFO - Fetching history for all tickets
2025-04-18 13:21:55,023 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:21:55,024 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:21:55,024 - freshconnect.processors.tickets - INFO - Fetching history for all tickets
2025-04-18 13:21:55,037 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128859
2025-04-18 13:21:55,038 - freshconnect.api.freshservice - INFO - Fetching ticket 128859
2025-04-18 13:21:55,253 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128859
2025-04-18 13:21:55,392 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:55,392 - freshconnect.processors.tickets - INFO - Stored history for ticket 128859
2025-04-18 13:21:55,894 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128857
2025-04-18 13:21:55,894 - freshconnect.api.freshservice - INFO - Fetching ticket 128857
2025-04-18 13:21:56,041 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128857
2025-04-18 13:21:56,212 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:56,212 - freshconnect.processors.tickets - INFO - Stored history for ticket 128857
2025-04-18 13:21:56,713 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128856
2025-04-18 13:21:56,713 - freshconnect.api.freshservice - INFO - Fetching ticket 128856
2025-04-18 13:21:56,847 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128856
2025-04-18 13:21:56,982 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:56,983 - freshconnect.processors.tickets - INFO - Stored history for ticket 128856
2025-04-18 13:21:57,484 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128855
2025-04-18 13:21:57,484 - freshconnect.api.freshservice - INFO - Fetching ticket 128855
2025-04-18 13:21:57,708 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128855
2025-04-18 13:21:57,843 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:57,844 - freshconnect.processors.tickets - INFO - Stored history for ticket 128855
2025-04-18 13:21:58,345 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128854
2025-04-18 13:21:58,345 - freshconnect.api.freshservice - INFO - Fetching ticket 128854
2025-04-18 13:21:58,522 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128854
2025-04-18 13:21:58,661 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:58,661 - freshconnect.processors.tickets - INFO - Stored history for ticket 128854
2025-04-18 13:21:59,162 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128853
2025-04-18 13:21:59,162 - freshconnect.api.freshservice - INFO - Fetching ticket 128853
2025-04-18 13:21:59,311 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128853
2025-04-18 13:21:59,473 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:21:59,473 - freshconnect.processors.tickets - INFO - Stored history for ticket 128853
2025-04-18 13:21:59,975 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128852
2025-04-18 13:21:59,975 - freshconnect.api.freshservice - INFO - Fetching ticket 128852
2025-04-18 13:22:00,148 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128852
2025-04-18 13:22:00,385 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:00,386 - freshconnect.processors.tickets - INFO - Stored history for ticket 128852
2025-04-18 13:22:00,887 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128851
2025-04-18 13:22:00,887 - freshconnect.api.freshservice - INFO - Fetching ticket 128851
2025-04-18 13:22:01,055 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128851
2025-04-18 13:22:01,234 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:01,234 - freshconnect.processors.tickets - INFO - Stored history for ticket 128851
2025-04-18 13:22:01,735 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128850
2025-04-18 13:22:01,735 - freshconnect.api.freshservice - INFO - Fetching ticket 128850
2025-04-18 13:22:01,888 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128850
2025-04-18 13:22:02,036 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:02,036 - freshconnect.processors.tickets - INFO - Stored history for ticket 128850
2025-04-18 13:22:02,537 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128849
2025-04-18 13:22:02,537 - freshconnect.api.freshservice - INFO - Fetching ticket 128849
2025-04-18 13:22:02,706 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128849
2025-04-18 13:22:02,859 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:02,860 - freshconnect.processors.tickets - INFO - Stored history for ticket 128849
2025-04-18 13:22:03,361 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128848
2025-04-18 13:22:03,361 - freshconnect.api.freshservice - INFO - Fetching ticket 128848
2025-04-18 13:22:03,543 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128848
2025-04-18 13:22:03,691 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:03,692 - freshconnect.processors.tickets - INFO - Stored history for ticket 128848
2025-04-18 13:22:04,193 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128847
2025-04-18 13:22:04,193 - freshconnect.api.freshservice - INFO - Fetching ticket 128847
2025-04-18 13:22:04,388 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128847
2025-04-18 13:22:04,522 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:04,522 - freshconnect.processors.tickets - INFO - Stored history for ticket 128847
2025-04-18 13:22:05,024 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128846
2025-04-18 13:22:05,024 - freshconnect.api.freshservice - INFO - Fetching ticket 128846
2025-04-18 13:22:05,165 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128846
2025-04-18 13:22:05,316 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:05,317 - freshconnect.processors.tickets - INFO - Stored history for ticket 128846
2025-04-18 13:22:05,818 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128845
2025-04-18 13:22:05,818 - freshconnect.api.freshservice - INFO - Fetching ticket 128845
2025-04-18 13:22:05,993 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128845
2025-04-18 13:22:06,173 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:06,173 - freshconnect.processors.tickets - INFO - Stored history for ticket 128845
2025-04-18 13:22:06,674 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128843
2025-04-18 13:22:06,674 - freshconnect.api.freshservice - INFO - Fetching ticket 128843
2025-04-18 13:22:06,836 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128843
2025-04-18 13:22:06,967 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:06,967 - freshconnect.processors.tickets - INFO - Stored history for ticket 128843
2025-04-18 13:22:07,468 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128842
2025-04-18 13:22:07,468 - freshconnect.api.freshservice - INFO - Fetching ticket 128842
2025-04-18 13:22:07,610 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128842
2025-04-18 13:22:07,739 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:07,739 - freshconnect.processors.tickets - INFO - Stored history for ticket 128842
2025-04-18 13:22:08,240 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128841
2025-04-18 13:22:08,241 - freshconnect.api.freshservice - INFO - Fetching ticket 128841
2025-04-18 13:22:08,374 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128841
2025-04-18 13:22:08,515 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:08,515 - freshconnect.processors.tickets - INFO - Stored history for ticket 128841
2025-04-18 13:22:09,016 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128840
2025-04-18 13:22:09,016 - freshconnect.api.freshservice - INFO - Fetching ticket 128840
2025-04-18 13:22:09,151 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128840
2025-04-18 13:22:09,326 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:09,326 - freshconnect.processors.tickets - INFO - Stored history for ticket 128840
2025-04-18 13:22:09,827 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128837
2025-04-18 13:22:09,827 - freshconnect.api.freshservice - INFO - Fetching ticket 128837
2025-04-18 13:22:09,977 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128837
2025-04-18 13:22:10,127 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:10,127 - freshconnect.processors.tickets - INFO - Stored history for ticket 128837
2025-04-18 13:22:10,628 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128838
2025-04-18 13:22:10,628 - freshconnect.api.freshservice - INFO - Fetching ticket 128838
2025-04-18 13:22:10,791 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128838
2025-04-18 13:22:10,934 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:10,934 - freshconnect.processors.tickets - INFO - Stored history for ticket 128838
2025-04-18 13:22:11,435 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128839
2025-04-18 13:22:11,435 - freshconnect.api.freshservice - INFO - Fetching ticket 128839
2025-04-18 13:22:11,562 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128839
2025-04-18 13:22:11,732 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:11,732 - freshconnect.processors.tickets - INFO - Stored history for ticket 128839
2025-04-18 13:22:12,232 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128835
2025-04-18 13:22:12,232 - freshconnect.api.freshservice - INFO - Fetching ticket 128835
2025-04-18 13:22:12,415 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128835
2025-04-18 13:22:12,552 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:12,553 - freshconnect.processors.tickets - INFO - Stored history for ticket 128835
2025-04-18 13:22:13,053 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128836
2025-04-18 13:22:13,053 - freshconnect.api.freshservice - INFO - Fetching ticket 128836
2025-04-18 13:22:13,224 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128836
2025-04-18 13:22:13,385 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:13,385 - freshconnect.processors.tickets - INFO - Stored history for ticket 128836
2025-04-18 13:22:13,886 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128834
2025-04-18 13:22:13,886 - freshconnect.api.freshservice - INFO - Fetching ticket 128834
2025-04-18 13:22:14,039 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128834
2025-04-18 13:22:14,265 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:14,265 - freshconnect.processors.tickets - INFO - Stored history for ticket 128834
2025-04-18 13:22:14,766 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128833
2025-04-18 13:22:14,766 - freshconnect.api.freshservice - INFO - Fetching ticket 128833
2025-04-18 13:22:14,904 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128833
2025-04-18 13:22:15,084 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:15,084 - freshconnect.processors.tickets - INFO - Stored history for ticket 128833
2025-04-18 13:22:15,585 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128832
2025-04-18 13:22:15,585 - freshconnect.api.freshservice - INFO - Fetching ticket 128832
2025-04-18 13:22:15,727 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128832
2025-04-18 13:22:15,860 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:15,861 - freshconnect.processors.tickets - INFO - Stored history for ticket 128832
2025-04-18 13:22:16,361 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128831
2025-04-18 13:22:16,361 - freshconnect.api.freshservice - INFO - Fetching ticket 128831
2025-04-18 13:22:16,531 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128831
2025-04-18 13:22:16,682 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:16,682 - freshconnect.processors.tickets - INFO - Stored history for ticket 128831
2025-04-18 13:22:17,183 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128829
2025-04-18 13:22:17,183 - freshconnect.api.freshservice - INFO - Fetching ticket 128829
2025-04-18 13:22:17,341 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128829
2025-04-18 13:22:17,502 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:17,502 - freshconnect.processors.tickets - INFO - Stored history for ticket 128829
2025-04-18 13:22:18,003 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128828
2025-04-18 13:22:18,003 - freshconnect.api.freshservice - INFO - Fetching ticket 128828
2025-04-18 13:22:18,186 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128828
2025-04-18 13:22:18,322 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:18,322 - freshconnect.processors.tickets - INFO - Stored history for ticket 128828
2025-04-18 13:22:18,822 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128827
2025-04-18 13:22:18,822 - freshconnect.api.freshservice - INFO - Fetching ticket 128827
2025-04-18 13:22:18,978 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128827
2025-04-18 13:22:19,134 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:19,134 - freshconnect.processors.tickets - INFO - Stored history for ticket 128827
2025-04-18 13:22:19,635 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128825
2025-04-18 13:22:19,635 - freshconnect.api.freshservice - INFO - Fetching ticket 128825
2025-04-18 13:22:19,770 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128825
2025-04-18 13:22:19,900 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:19,901 - freshconnect.processors.tickets - INFO - Stored history for ticket 128825
2025-04-18 13:22:20,401 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128823
2025-04-18 13:22:20,401 - freshconnect.api.freshservice - INFO - Fetching ticket 128823
2025-04-18 13:22:20,557 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128823
2025-04-18 13:22:20,695 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:20,696 - freshconnect.processors.tickets - INFO - Stored history for ticket 128823
2025-04-18 13:22:21,196 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128822
2025-04-18 13:22:21,196 - freshconnect.api.freshservice - INFO - Fetching ticket 128822
2025-04-18 13:22:21,370 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128822
2025-04-18 13:22:21,490 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:21,491 - freshconnect.processors.tickets - INFO - Stored history for ticket 128822
2025-04-18 13:22:21,992 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128821
2025-04-18 13:22:21,992 - freshconnect.api.freshservice - INFO - Fetching ticket 128821
2025-04-18 13:22:22,145 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128821
2025-04-18 13:22:22,293 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:22,293 - freshconnect.processors.tickets - INFO - Stored history for ticket 128821
2025-04-18 13:22:22,794 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128820
2025-04-18 13:22:22,794 - freshconnect.api.freshservice - INFO - Fetching ticket 128820
2025-04-18 13:22:22,983 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128820
2025-04-18 13:22:23,139 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:23,139 - freshconnect.processors.tickets - INFO - Stored history for ticket 128820
2025-04-18 13:22:23,640 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128819
2025-04-18 13:22:23,640 - freshconnect.api.freshservice - INFO - Fetching ticket 128819
2025-04-18 13:22:23,870 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128819
2025-04-18 13:22:24,018 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:24,019 - freshconnect.processors.tickets - INFO - Stored history for ticket 128819
2025-04-18 13:22:24,520 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128818
2025-04-18 13:22:24,520 - freshconnect.api.freshservice - INFO - Fetching ticket 128818
2025-04-18 13:22:24,660 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128818
2025-04-18 13:22:24,872 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:24,872 - freshconnect.processors.tickets - INFO - Stored history for ticket 128818
2025-04-18 13:22:25,373 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128817
2025-04-18 13:22:25,373 - freshconnect.api.freshservice - INFO - Fetching ticket 128817
2025-04-18 13:22:25,539 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128817
2025-04-18 13:22:25,697 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:25,698 - freshconnect.processors.tickets - INFO - Stored history for ticket 128817
2025-04-18 13:22:26,199 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128816
2025-04-18 13:22:26,199 - freshconnect.api.freshservice - INFO - Fetching ticket 128816
2025-04-18 13:22:26,337 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128816
2025-04-18 13:22:26,498 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:26,498 - freshconnect.processors.tickets - INFO - Stored history for ticket 128816
2025-04-18 13:22:26,999 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128815
2025-04-18 13:22:26,999 - freshconnect.api.freshservice - INFO - Fetching ticket 128815
2025-04-18 13:22:27,177 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128815
2025-04-18 13:22:27,314 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:27,314 - freshconnect.processors.tickets - INFO - Stored history for ticket 128815
2025-04-18 13:22:27,815 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128814
2025-04-18 13:22:27,816 - freshconnect.api.freshservice - INFO - Fetching ticket 128814
2025-04-18 13:22:27,975 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128814
2025-04-18 13:22:28,129 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:28,129 - freshconnect.processors.tickets - INFO - Stored history for ticket 128814
2025-04-18 13:22:28,630 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128813
2025-04-18 13:22:28,631 - freshconnect.api.freshservice - INFO - Fetching ticket 128813
2025-04-18 13:22:28,762 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128813
2025-04-18 13:22:28,923 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:28,924 - freshconnect.processors.tickets - INFO - Stored history for ticket 128813
2025-04-18 13:22:29,425 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128812
2025-04-18 13:22:29,425 - freshconnect.api.freshservice - INFO - Fetching ticket 128812
2025-04-18 13:22:29,584 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128812
2025-04-18 13:22:29,747 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:29,748 - freshconnect.processors.tickets - INFO - Stored history for ticket 128812
2025-04-18 13:22:30,249 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128811
2025-04-18 13:22:30,249 - freshconnect.api.freshservice - INFO - Fetching ticket 128811
2025-04-18 13:22:30,398 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128811
2025-04-18 13:22:30,526 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:30,526 - freshconnect.processors.tickets - INFO - Stored history for ticket 128811
2025-04-18 13:22:31,027 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128810
2025-04-18 13:22:31,027 - freshconnect.api.freshservice - INFO - Fetching ticket 128810
2025-04-18 13:22:31,179 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128810
2025-04-18 13:22:31,336 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:31,336 - freshconnect.processors.tickets - INFO - Stored history for ticket 128810
2025-04-18 13:22:31,837 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128809
2025-04-18 13:22:31,837 - freshconnect.api.freshservice - INFO - Fetching ticket 128809
2025-04-18 13:22:31,991 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128809
2025-04-18 13:22:32,145 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:32,145 - freshconnect.processors.tickets - INFO - Stored history for ticket 128809
2025-04-18 13:22:32,646 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128797
2025-04-18 13:22:32,646 - freshconnect.api.freshservice - INFO - Fetching ticket 128797
2025-04-18 13:22:32,887 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128797
2025-04-18 13:22:33,054 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:33,055 - freshconnect.processors.tickets - INFO - Stored history for ticket 128797
2025-04-18 13:22:33,556 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128796
2025-04-18 13:22:33,556 - freshconnect.api.freshservice - INFO - Fetching ticket 128796
2025-04-18 13:22:33,712 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128796
2025-04-18 13:22:33,860 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:33,860 - freshconnect.processors.tickets - INFO - Stored history for ticket 128796
2025-04-18 13:22:34,360 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128795
2025-04-18 13:22:34,360 - freshconnect.api.freshservice - INFO - Fetching ticket 128795
2025-04-18 13:22:34,508 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128795
2025-04-18 13:22:34,654 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:34,654 - freshconnect.processors.tickets - INFO - Stored history for ticket 128795
2025-04-18 13:22:35,155 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128793
2025-04-18 13:22:35,155 - freshconnect.api.freshservice - INFO - Fetching ticket 128793
2025-04-18 13:22:35,327 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128793
2025-04-18 13:22:35,463 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:35,464 - freshconnect.processors.tickets - INFO - Stored history for ticket 128793
2025-04-18 13:22:35,965 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128792
2025-04-18 13:22:35,965 - freshconnect.api.freshservice - INFO - Fetching ticket 128792
2025-04-18 13:22:36,109 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128792
2025-04-18 13:22:36,244 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:36,244 - freshconnect.processors.tickets - INFO - Stored history for ticket 128792
2025-04-18 13:22:36,745 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128791
2025-04-18 13:22:36,745 - freshconnect.api.freshservice - INFO - Fetching ticket 128791
2025-04-18 13:22:36,886 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128791
2025-04-18 13:22:37,023 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:37,024 - freshconnect.processors.tickets - INFO - Stored history for ticket 128791
2025-04-18 13:22:37,525 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128790
2025-04-18 13:22:37,526 - freshconnect.api.freshservice - INFO - Fetching ticket 128790
2025-04-18 13:22:37,676 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128790
2025-04-18 13:22:37,827 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:37,827 - freshconnect.processors.tickets - INFO - Stored history for ticket 128790
2025-04-18 13:22:38,329 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128789
2025-04-18 13:22:38,329 - freshconnect.api.freshservice - INFO - Fetching ticket 128789
2025-04-18 13:22:38,512 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128789
2025-04-18 13:22:38,648 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:38,648 - freshconnect.processors.tickets - INFO - Stored history for ticket 128789
2025-04-18 13:22:39,149 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128787
2025-04-18 13:22:39,149 - freshconnect.api.freshservice - INFO - Fetching ticket 128787
2025-04-18 13:22:39,299 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128787
2025-04-18 13:22:39,474 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:39,474 - freshconnect.processors.tickets - INFO - Stored history for ticket 128787
2025-04-18 13:22:39,975 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128785
2025-04-18 13:22:39,975 - freshconnect.api.freshservice - INFO - Fetching ticket 128785
2025-04-18 13:22:40,130 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128785
2025-04-18 13:22:40,283 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:40,283 - freshconnect.processors.tickets - INFO - Stored history for ticket 128785
2025-04-18 13:22:40,784 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128784
2025-04-18 13:22:40,784 - freshconnect.api.freshservice - INFO - Fetching ticket 128784
2025-04-18 13:22:40,924 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128784
2025-04-18 13:22:41,075 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:41,076 - freshconnect.processors.tickets - INFO - Stored history for ticket 128784
2025-04-18 13:22:41,576 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128783
2025-04-18 13:22:41,577 - freshconnect.api.freshservice - INFO - Fetching ticket 128783
2025-04-18 13:22:41,725 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128783
2025-04-18 13:22:41,907 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:41,908 - freshconnect.processors.tickets - INFO - Stored history for ticket 128783
2025-04-18 13:22:42,408 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128781
2025-04-18 13:22:42,408 - freshconnect.api.freshservice - INFO - Fetching ticket 128781
2025-04-18 13:22:42,609 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128781
2025-04-18 13:22:42,790 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:42,791 - freshconnect.processors.tickets - INFO - Stored history for ticket 128781
2025-04-18 13:22:43,292 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128780
2025-04-18 13:22:43,292 - freshconnect.api.freshservice - INFO - Fetching ticket 128780
2025-04-18 13:22:43,415 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128780
2025-04-18 13:22:43,576 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:43,576 - freshconnect.processors.tickets - INFO - Stored history for ticket 128780
2025-04-18 13:22:44,077 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128779
2025-04-18 13:22:44,077 - freshconnect.api.freshservice - INFO - Fetching ticket 128779
2025-04-18 13:22:44,229 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128779
2025-04-18 13:22:44,371 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:44,372 - freshconnect.processors.tickets - INFO - Stored history for ticket 128779
2025-04-18 13:22:44,873 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128778
2025-04-18 13:22:44,873 - freshconnect.api.freshservice - INFO - Fetching ticket 128778
2025-04-18 13:22:45,027 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128778
2025-04-18 13:22:45,233 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:45,233 - freshconnect.processors.tickets - INFO - Stored history for ticket 128778
2025-04-18 13:22:45,734 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128777
2025-04-18 13:22:45,734 - freshconnect.api.freshservice - INFO - Fetching ticket 128777
2025-04-18 13:22:45,889 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128777
2025-04-18 13:22:46,036 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:46,036 - freshconnect.processors.tickets - INFO - Stored history for ticket 128777
2025-04-18 13:22:46,536 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128776
2025-04-18 13:22:46,536 - freshconnect.api.freshservice - INFO - Fetching ticket 128776
2025-04-18 13:22:46,785 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128776
2025-04-18 13:22:46,912 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:46,913 - freshconnect.processors.tickets - INFO - Stored history for ticket 128776
2025-04-18 13:22:47,414 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128775
2025-04-18 13:22:47,414 - freshconnect.api.freshservice - INFO - Fetching ticket 128775
2025-04-18 13:22:47,578 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128775
2025-04-18 13:22:47,734 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:47,734 - freshconnect.processors.tickets - INFO - Stored history for ticket 128775
2025-04-18 13:22:48,236 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128774
2025-04-18 13:22:48,236 - freshconnect.api.freshservice - INFO - Fetching ticket 128774
2025-04-18 13:22:48,410 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128774
2025-04-18 13:22:48,541 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:48,541 - freshconnect.processors.tickets - INFO - Stored history for ticket 128774
2025-04-18 13:22:49,042 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128773
2025-04-18 13:22:49,042 - freshconnect.api.freshservice - INFO - Fetching ticket 128773
2025-04-18 13:22:49,211 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128773
2025-04-18 13:22:49,344 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:22:49,345 - freshconnect.processors.tickets - INFO - Stored history for ticket 128773
2025-04-18 13:22:49,668 - __main__ - INFO - Process interrupted by user
2025-04-18 13:24:19,594 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:24:19
2025-04-18 13:24:19,595 - __main__ - INFO - Looking back 1 days for tickets and 10 minutes for agent activity
2025-04-18 13:24:19,596 - __main__ - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:19,610 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:24:19,611 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:24:19,611 - freshconnect.processors.tickets - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:19,611 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:19,866 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 1)
2025-04-18 13:24:20,367 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:20,539 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 2)
2025-04-18 13:24:21,040 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:21,259 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 3)
2025-04-18 13:24:21,760 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:21,934 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 4)
2025-04-18 13:24:22,435 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:22,642 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 5)
2025-04-18 13:24:23,143 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:23,356 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 6)
2025-04-18 13:24:23,858 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:24,052 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 7)
2025-04-18 13:24:24,553 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:24,735 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 8)
2025-04-18 13:24:25,237 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:25,427 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 9)
2025-04-18 13:24:25,928 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:26,131 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 10)
2025-04-18 13:24:26,632 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:24:19-04:00
2025-04-18 13:24:26,808 - freshconnect.processors.tickets - INFO - Fetched 23 tickets (page 11)
2025-04-18 13:24:30,100 - freshconnect.data.backends.sqlite_backend - INFO - Cleared table 'tickets' and inserted 323 items
2025-04-18 13:24:30,100 - freshconnect.processors.tickets - INFO - Stored 323 tickets in the database
2025-04-18 13:24:30,101 - __main__ - INFO - Fetched and stored 323 tickets
2025-04-18 13:24:30,103 - __main__ - INFO - Fetching history for up to 5 tickets
2025-04-18 13:24:30,104 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:24:30,105 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:24:30,105 - freshconnect.processors.tickets - INFO - Fetching history for all tickets
2025-04-18 13:24:30,122 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128859
2025-04-18 13:24:30,122 - freshconnect.api.freshservice - INFO - Fetching ticket 128859
2025-04-18 13:24:30,353 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128859
2025-04-18 13:24:30,493 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:30,494 - freshconnect.processors.tickets - INFO - Updated history for ticket 128859
2025-04-18 13:24:30,995 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128857
2025-04-18 13:24:30,995 - freshconnect.api.freshservice - INFO - Fetching ticket 128857
2025-04-18 13:24:31,170 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128857
2025-04-18 13:24:31,309 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:31,310 - freshconnect.processors.tickets - INFO - Updated history for ticket 128857
2025-04-18 13:24:31,811 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128856
2025-04-18 13:24:31,811 - freshconnect.api.freshservice - INFO - Fetching ticket 128856
2025-04-18 13:24:32,003 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128856
2025-04-18 13:24:32,156 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:32,157 - freshconnect.processors.tickets - INFO - Updated history for ticket 128856
2025-04-18 13:24:32,658 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128855
2025-04-18 13:24:32,658 - freshconnect.api.freshservice - INFO - Fetching ticket 128855
2025-04-18 13:24:32,810 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128855
2025-04-18 13:24:32,942 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:32,943 - freshconnect.processors.tickets - INFO - Updated history for ticket 128855
2025-04-18 13:24:33,445 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128854
2025-04-18 13:24:33,445 - freshconnect.api.freshservice - INFO - Fetching ticket 128854
2025-04-18 13:24:33,583 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128854
2025-04-18 13:24:33,748 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:33,749 - freshconnect.processors.tickets - INFO - Updated history for ticket 128854
2025-04-18 13:24:34,250 - freshconnect.processors.tickets - INFO - Processed history for 5 tickets
2025-04-18 13:24:34,250 - __main__ - INFO - Processed history for 5 tickets
2025-04-18 13:24:34,251 - __main__ - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:24:34,251 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:24:34,254 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:24:34,254 - freshconnect.processors.agents - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:24:34,292 - freshconnect.data.backends.sqlite_backend - INFO - Created table 'recent_agent_activity'
2025-04-18 13:24:34,301 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'recent_agent_activity'
2025-04-18 13:24:34,301 - freshconnect.processors.agents - INFO - Stored recent agent activity in database: 0 actions by 0 agents
2025-04-18 13:24:34,304 - __main__ - INFO - Analysis completed at 2025-04-18 13:24:34
2025-04-18 13:24:50,341 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:24:50
2025-04-18 13:24:50,341 - __main__ - INFO - Looking back 7 days for tickets and 10 minutes for agent activity
2025-04-18 13:24:50,341 - __main__ - INFO - Skipping ticket fetch as requested
2025-04-18 13:24:50,342 - __main__ - INFO - Fetching history for up to 20 tickets
2025-04-18 13:24:50,353 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:24:50,355 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:24:50,356 - freshconnect.processors.tickets - INFO - Fetching history for all tickets
2025-04-18 13:24:50,371 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128859
2025-04-18 13:24:50,372 - freshconnect.api.freshservice - INFO - Fetching ticket 128859
2025-04-18 13:24:50,598 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128859
2025-04-18 13:24:50,742 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:50,743 - freshconnect.processors.tickets - INFO - Updated history for ticket 128859
2025-04-18 13:24:51,243 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128857
2025-04-18 13:24:51,243 - freshconnect.api.freshservice - INFO - Fetching ticket 128857
2025-04-18 13:24:51,418 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128857
2025-04-18 13:24:51,552 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:51,553 - freshconnect.processors.tickets - INFO - Updated history for ticket 128857
2025-04-18 13:24:52,054 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128856
2025-04-18 13:24:52,054 - freshconnect.api.freshservice - INFO - Fetching ticket 128856
2025-04-18 13:24:52,202 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128856
2025-04-18 13:24:52,323 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:52,323 - freshconnect.processors.tickets - INFO - Updated history for ticket 128856
2025-04-18 13:24:52,823 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128855
2025-04-18 13:24:52,823 - freshconnect.api.freshservice - INFO - Fetching ticket 128855
2025-04-18 13:24:52,966 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128855
2025-04-18 13:24:53,141 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:53,141 - freshconnect.processors.tickets - INFO - Updated history for ticket 128855
2025-04-18 13:24:53,642 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128854
2025-04-18 13:24:53,642 - freshconnect.api.freshservice - INFO - Fetching ticket 128854
2025-04-18 13:24:53,770 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128854
2025-04-18 13:24:53,894 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:53,895 - freshconnect.processors.tickets - INFO - Updated history for ticket 128854
2025-04-18 13:24:54,396 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128853
2025-04-18 13:24:54,396 - freshconnect.api.freshservice - INFO - Fetching ticket 128853
2025-04-18 13:24:54,549 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128853
2025-04-18 13:24:54,660 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:54,660 - freshconnect.processors.tickets - INFO - Updated history for ticket 128853
2025-04-18 13:24:55,162 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128852
2025-04-18 13:24:55,163 - freshconnect.api.freshservice - INFO - Fetching ticket 128852
2025-04-18 13:24:55,309 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128852
2025-04-18 13:24:55,452 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:55,453 - freshconnect.processors.tickets - INFO - Updated history for ticket 128852
2025-04-18 13:24:55,954 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128851
2025-04-18 13:24:55,955 - freshconnect.api.freshservice - INFO - Fetching ticket 128851
2025-04-18 13:24:56,088 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128851
2025-04-18 13:24:56,202 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:56,203 - freshconnect.processors.tickets - INFO - Updated history for ticket 128851
2025-04-18 13:24:56,704 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128850
2025-04-18 13:24:56,704 - freshconnect.api.freshservice - INFO - Fetching ticket 128850
2025-04-18 13:24:56,841 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128850
2025-04-18 13:24:56,985 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:56,986 - freshconnect.processors.tickets - INFO - Updated history for ticket 128850
2025-04-18 13:24:57,487 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128849
2025-04-18 13:24:57,487 - freshconnect.api.freshservice - INFO - Fetching ticket 128849
2025-04-18 13:24:57,625 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128849
2025-04-18 13:24:57,773 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:57,773 - freshconnect.processors.tickets - INFO - Updated history for ticket 128849
2025-04-18 13:24:58,274 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128848
2025-04-18 13:24:58,275 - freshconnect.api.freshservice - INFO - Fetching ticket 128848
2025-04-18 13:24:58,503 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128848
2025-04-18 13:24:58,655 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:58,656 - freshconnect.processors.tickets - INFO - Updated history for ticket 128848
2025-04-18 13:24:59,158 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128847
2025-04-18 13:24:59,159 - freshconnect.api.freshservice - INFO - Fetching ticket 128847
2025-04-18 13:24:59,312 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128847
2025-04-18 13:24:59,437 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:24:59,437 - freshconnect.processors.tickets - INFO - Updated history for ticket 128847
2025-04-18 13:24:59,939 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128846
2025-04-18 13:24:59,939 - freshconnect.api.freshservice - INFO - Fetching ticket 128846
2025-04-18 13:25:00,110 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128846
2025-04-18 13:25:00,805 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:00,805 - freshconnect.processors.tickets - INFO - Updated history for ticket 128846
2025-04-18 13:25:01,305 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128845
2025-04-18 13:25:01,306 - freshconnect.api.freshservice - INFO - Fetching ticket 128845
2025-04-18 13:25:01,482 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128845
2025-04-18 13:25:01,628 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:01,628 - freshconnect.processors.tickets - INFO - Updated history for ticket 128845
2025-04-18 13:25:02,130 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128843
2025-04-18 13:25:02,130 - freshconnect.api.freshservice - INFO - Fetching ticket 128843
2025-04-18 13:25:02,279 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128843
2025-04-18 13:25:02,414 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:02,415 - freshconnect.processors.tickets - INFO - Updated history for ticket 128843
2025-04-18 13:25:02,915 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128840
2025-04-18 13:25:02,916 - freshconnect.api.freshservice - INFO - Fetching ticket 128840
2025-04-18 13:25:03,052 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128840
2025-04-18 13:25:03,185 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:03,186 - freshconnect.processors.tickets - INFO - Updated history for ticket 128840
2025-04-18 13:25:03,687 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128841
2025-04-18 13:25:03,688 - freshconnect.api.freshservice - INFO - Fetching ticket 128841
2025-04-18 13:25:03,820 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128841
2025-04-18 13:25:03,938 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:03,939 - freshconnect.processors.tickets - INFO - Updated history for ticket 128841
2025-04-18 13:25:04,440 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128842
2025-04-18 13:25:04,440 - freshconnect.api.freshservice - INFO - Fetching ticket 128842
2025-04-18 13:25:04,572 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128842
2025-04-18 13:25:04,704 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:04,704 - freshconnect.processors.tickets - INFO - Updated history for ticket 128842
2025-04-18 13:25:05,205 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128839
2025-04-18 13:25:05,205 - freshconnect.api.freshservice - INFO - Fetching ticket 128839
2025-04-18 13:25:05,341 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128839
2025-04-18 13:25:05,467 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:05,467 - freshconnect.processors.tickets - INFO - Updated history for ticket 128839
2025-04-18 13:25:05,968 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128837
2025-04-18 13:25:05,968 - freshconnect.api.freshservice - INFO - Fetching ticket 128837
2025-04-18 13:25:06,114 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128837
2025-04-18 13:25:06,238 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:25:06,239 - freshconnect.processors.tickets - INFO - Updated history for ticket 128837
2025-04-18 13:25:06,740 - freshconnect.processors.tickets - INFO - Processed history for 20 tickets
2025-04-18 13:25:06,741 - __main__ - INFO - Processed history for 20 tickets
2025-04-18 13:25:06,742 - __main__ - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:25:06,743 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:25:06,745 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:25:06,746 - freshconnect.processors.agents - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:25:06,784 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'recent_agent_activity'
2025-04-18 13:25:06,785 - freshconnect.processors.agents - INFO - Stored recent agent activity in database: 0 actions by 0 agents
2025-04-18 13:25:06,791 - __main__ - INFO - Analysis completed at 2025-04-18 13:25:06
2025-04-18 13:25:21,071 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:25:21
2025-04-18 13:25:21,072 - __main__ - INFO - Looking back 7 days for tickets and 10 minutes for agent activity
2025-04-18 13:25:21,072 - __main__ - INFO - Skipping ticket fetch as requested
2025-04-18 13:25:21,072 - __main__ - INFO - Skipping ticket history fetch as requested
2025-04-18 13:25:21,072 - __main__ - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:25:21,082 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:25:21,083 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:25:21,083 - freshconnect.processors.agents - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:25:21,103 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'recent_agent_activity'
2025-04-18 13:25:21,104 - freshconnect.processors.agents - INFO - Stored recent agent activity in database: 0 actions by 0 agents
2025-04-18 13:25:21,106 - __main__ - INFO - Analysis completed at 2025-04-18 13:25:21
2025-04-18 13:39:49,942 - __main__ - INFO - Starting ticket load and agent activity analysis at 2025-04-18 13:39:49
2025-04-18 13:39:49,942 - __main__ - INFO - Looking back 1 days for tickets and 10 minutes for agent activity
2025-04-18 13:39:49,942 - __main__ - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:49,950 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:39:49,951 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:39:49,951 - freshconnect.processors.tickets - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:49,951 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:50,283 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 1)
2025-04-18 13:39:50,784 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:51,613 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 2)
2025-04-18 13:39:52,114 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:52,309 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 3)
2025-04-18 13:39:52,810 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:52,998 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 4)
2025-04-18 13:39:53,500 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:53,657 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 5)
2025-04-18 13:39:54,159 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:54,369 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 6)
2025-04-18 13:39:54,869 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:55,034 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 7)
2025-04-18 13:39:55,535 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:55,712 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 8)
2025-04-18 13:39:56,214 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:56,449 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 9)
2025-04-18 13:39:56,950 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:57,161 - freshconnect.processors.tickets - INFO - Fetched 30 tickets (page 10)
2025-04-18 13:39:57,662 - freshconnect.api.freshservice - INFO - Fetching tickets updated since 2025-04-17T13:39:49-04:00
2025-04-18 13:39:57,843 - freshconnect.processors.tickets - INFO - Fetched 19 tickets (page 11)
2025-04-18 13:40:00,741 - freshconnect.data.backends.sqlite_backend - INFO - Cleared table 'tickets' and inserted 319 items
2025-04-18 13:40:00,741 - freshconnect.processors.tickets - INFO - Stored 319 tickets in the database
2025-04-18 13:40:00,743 - __main__ - INFO - Fetched and stored 319 tickets
2025-04-18 13:40:00,743 - __main__ - INFO - Fetching history for up to 5 tickets
2025-04-18 13:40:00,744 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:40:00,745 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:40:00,745 - freshconnect.processors.tickets - INFO - Fetching history for all tickets
2025-04-18 13:40:00,755 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128860
2025-04-18 13:40:00,755 - freshconnect.api.freshservice - INFO - Fetching ticket 128860
2025-04-18 13:40:00,951 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128860
2025-04-18 13:40:01,121 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'ticket_history'
2025-04-18 13:40:01,121 - freshconnect.processors.tickets - INFO - Stored history for ticket 128860
2025-04-18 13:40:01,622 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128859
2025-04-18 13:40:01,622 - freshconnect.api.freshservice - INFO - Fetching ticket 128859
2025-04-18 13:40:01,778 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128859
2025-04-18 13:40:01,946 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:40:01,946 - freshconnect.processors.tickets - INFO - Updated history for ticket 128859
2025-04-18 13:40:02,447 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128857
2025-04-18 13:40:02,447 - freshconnect.api.freshservice - INFO - Fetching ticket 128857
2025-04-18 13:40:02,610 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128857
2025-04-18 13:40:02,799 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:40:02,800 - freshconnect.processors.tickets - INFO - Updated history for ticket 128857
2025-04-18 13:40:03,301 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128856
2025-04-18 13:40:03,301 - freshconnect.api.freshservice - INFO - Fetching ticket 128856
2025-04-18 13:40:03,460 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128856
2025-04-18 13:40:03,646 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:40:03,646 - freshconnect.processors.tickets - INFO - Updated history for ticket 128856
2025-04-18 13:40:04,147 - freshconnect.processors.tickets - INFO - Fetching history for ticket 128855
2025-04-18 13:40:04,147 - freshconnect.api.freshservice - INFO - Fetching ticket 128855
2025-04-18 13:40:04,320 - freshconnect.api.freshservice - INFO - Fetching history for ticket 128855
2025-04-18 13:40:04,456 - freshconnect.data.backends.sqlite_backend - INFO - Updated 1 items in table 'ticket_history'
2025-04-18 13:40:04,456 - freshconnect.processors.tickets - INFO - Updated history for ticket 128855
2025-04-18 13:40:04,957 - freshconnect.processors.tickets - INFO - Processed history for 5 tickets
2025-04-18 13:40:04,957 - __main__ - INFO - Processed history for 5 tickets
2025-04-18 13:40:04,958 - __main__ - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:40:04,959 - freshconnect.data.database - INFO - Using SQLite backend at C:\Scripts\FreshConnect\storage\freshconnect.db
2025-04-18 13:40:04,960 - freshconnect.data.database - INFO - Successfully initialized database
2025-04-18 13:40:04,961 - freshconnect.processors.agents - INFO - Analyzing agent activity in the last 10 minutes
2025-04-18 13:40:04,986 - freshconnect.data.backends.sqlite_backend - INFO - Inserted 1 items into table 'recent_agent_activity'
2025-04-18 13:40:04,987 - freshconnect.processors.agents - INFO - Stored recent agent activity in database: 0 actions by 0 agents
2025-04-18 13:40:04,989 - __main__ - INFO - Analysis completed at 2025-04-18 13:40:04
