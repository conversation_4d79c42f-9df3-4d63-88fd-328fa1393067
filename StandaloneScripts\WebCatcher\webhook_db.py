from sqlalchemy import create_engine, Column, Integer, String, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

Base = declarative_base()

class WebhookEvent(Base):
    __tablename__ = 'webhook_events'
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    event_type = Column(String)
    payload = Column(JSON)
    source = Column(String)

def init_db():
    engine = create_engine('sqlite:///Storage/webhooks.db')
    Base.metadata.create_all(engine)
    return sessionmaker(bind=engine)()