# FreshConnect Tests

This directory contains tests for the FreshConnect package.

## Test Structure

- `unit/`: Unit tests for individual components
  - `api/`: Tests for the API module
  - `data/`: Tests for the data module
  - `processors/`: Tests for the processors module
  - `web/`: Tests for the web module
  - `utils/`: Tests for the utils module
- `integration/`: Integration tests (to be added)

## Running Tests

### Running All Tests

```bash
# From the project root
pytest
```

### Running Tests with Coverage

```bash
# From the project root
pytest --cov=freshconnect
```

### Running Specific Test Categories

```bash
# Run all unit tests
pytest -m unit

# Run all API tests
pytest -m api

# Run all data tests
pytest -m data

# Run all processor tests
pytest -m processors

# Run all web tests
pytest -m web

# Run all utils tests
pytest -m utils
```

### Running Specific Test Files

```bash
# Run tests in a specific file
pytest tests/unit/api/test_freshservice.py

# Run tests in a specific directory
pytest tests/unit/processors/
```

## Test Fixtures

Common test fixtures are defined in `conftest.py`:

- `mock_response`: Creates a mock HTTP response object
- `mock_api_client`: Creates a mock FreshService API client
- `temp_db_file`: Creates a temporary database file
- `mock_db`: Creates a mock database with test data
- `mock_env_vars`: Mocks environment variables

## Writing Tests

When writing new tests:

1. Use the appropriate markers (`unit`, `api`, `data`, etc.)
2. Use fixtures from `conftest.py` when possible
3. Mock external dependencies
4. Follow the existing test structure and naming conventions

Example:

```python
import pytest

@pytest.mark.unit
@pytest.mark.api
def test_my_function(mock_api_client):
    # Test code here
    assert result == expected
```
