#!/usr/bin/env python3
"""
Suspicious Content Extractor

This script extracts suspicious emails and links from ticket conversations JSON file
and creates a CSV with ticket details.

Usage:
    python extract_suspicious_content.py <tickets_json_file>

Example:
    python extract_suspicious_content.py ticket_conversations_20250711_115454.json
"""

import sys
import json
import csv
import re
from datetime import datetime
from urllib.parse import urlparse


def extract_emails_and_links(text):
    """
    Extract email addresses and URLs from text.
    
    Args:
        text (str): Text to search
        
    Returns:
        tuple: (emails, links)
    """
    if not text:
        return [], []
    
    # Email regex pattern
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    
    # URL regex pattern (http/https and common domains)
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+|www\.[^\s<>"{}|\\^`\[\]]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"{}|\\^`\[\]]*)?'
    
    emails = re.findall(email_pattern, text, re.IGNORECASE)
    urls = re.findall(url_pattern, text, re.IGNORECASE)
    
    # Clean up URLs - add http:// if missing
    cleaned_urls = []
    for url in urls:
        if not url.startswith(('http://', 'https://')):
            if url.startswith('www.'):
                url = 'http://' + url
            elif '.' in url and not url.startswith('@'):  # Avoid email addresses
                url = 'http://' + url
        cleaned_urls.append(url)
    
    return emails, cleaned_urls


def get_domain_from_email_or_url(content):
    """
    Extract domain from email address or URL.
    
    Args:
        content (str): Email address or URL
        
    Returns:
        str: Domain name
    """
    if '@' in content:
        # Email address
        return content.split('@')[1].lower()
    else:
        # URL
        try:
            if not content.startswith(('http://', 'https://')):
                content = 'http://' + content
            parsed = urlparse(content)
            return parsed.netloc.lower()
        except:
            return content.lower()


def process_ticket_conversations(json_file):
    """
    Process ticket conversations and extract suspicious content.
    
    Args:
        json_file (str): Path to the JSON file
        
    Returns:
        list: List of dictionaries with extracted data
    """
    print(f"Loading ticket data from: {json_file}")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    tickets = data.get('tickets', [])
    print(f"Processing {len(tickets)} tickets...")
    
    results = []
    
    for i, ticket in enumerate(tickets, 1):
        if i % 500 == 0:
            print(f"Processed {i}/{len(tickets)} tickets...")
        
        ticket_id = ticket.get('ticket_id')
        ticket_details = ticket.get('ticket_details', {})
        conversations = ticket.get('conversations', [])
        
        # Get ticket information
        department_id = ticket_details.get('department_id')
        created_at = ticket_details.get('created_at', '')
        
        # Parse ticket date
        ticket_date = ''
        if created_at:
            try:
                # Parse ISO format date
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                ticket_date = dt.strftime('%Y-%m-%d')
            except:
                ticket_date = created_at[:10] if len(created_at) >= 10 else created_at
        
        # Create ticket link
        ticket_link = f"https://kaservicedesk.kingspan.com/a/tickets/{ticket_id}"
        
        # Process ticket subject and description
        all_text = []
        
        # Add subject
        subject = ticket_details.get('subject', '')
        if subject:
            all_text.append(subject)
        
        # Add description
        description = ticket_details.get('description_text', '')
        if description:
            all_text.append(description)
        
        # Add conversation content
        for conv in conversations:
            body_text = conv.get('body_text', '')
            if body_text:
                all_text.append(body_text)
        
        # Extract emails and links from all text
        for text in all_text:
            emails, links = extract_emails_and_links(text)
            
            # Add emails to results
            for email in emails:
                domain = get_domain_from_email_or_url(email)
                results.append({
                    'ticket_id': ticket_id,
                    'suspicious_content': email,
                    'domain': domain,
                    'department_id': department_id,
                    'ticket_date': ticket_date,
                    'ticket_link': ticket_link
                })
            
            # Add links to results
            for link in links:
                domain = get_domain_from_email_or_url(link)
                results.append({
                    'ticket_id': ticket_id,
                    'suspicious_content': link,
                    'domain': domain,
                    'department_id': department_id,
                    'ticket_date': ticket_date,
                    'ticket_link': ticket_link
                })
    
    print(f"Extracted {len(results)} suspicious emails and links")
    return results


def save_to_csv(results, output_file):
    """
    Save results to CSV file.
    
    Args:
        results (list): List of result dictionaries
        output_file (str): Output CSV filename
    """
    print(f"Saving results to: {output_file}")
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        fieldnames = [
            'ticket_id',
            'suspicious_email_or_link', 
            'domain',
            'department_id',
            'ticket_date',
            'ticket_link'
        ]
        
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            writer.writerow({
                'ticket_id': result['ticket_id'],
                'suspicious_email_or_link': result['suspicious_content'],
                'domain': result['domain'],
                'department_id': result['department_id'],
                'ticket_date': result['ticket_date'],
                'ticket_link': result['ticket_link']
            })
    
    print(f"CSV file created with {len(results)} entries")


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python extract_suspicious_content.py <tickets_json_file>")
        print("Example: python extract_suspicious_content.py ticket_conversations_20250711_115454.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        # Process the JSON file
        results = process_ticket_conversations(json_file)
        
        if not results:
            print("No suspicious emails or links found.")
            sys.exit(0)
        
        # Generate output filename
        base_name = json_file.replace('.json', '')
        csv_file = f"{base_name}_suspicious_content.csv"
        
        # Save to CSV
        save_to_csv(results, csv_file)
        
        # Print summary
        print(f"\nSummary:")
        print(f"Total entries: {len(results)}")
        
        # Count unique tickets
        unique_tickets = len(set(r['ticket_id'] for r in results))
        print(f"Unique tickets: {unique_tickets}")
        
        # Count by type
        emails = [r for r in results if '@' in r['suspicious_content']]
        links = [r for r in results if '@' not in r['suspicious_content']]
        print(f"Email addresses: {len(emails)}")
        print(f"Links/URLs: {len(links)}")
        
        # Show sample entries
        print(f"\nSample entries:")
        for i, result in enumerate(results[:5]):
            print(f"  {i+1}. Ticket {result['ticket_id']}: {result['suspicious_content'][:50]}...")
        
        print(f"\nOutput file: {csv_file}")
        
    except FileNotFoundError:
        print(f"Error: File '{json_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
