# Script Launcher

A Flask-based web application for browsing and running standalone scripts with a user-friendly interface.

## Features

- Browse available scripts by category
- Run scripts with custom parameters
- View script output in real-time
- Admin interface for managing scripts
- Support for Python and PowerShell scripts

## Installation

1. Make sure you have Python 3.6+ installed
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. Run the Flask application:

```bash
python app.py
```

2. Open your browser and navigate to http://localhost:5001

## Adding Scripts

You can add scripts through the admin interface or by directly editing the database. Each script requires:

- Name: A descriptive name for the script
- Description: What the script does
- Path: Relative path to the script file
- Type: Python, PowerShell, or other
- Category: For organizing scripts
- Parameters: Configuration for input parameters

## Directory Structure

```
ScriptLauncher/
├── app.py                  # Main Flask application
├── scripts_db.py           # Database functions
├── data/                   # Database storage
│   └── scripts.db          # SQLite database
├── static/                 # Static assets
│   ├── css/
│   │   └── style.css       # Custom CSS
│   └── js/
│       └── scripts.js      # Custom JavaScript
└── templates/              # HTML templates
    ├── base.html           # Base template
    ├── index.html          # Script index
    ├── script_form.html    # Script execution form
    ├── admin.html          # Admin dashboard
    ├── add_script.html     # Add script form
    └── edit_script.html    # Edit script form
```

## Dependencies

- Flask: Web framework
- Bootstrap 5: Frontend framework
- jQuery: JavaScript library
- Font Awesome: Icon library
- SQLite: Database
