{"history": {"1": {"history": {"1": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Service Delivery", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:05:26Z"}, "2": {"actor": {"id": 19004128309, "name": "<PERSON>"}, "content": " set Status as Resolved, set Sub Category as Monitor, set Item as Setup Issues / Not working correctly, set planned end date as Thu, 2024, Oct 31 15:05 and set planned start date as Thu, 2024, Oct 31 14:50", "sub_contents": null, "created_at": "2024-10-31T15:05:25Z"}, "3": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Service Delivery", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:05:16Z"}, "4": {"actor": {"id": 19004128309, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T15:05:15Z"}, "5": {"actor": {"id": 19004128309, "name": "<PERSON>"}, "content": " Updated Resolution Note", "sub_contents": null, "created_at": "2024-10-31T15:04:37Z"}, "6": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Service Delivery", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:04:32Z"}, "7": {"actor": {"id": 19004128309, "name": "<PERSON>"}, "content": " added a private note", "sub_contents": null, "created_at": "2024-10-31T15:04:31Z"}, "8": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Service Delivery", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T14:43:27Z"}, "9": {"actor": {"id": 19004913490, "name": "<PERSON>"}, "content": " set Category as Hardware, set Agent as <PERSON> and set Group as Support Specialist L2", "sub_contents": null, "created_at": "2024-10-31T14:43:26Z"}, "10": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Timer Node event", "sub_contents": ["set Is Parent or Child as None", "Workflow Ends"], "created_at": "2024-10-31T13:47:12Z"}, "11": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Tags - Associate Ticket ID Adding P-Tags workflow from Timer Node event", "sub_contents": ["added tag P-107804", "Expression successfully executed for Create tag string | Concat(\"p-\",\"{{ticket.associated_ticket_id}}\")", "Workflow Ends"], "created_at": "2024-10-31T12:51:12Z"}, "12": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Timer Node event", "sub_contents": ["set Boarding Critical as None", "Workflow Ends"], "created_at": "2024-10-31T12:50:12Z"}, "13": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Assign Request Type (Requester & Internal Service Account & Agent) workflow from Ticket is raised event", "sub_contents": ["set Request Type as Customer", "Workflow Ends"], "created_at": "2024-10-31T12:47:12Z"}, "14": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Ticket is raised  event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  9:47", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:47:12Z"}, "15": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Auto-assign tickets to L1 that are unassigned after 5 minutes workflow from Ticket is raised  event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:52", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:47:12Z"}, "16": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Tags - Associate Ticket ID Adding P-Tags workflow from Ticket is raised or child added event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:51", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:47:12Z"}, "17": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Ticket is raised or Service Req is Updated (requested items is added doesnt work***) event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:50", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:47:12Z"}, "18": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Laptop/Desktop, Mobile Tasks Deployment, Printers workflow from Service request is raised or when requested item added event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:48", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:47:12Z"}, "19": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T17:18:27Z"}, "20": {"actor": {"id": 19000463912, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T17:18:26Z"}, "21": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T17:14:28Z"}, "22": {"actor": {"id": 19004145184, "name": "<PERSON>"}, "content": " set Status as Resolved, set planned end date as Thu, 2024, Oct 31 17:14 and set planned start date as Thu, 2024, Oct 31 16:59", "sub_contents": null, "created_at": "2024-10-31T17:14:28Z"}, "23": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T17:14:25Z"}, "24": {"actor": {"id": 19004145184, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T17:14:25Z"}, "25": {"actor": {"id": 19000339280, "name": "System"}, "content": " First response time breached for Incident SLA", "sub_contents": null, "created_at": "2024-10-31T16:43:24Z"}, "26": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T16:26:33Z"}, "27": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:39:22Z"}, "28": {"actor": {"id": 19004145184, "name": "<PERSON>"}, "content": " set Agent as <PERSON>", "sub_contents": null, "created_at": "2024-10-31T15:39:22Z"}, "29": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": "updated a note", "sub_contents": null, "created_at": "2024-10-31T14:14:53Z"}, "30": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T14:14:28Z"}, "31": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": " set Category as Applications (Software), set Agent as none, set Sub Category as Email, set Item as Blocked Incoming Email and set Group as Systems Administrators", "sub_contents": null, "created_at": "2024-10-31T14:14:26Z"}, "32": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": "updated a note", "sub_contents": null, "created_at": "2024-10-31T14:14:19Z"}, "33": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Timer Node event", "sub_contents": ["set Boarding Critical as None", "Workflow Ends"], "created_at": "2024-10-31T14:13:36Z"}, "34": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Service Delivery", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T14:12:41Z"}, "35": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": "deleted item Email Feature Requests", "sub_contents": null, "created_at": "2024-10-31T14:12:40Z"}, "36": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": " set Type as Incident", "sub_contents": ["System executed Incident SLA (SLA) and set due by time as Mon, 2024, Nov 4 13:42"], "created_at": "2024-10-31T14:12:40Z"}, "37": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:05:07Z"}, "38": {"actor": {"id": 19000464068, "name": "<PERSON>"}, "content": " set Status as Closed, set Category as Applications (Software), set Sub Category as SAP (Vicwest), set Item as Production, set planned end date as Thu, 2024, Oct 31 15:05 and set planned start date as Thu, 2024, Oct 31 14:50", "sub_contents": null, "created_at": "2024-10-31T15:05:07Z"}, "39": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T15:04:28Z"}, "40": {"actor": {"id": 19000463951, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T15:04:27Z"}, "41": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T14:23:59Z"}, "42": {"actor": {"id": 19000464068, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T14:23:59Z"}, "43": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T14:12:25Z"}, "44": {"actor": {"id": 19000463951, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T14:12:24Z"}, "45": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Timer Node event", "sub_contents": ["set Is Parent or Child as None", "Workflow Ends"], "created_at": "2024-10-31T13:30:32Z"}, "46": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T13:09:05Z"}, "47": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Assign Ticket to the First Responder workflow from Reply or Public note is added by agent event", "sub_contents": ["set Agent as <PERSON>", "Workflow Ends"], "created_at": "2024-10-31T13:09:05Z"}, "48": {"actor": {"id": 19000464068, "name": "<PERSON>"}, "content": "replied  to  <EMAIL>", "sub_contents": null, "created_at": "2024-10-31T13:09:05Z"}, "49": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as Vicwest and set Subdivision as Vicwest BIS", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T12:39:37Z"}, "50": {"actor": {"id": 19005732943, "name": "<PERSON>"}, "content": " set Group as Vicwest BIS", "sub_contents": null, "created_at": "2024-10-31T12:39:36Z"}, "51": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Tags - Associate Ticket ID Adding P-Tags workflow from Timer Node event", "sub_contents": ["added tag P-107802", "Expression successfully executed for Create tag string | Concat(\"p-\",\"{{ticket.associated_ticket_id}}\")", "Workflow Ends"], "created_at": "2024-10-31T12:34:32Z"}, "52": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Timer Node event", "sub_contents": ["set Boarding Critical as None", "Workflow Ends"], "created_at": "2024-10-31T12:33:32Z"}, "53": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Assign Request Type (Requester & Internal Service Account & Agent) workflow from Ticket is raised event", "sub_contents": ["set Request Type as Customer", "Workflow Ends"], "created_at": "2024-10-31T12:30:32Z"}, "54": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Ticket is raised  event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  9:30", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:30:32Z"}, "55": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Timer Node event", "sub_contents": ["set Is Parent or Child as None", "set due by time as Fri, 2024, Nov 8 14:07", "Workflow Ends"], "created_at": "2024-10-31T13:15:23Z"}, "56": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T12:23:36Z"}, "57": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Don't Send Email Notifications by Requester workflow from Ticket is updated/raised/marked as spam  event", "sub_contents": ["Skipped the new ticket email notifications", "Workflow Ends"], "created_at": "2024-10-31T12:23:36Z"}, "58": {"actor": {"id": 19003512388, "name": "<PERSON><PERSON>"}, "content": " set Status as Closed, set planned end date as Thu, 2024, Oct 31 12:23 and set planned start date as Thu, 2024, Oct 31 12:08", "sub_contents": null, "created_at": "2024-10-31T12:23:35Z"}, "59": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T12:23:18Z"}, "60": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Don't Send Email Notifications by Requester workflow from Ticket is updated/raised/marked as spam  event", "sub_contents": ["Skipped the new ticket email notifications", "Workflow Ends"], "created_at": "2024-10-31T12:23:18Z"}, "61": {"actor": {"id": 19003512388, "name": "<PERSON><PERSON>"}, "content": " set Agent as <PERSON><PERSON>", "sub_contents": ["Modified Additional Status Details"], "created_at": "2024-10-31T12:23:17Z"}, "62": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Ticket Set Agent Team Division, Agent Team Subdivision, Agent (Reporting) workflow from Ticket Agent or Group is Updated event", "sub_contents": ["set Division as KA IT and set Subdivision as KA IT Operations", "Modified Agent (Reporting)", "Workflow Ends"], "created_at": "2024-10-31T12:23:10Z"}, "63": {"actor": {"id": 19003512388, "name": "<PERSON><PERSON>"}, "content": " added a private note", "sub_contents": null, "created_at": "2024-10-31T12:23:09Z"}, "64": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Tags - Associate Ticket ID Adding P-Tags workflow from Timer Node event", "sub_contents": ["added tag P-107801", "Expression successfully executed for Create tag string | Concat(\"p-\",\"{{ticket.associated_ticket_id}}\")", "Workflow Ends"], "created_at": "2024-10-31T12:19:23Z"}, "65": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Timer Node event", "sub_contents": ["set Boarding Critical as None", "Workflow Ends"], "created_at": "2024-10-31T12:18:23Z"}, "66": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Assign Request Type (Requester & Internal Service Account & Agent) workflow from Ticket is raised event", "sub_contents": ["set Request Type as Internal Service Account", "Workflow Ends"], "created_at": "2024-10-31T12:15:23Z"}, "67": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Fields - <PERSON> Parent or Child to None if uninitialized for more than 1hour workflow from Ticket is raised  event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  9:15", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:15:23Z"}, "68": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Auto-assign tickets to L1 that are unassigned after 5 minutes workflow from Ticket is raised  event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:20", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:15:23Z"}, "69": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Tags - Associate Ticket ID Adding P-Tags workflow from Ticket is raised or child added event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:19", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:15:23Z"}, "70": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Employee Onboarding - Critical Service Items workflow from Ticket is raised or Service Req is Updated (requested items is added doesnt work***) event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:18", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:15:23Z"}, "71": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Tickets - Laptop/Desktop, Mobile Tasks Deployment, Printers workflow from Service request is raised or when requested item added event", "sub_contents": ["System initiated Timer. Scheduled to end at Thu, 2024, Oct 31  8:16", "Workflow is waiting for another event Timer Node"], "created_at": "2024-10-31T12:15:23Z"}, "72": {"actor": {"id": 0, "name": "Ticket Workflow"}, "content": " executed Automatic Call Routing - Non-Boarding Related workflow from Ticket is raised event", "sub_contents": ["set Category as Governance, set Type as Incident, set Sub Category as Monitoring, set Department as Kingspan KNA, set Item as Audit Reports and set Group as IT Ops - Governance", "Workflow Ends"], "created_at": "2024-10-31T12:15:23Z"}}}}}