"""
Run the FreshConnect web application.

This script loads environment variables from .env file and runs the Flask app.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check if API key is set
api_key = os.getenv('FRESHSERVICE_API_KEY')
domain = os.getenv('FRESHSERVICE_DOMAIN')

if not api_key:
    print("Error: FRESHSERVICE_API_KEY environment variable is not set.")
    print("Please set this in your .env file or environment variables.")
    exit(1)
    
if not domain:
    print("Error: FRESHSERVICE_DOMAIN environment variable is not set.")
    print("Please set this in your .env file or environment variables.")
    exit(1)

# Manually set the API key and domain in settings
from freshconnect.config import settings
settings.FRESHSERVICE_CONFIG['api_key'] = api_key
settings.FRESHSERVICE_CONFIG['domain'] = domain

print(f"Using FreshService domain: {domain}")

# Run the Flask app
from freshconnect.web.app import run_app
run_app()
