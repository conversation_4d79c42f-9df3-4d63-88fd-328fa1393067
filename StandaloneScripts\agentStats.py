import json
import plotly.graph_objects as go
from collections import Counter
from plotly.subplots import make_subplots
from datetime import datetime

def analyze_agent_activity():
    # Read tickets data
    with open('Storage/tickets.json', 'r') as f:
        tickets_data = json.load(f)
        
    # Read history data
    with open('Storage/ticketHistory.json', 'r') as f:
        history_data = json.load(f)

    # Extract all actor names, group setters, and timestamps
    actors = []
    group_setters = []
    action_times = []
    
    for ticket_history in history_data['history'].values():
        for event in ticket_history['history'].values():
            actor_name = event['actor'].get('name')
            if actor_name and actor_name != "System" and actor_name != "Ticket Workflow":
                actors.append(actor_name)
                
                # Check if this event involves setting a group
                content = event.get('content', '').lower()
                if 'set group as' in content:
                    group_setters.append(actor_name)
                
                # Record timestamp and actor
                if 'created_at' in event:
                    timestamp = datetime.fromisoformat(event['created_at'].replace('Z', '+00:00'))
                    action_times.append((timestamp, actor_name))

    # Count occurrences
    actor_counts = Counter(actors)
    group_setter_counts = Counter(group_setters)

    # Sort by count
    sorted_actors = dict(sorted(actor_counts.items(), key=lambda x: x[1], reverse=True))
    sorted_group_setters = dict(sorted(group_setter_counts.items(), key=lambda x: x[1], reverse=True))

    # Create subplot visualization
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=('Overall Agent Activity', 'Group Setting Activity', 'Action Timing'),
        vertical_spacing=0.1,
        row_heights=[0.33, 0.33, 0.34]
    )

    # Add overall activity bar
    fig.add_trace(
        go.Bar(x=list(sorted_actors.keys()), y=list(sorted_actors.values()), name="Overall Activity"),
        row=1, col=1
    )

    # Add group setting activity bar
    fig.add_trace(
        go.Bar(x=list(sorted_group_setters.keys()), y=list(sorted_group_setters.values()), name="Group Settings"),
        row=2, col=1
    )

    # Count occurrences of timestamp-actor pairs
    action_counts = Counter(action_times)
    
    # Sort action times and prepare data with sizes
    sorted_actions = sorted(action_counts.items(), key=lambda x: x[0][0])
    timestamps = [x[0][0] for x in sorted_actions]
    actors_time = [x[0][1] for x in sorted_actions]
    sizes = [x[1] * 10 for x in sorted_actions]  # Multiply by 10 to make sizes more visible
    
    # Add scatter plot for timing
    fig.add_trace(
        go.Scatter(
            x=timestamps,
            y=actors_time,
            mode='markers',
            name="Action Timing",
            marker=dict(
                size=sizes,
                sizemode='area',
                sizeref=2.*max(sizes)/(40.**2),  # Normalize sizes
                sizemin=4,  # Minimum size for single occurrences
                color=sizes,  # Use sizes for color intensity
                colorscale='RdYlBu_r',  # Red-Yellow-Blue reversed (warm center)
                showscale=True,  # Show colorbar
                colorbar=dict(
                    title="Actions",
                    x=1.1  # Move colorbar outside the plot
                )
            )
        ),
        row=3, col=1
    )

    # Update layout
    fig.update_layout(
        height=1800,  # Increased height for three graphs
        width=1200,
        showlegend=False,
        title_text="Agent Activity Analysis"
    )

    # Update x-axis properties
    fig.update_xaxes(tickangle=-45, row=1, col=1)
    fig.update_xaxes(tickangle=-45, row=2, col=1)
    fig.update_xaxes(title_text="Time", row=3, col=1)
    
    # Update y-axis titles
    fig.update_yaxes(title_text="Number of Actions", row=1, col=1)
    fig.update_yaxes(title_text="Number of Group Changes", row=2, col=1)
    fig.update_yaxes(title_text="Agent", row=3, col=1)

    # Add more bottom margin for rotated labels
    fig.update_layout(margin=dict(b=150))

    # Show the plot in browser
    fig.show()

if __name__ == "__main__":
    analyze_agent_activity()
