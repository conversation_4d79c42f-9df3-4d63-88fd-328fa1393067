{"service_items": {"1": {"id": 19000742684, "created_at": "2021-11-04T19:57:32Z", "updated_at": "2024-08-16T21:32:55Z", "name": "MFA", "delivery_time": null, "display_id": 130, "category_id": 19000095373, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "2": {"id": ***********, "created_at": "2021-12-27T17:34:54Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Account Password Requests", "delivery_time": null, "display_id": 171, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_type_of_request}} {{item.name}} | Requested by {{requestor.name}}"}}, "3": {"id": ***********, "created_at": "2022-08-08T20:12:58Z", "updated_at": "2024-08-27T17:23:34Z", "name": "AD Account Migration to Another Kingspan Child Domain", "delivery_time": null, "display_id": 218, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_users}} | {{item.name}} - {{custom_field.cf_user_account}}; {{custom_field.cf_user_or_account_name_if_not_found_is_drop_down_above}} ", "auto_gen_document": false}}, "4": {"id": ***********, "created_at": "2021-11-03T23:02:47Z", "updated_at": "2024-08-16T21:32:54Z", "name": "AD Network Account Extension", "delivery_time": null, "display_id": 125, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "5": {"id": ***********, "created_at": "2022-11-01T13:26:31Z", "updated_at": "2024-08-16T21:33:18Z", "name": "AD Network Account Extension for Consultants", "delivery_time": null, "display_id": 234, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "6": {"id": ***********, "created_at": "2024-02-28T18:37:40Z", "updated_at": "2024-08-16T21:33:50Z", "name": "AD Network Account Reactivation", "delivery_time": null, "display_id": 369, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "7": {"id": ***********, "created_at": "2021-11-04T18:13:09Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Email Feature Requests", "delivery_time": null, "display_id": 129, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "8": {"id": 19001188142, "created_at": "2023-12-06T15:16:17Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Group Access, Members and Permission", "delivery_time": null, "display_id": 351, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_title}}  {{item.name}}"}}, "9": {"id": ***********, "created_at": "2022-07-14T19:47:00Z", "updated_at": "2024-08-16T21:33:10Z", "name": "Special Account Request - Generic, Admin, etc. ", "delivery_time": null, "display_id": 207, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_account_type}} - {{item.name}} "}}, "10": {"id": ***********, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:53Z", "name": "VPN Access Request", "delivery_time": null, "display_id": 61, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "11": {"id": ***********, "created_at": "2022-10-31T13:54:16Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Automate file transformation with scripts", "delivery_time": null, "display_id": 232, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "12": {"id": 19001157259, "created_at": "2022-05-17T21:51:13Z", "updated_at": "2024-08-16T21:33:09Z", "name": "Access & Permissions - Database DB", "delivery_time": null, "display_id": 202, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}}: {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}", "auto_gen_document": false}}, "13": {"id": 19000446196, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:52Z", "name": "Data Backup", "delivery_time": null, "display_id": 6, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-data-backup-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "14": {"id": 19000998624, "created_at": "2021-12-18T01:59:18Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Data Request - IT User or Systems Data", "delivery_time": null, "display_id": 167, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "15": {"id": 19000446197, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:52Z", "name": "Data Restore from Backup", "delivery_time": null, "display_id": 7, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-file-restore-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "16": {"id": 19001189995, "created_at": "2024-01-08T16:07:50Z", "updated_at": "2024-08-16T21:33:44Z", "name": "Data Retention for Legal Hold", "delivery_time": null, "display_id": 355, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-data-backup-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}", "auto_gen_document": false}}, "17": {"id": 19001166467, "created_at": "2022-11-04T19:56:57Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Database Restore", "delivery_time": null, "display_id": 235, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "18": {"id": 19001144099, "created_at": "2022-01-25T21:06:53Z", "updated_at": "2024-08-16T21:33:07Z", "name": "Document Translation", "delivery_time": null, "display_id": 189, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "19": {"id": 19001183926, "created_at": "2023-09-22T14:08:23Z", "updated_at": "2024-08-16T21:33:41Z", "name": "eDiscovery Request", "delivery_time": null, "display_id": 335, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_request_summary}} - {{item.name}}"}}, "20": {"id": 19001202274, "created_at": "2024-08-26T16:36:50Z", "updated_at": "2024-08-26T20:38:02Z", "name": "Enterprise Application Registration", "delivery_time": null, "display_id": 437, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} | {{custom_field.cf_short_description}}"}}, "21": {"id": 19001147950, "created_at": "2022-02-23T18:57:24Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Equipment Recieving", "delivery_time": null, "display_id": 191, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "22": {"id": 19000861835, "created_at": "2021-11-25T04:46:32Z", "updated_at": "2024-08-16T21:32:59Z", "name": "Files and Folders - Migrate or Reorganize ", "delivery_time": null, "display_id": 154, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "23": {"id": 19001161180, "created_at": "2022-07-20T13:04:58Z", "updated_at": "2024-08-16T21:33:15Z", "name": "IT Device and Data Maintenance", "delivery_time": null, "display_id": 209, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_request_details}} {{item.name}}"}}, "24": {"id": 19001171206, "created_at": "2023-03-15T16:44:47Z", "updated_at": "2024-08-16T21:33:28Z", "name": "IT Systems - Daily Lansweeper Asset Review & Maintenance", "delivery_time": null, "display_id": 271, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "25": {"id": 19001189098, "created_at": "2023-12-20T19:41:28Z", "updated_at": "2024-08-16T21:33:43Z", "name": "Lansweeper Custom Reports", "delivery_time": null, "display_id": 353, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "26": {"id": 19001201487, "created_at": "2024-08-12T19:52:00Z", "updated_at": "2024-08-22T19:08:48Z", "name": "Network File Share - Remove Share", "delivery_time": null, "display_id": 430, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "27": {"id": 19000905730, "created_at": "2021-12-01T19:51:28Z", "updated_at": "2024-08-16T21:32:59Z", "name": "Network Shared Files and Folders - Permissions and Access", "delivery_time": null, "display_id": 157, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "28": {"id": 19001173917, "created_at": "2023-05-29T19:39:13Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Power BI - Request for Dataflows, Data Models, Reports, etc.", "delivery_time": null, "display_id": 284, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "29": {"id": 19001188737, "created_at": "2023-12-14T19:27:58Z", "updated_at": "2024-08-16T21:33:43Z", "name": "Powerplatform - Automation", "delivery_time": null, "display_id": 352, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "30": {"id": 19001171933, "created_at": "2023-04-06T13:58:45Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Report Generation", "delivery_time": null, "display_id": 277, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "31": {"id": 19001199995, "created_at": "2024-07-08T15:43:30Z", "updated_at": "2024-08-16T21:33:56Z", "name": "Sharepoint - Maintenance (Updates, Deletion, etc.)", "delivery_time": null, "display_id": 397, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "32": {"id": 19001171260, "created_at": "2023-03-16T21:59:08Z", "updated_at": "2024-08-16T21:33:28Z", "name": "SQL Server Database Metadata", "delivery_time": null, "display_id": 272, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "33": {"id": 19001169982, "created_at": "2023-02-13T17:08:27Z", "updated_at": "2024-08-16T21:33:27Z", "name": "<PERSON><PERSON> VM Backups in addition to nightly backups", "delivery_time": null, "display_id": 262, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "34": {"id": ***********, "created_at": "2022-07-19T19:32:56Z", "updated_at": "2024-08-16T21:33:10Z", "name": "User Account Maintenance - AD, O365 Data", "delivery_time": null, "display_id": 208, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} :  {{custom_field.cf_request_details}} |{{item.name}} ", "auto_gen_document": false}}, "35": {"id": ***********, "created_at": "2021-11-17T11:03:56Z", "updated_at": "2024-08-16T21:32:57Z", "name": "AD Network Account Creation", "delivery_time": null, "display_id": 143, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requestor.name}}: {{item.name}} - {{requested_for}}", "auto_gen_document": false}}, "36": {"id": ***********, "created_at": "2022-01-19T18:28:02Z", "updated_at": "2024-08-16T21:33:06Z", "name": "AD Network Account Termination", "delivery_time": null, "display_id": 178, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}}: {{item.name}}"}}, "37": {"id": ***********, "created_at": "2024-07-18T13:57:39Z", "updated_at": "2024-08-16T21:33:56Z", "name": "AD User Account Migration - To KNA", "delivery_time": null, "display_id": 402, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "38": {"id": ***********, "created_at": "2021-11-17T12:53:12Z", "updated_at": "2024-08-16T21:32:57Z", "name": "Boarding Hardware Options", "delivery_time": null, "display_id": 146, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472064, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "39": {"id": 19000785685, "created_at": "2021-11-12T23:21:29Z", "updated_at": "2024-08-16T21:32:55Z", "name": "Boarding Software Selections", "delivery_time": null, "display_id": 138, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "40": {"id": ***********, "created_at": "2021-10-26T15:17:45Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Concur New Account", "delivery_time": null, "display_id": 75, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_concur_instance_your_business}} - {{item.name}}", "auto_gen_document": false}}, "41": {"id": ***********, "created_at": "2021-10-26T15:18:34Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Credit Card", "delivery_time": null, "display_id": 76, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request by {{requestor.name}} for {{requested_for}} : {{item.name}} - {{custom_field.cf_employee_name_for_credit_card}}", "auto_gen_document": false}}, "42": {"id": ***********, "created_at": "2024-08-15T12:43:42Z", "updated_at": "2024-08-16T21:34:05Z", "name": "Delegated OneDrive Access", "delivery_time": null, "display_id": 433, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Grant access to {{custom_field.cf_onedrive_to_access}} OneDrive files"}}, "43": {"id": 19000446221, "created_at": "2021-09-08T17:00:32Z", "updated_at": "2024-10-15T03:44:53Z", "name": "Email Access", "delivery_time": null, "display_id": 31, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-email-access-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "44": {"id": 19000772419, "created_at": "2021-11-10T20:25:53Z", "updated_at": "2024-08-16T21:32:55Z", "name": "Employee Change Board - User Data", "delivery_time": null, "display_id": 135, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}}: {{item.name}} "}}, "45": {"id": 19001143399, "created_at": "2022-01-19T15:51:28Z", "updated_at": "2024-08-16T21:33:06Z", "name": "Employee Off-boarding", "delivery_time": null, "display_id": 175, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-employee-offboarding-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "Off-Board {{custom_field.cf_select_the_employee_or_consultant_s_name}} / {{custom_field.cf_employee_name_user_id_amp_email_if_not_available_above}} - Requested By {{requestor.name}}", "auto_gen_document": false}}, "46": {"id": 19000446195, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:52Z", "name": "Employee On-boarding", "delivery_time": null, "display_id": 5, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-newhire-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "On-board {{custom_field.cf_employee_first_name}} {{custom_field.cf_employee_last_name}} ({{custom_field.cf_employee_start_date}}) - {{custom_field.cf_employee_type}} {{custom_field.cf_locations}}", "auto_gen_document": false}}, "47": {"id": 19001162591, "created_at": "2022-08-13T13:55:27Z", "updated_at": "2024-08-16T21:33:16Z", "name": "Employee On-boarding (AWIP)", "delivery_time": null, "display_id": 222, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "On-board {{custom_field.cf_employee_first_name}} {{custom_field.cf_employee_last_name}} ({{custom_field.cf_employee_start_date}}) - {{custom_field.cf_employee_type}} {{custom_field.cf_locations}}"}}, "48": {"id": 19001169146, "created_at": "2023-01-21T05:09:35Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Employee On-Boarding (KA IT)", "delivery_time": null, "display_id": 256, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "49": {"id": 19001167138, "created_at": "2022-11-21T15:17:18Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Employee On-boarding (KIP)", "delivery_time": null, "display_id": 240, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-newhire-new", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "On-board {{custom_field.cf_employee_first_name}} {{custom_field.cf_employee_last_name}} ({{custom_field.cf_employee_start_date}}) - {{custom_field.cf_employee_type}} {{custom_field.cf_locations}}"}}, "50": {"id": 19000809004, "created_at": "2021-11-17T11:01:36Z", "updated_at": "2024-08-16T21:32:57Z", "name": "Finance and Fleet (decomissioned)", "delivery_time": null, "display_id": 142, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} ", "auto_gen_document": false}}, "51": {"id": 19001143438, "created_at": "2022-01-19T17:55:48Z", "updated_at": "2024-08-16T21:33:06Z", "name": "Finance and Fleet Termination", "delivery_time": null, "display_id": 177, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} "}}, "52": {"id": 19000690456, "created_at": "2021-10-26T15:22:59Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Fleet Car", "delivery_time": null, "display_id": 77, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "53": {"id": 19000690195, "created_at": "2021-10-26T14:48:14Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Hardware Requests", "delivery_time": null, "display_id": 72, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "54": {"id": 19000809478, "created_at": "2021-11-17T12:46:08Z", "updated_at": "2024-08-16T21:32:57Z", "name": "KIP Applications", "delivery_time": null, "display_id": 144, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "55": {"id": 19000809479, "created_at": "2021-11-17T12:49:20Z", "updated_at": "2024-08-16T21:32:57Z", "name": "KIP Finance and Fleet", "delivery_time": null, "display_id": 145, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} "}}, "56": {"id": 19001187086, "created_at": "2023-11-10T17:32:19Z", "updated_at": "2024-08-16T21:33:42Z", "name": "KnowBe4 - Add User ", "delivery_time": null, "display_id": 346, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "57": {"id": 19001189605, "created_at": "2024-01-02T20:21:51Z", "updated_at": "2024-08-16T21:33:43Z", "name": "KnowBe4 - Archive User", "delivery_time": null, "display_id": 354, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "58": {"id": 19001176427, "created_at": "2023-07-10T16:10:05Z", "updated_at": "2024-08-16T21:33:30Z", "name": "<PERSON><PERSON> for TESTING - Employee On-boarding", "delivery_time": null, "display_id": 295, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "59": {"id": ***********, "created_at": "2022-01-19T20:32:32Z", "updated_at": "2024-08-16T21:33:07Z", "name": "Microsoft DBC - Account Termination", "delivery_time": null, "display_id": 185, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "60": {"id": ***********, "created_at": "2022-08-04T19:10:56Z", "updated_at": "2024-08-16T21:33:16Z", "name": "MII ", "delivery_time": null, "display_id": 216, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "61": {"id": 19001183217, "created_at": "2023-09-13T15:04:32Z", "updated_at": "2024-08-16T21:33:40Z", "name": "(OLD) Employee On-boarding 2023-09-13", "delivery_time": null, "display_id": 331, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-newhire-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "On-board {{custom_field.cf_employee_first_name}} {{custom_field.cf_employee_last_name}} ({{custom_field.cf_employee_start_date}}) - {{custom_field.cf_employee_type}} {{custom_field.cf_locations}}"}}, "62": {"id": 19001167333, "created_at": "2022-11-25T15:11:35Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Onboarding TEST", "delivery_time": null, "display_id": 241, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "63": {"id": 19001143494, "created_at": "2022-01-19T20:21:21Z", "updated_at": "2024-08-16T21:33:07Z", "name": "Power BI - License Removal", "delivery_time": null, "display_id": 181, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "{{item.name}} - {{custom_field.cf_user_account}} | {{custom_field.cf_user_account_if_not_available_in_user_dropdown}} : Request for {{requested_for}} by {{requestor.name}}: "}}, "64": {"id": ***********, "created_at": "2022-12-06T02:38:58Z", "updated_at": "2024-08-16T21:33:20Z", "name": "Printer <PERSON>up", "delivery_time": null, "display_id": 245, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "65": {"id": ***********, "created_at": "2022-07-21T15:23:59Z", "updated_at": "2024-08-16T21:33:15Z", "name": "Quickbooks - AWIP Access & Permissions", "delivery_time": null, "display_id": 211, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "66": {"id": 19001143485, "created_at": "2022-01-19T19:58:21Z", "updated_at": "2024-08-16T21:33:07Z", "name": "Salesforce CRM - Termination", "delivery_time": null, "display_id": 180, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "67": {"id": ***********, "created_at": "2022-01-19T20:31:25Z", "updated_at": "2024-08-16T21:33:07Z", "name": "SAP - AWIP - Account Termination", "delivery_time": null, "display_id": 184, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "68": {"id": ***********, "created_at": "2022-01-19T20:27:29Z", "updated_at": "2024-08-16T21:33:07Z", "name": "SAP B1 - <PERSON><PERSON> - Account Termination", "delivery_time": null, "display_id": 183, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "69": {"id": ***********, "created_at": "2022-01-20T13:45:14Z", "updated_at": "2024-08-16T21:33:07Z", "name": "SAP ECC - Vicwest Account Termination", "delivery_time": null, "display_id": 187, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "70": {"id": ***********, "created_at": "2022-01-19T20:24:26Z", "updated_at": "2024-08-16T21:33:07Z", "name": "SAP S/4 HANA - Account Termination", "delivery_time": null, "display_id": 182, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "71": {"id": ***********, "created_at": "2021-10-26T15:12:40Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Software Requests", "delivery_time": null, "display_id": 73, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "72": {"id": 19001183218, "created_at": "2023-09-13T15:13:55Z", "updated_at": "2024-08-16T21:33:40Z", "name": "TEST Employee On-boarding 2023-09-13", "delivery_time": null, "display_id": 332, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-newhire-new", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": true, "configs": {"attachment_mandatory": false, "subject": "On-board {{custom_field.cf_employee_first_name}} {{custom_field.cf_employee_last_name}} ({{custom_field.cf_employee_start_date}}) - {{custom_field.cf_employee_type}} {{custom_field.cf_locations}}"}}, "73": {"id": 19001143580, "created_at": "2022-01-20T12:43:12Z", "updated_at": "2024-08-16T21:33:07Z", "name": "XA / AS400 - Termination", "delivery_time": 8, "display_id": 186, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "74": {"id": 19001172383, "created_at": "2023-04-19T21:56:28Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Asset Tags", "delivery_time": null, "display_id": 278, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "75": {"id": 19001197344, "created_at": "2024-05-15T17:35:47Z", "updated_at": "2024-08-16T21:33:52Z", "name": "CHANGE a Site Plant, Office or Facility", "delivery_time": null, "display_id": 384, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_title_for_description}} | {{item.name}}"}}, "76": {"id": 19001158777, "created_at": "2022-06-07T15:40:47Z", "updated_at": "2024-08-16T21:33:10Z", "name": "Conference Room - Add, Change or Remove an Office 365 Outlook Room", "delivery_time": null, "display_id": 204, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "77": {"id": 19000889851, "created_at": "2021-11-29T22:23:33Z", "updated_at": "2024-08-16T21:32:59Z", "name": "Equipment Disposal", "delivery_time": null, "display_id": 156, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}}: {{custom_field.cf_disposal_pickup_site}} - {{item.name}}"}}, "78": {"id": 19001171114, "created_at": "2023-03-13T17:10:56Z", "updated_at": "2024-08-16T21:33:28Z", "name": "IT Site Visit or Meeting", "delivery_time": null, "display_id": 270, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "79": {"id": 19000446213, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:53Z", "name": "Meeting Room", "delivery_time": null, "display_id": 23, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-meeting-room-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "80": {"id": 19001150420, "created_at": "2022-03-17T13:11:41Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Mobile Phone - Number and/or Asset Transfer", "delivery_time": null, "display_id": 194, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "81": {"id": 19000958094, "created_at": "2021-12-10T17:34:17Z", "updated_at": "2024-08-16T21:33:04Z", "name": "Network Cable Install", "delivery_time": null, "display_id": 159, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "82": {"id": 19001197343, "created_at": "2024-05-15T17:33:55Z", "updated_at": "2024-08-16T21:33:52Z", "name": "NEW Site Plant, Office or Facility", "delivery_time": null, "display_id": 383, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_title_for_description}} | {{item.name}}"}}, "83": {"id": 19001198029, "created_at": "2024-05-24T19:43:04Z", "updated_at": "2024-08-16T21:33:52Z", "name": "Site Circuit ADD - Internet Access", "delivery_time": null, "display_id": 387, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "84": {"id": 19001198032, "created_at": "2024-05-24T19:59:09Z", "updated_at": "2024-08-16T21:33:53Z", "name": "Site Circuit CHANGE - Internet Access", "delivery_time": null, "display_id": 389, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "85": {"id": 19001198031, "created_at": "2024-05-24T19:57:34Z", "updated_at": "2024-08-16T21:33:52Z", "name": "Site Circuit REMOVE - Internet Access", "delivery_time": null, "display_id": 388, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "86": {"id": 19000446212, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:52Z", "name": "Workstation Setup", "delivery_time": null, "display_id": 22, "category_id": 19000095374, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-workstation-setup-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": null, "delivery_time_visibility": null, "allow_attachments": false, "allow_quantity": null, "is_bundle": false, "create_child": false, "configs": null}, "87": {"id": 19001168912, "created_at": "2023-01-16T14:30:15Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Computer - MAC", "delivery_time": null, "display_id": 254, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472048, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "88": {"id": 19001169485, "created_at": "2023-01-30T23:05:32Z", "updated_at": "2024-08-16T21:33:22Z", "name": "Computer - Non-Standard Laptop or Desktop", "delivery_time": null, "display_id": 257, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_model_number}} - {{item.name}}"}}, "89": {"id": 19000586509, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Desk Phone", "delivery_time": null, "display_id": 68, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472060, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "90": {"id": 19000714328, "created_at": "2021-10-29T20:21:36Z", "updated_at": "2024-10-15T03:44:58Z", "name": "Desktop - Engineering/Drafting", "delivery_time": null, "display_id": 103, "category_id": 19000095369, "product_id": 19000094866, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472044, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "91": {"id": 19000736139, "created_at": "2021-11-03T17:40:57Z", "updated_at": "2024-10-15T03:44:59Z", "name": "Desktop - Standard", "delivery_time": null, "display_id": 113, "category_id": 19000095369, "product_id": 19000094866, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472044, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "92": {"id": 19001157104, "created_at": "2022-05-16T21:48:37Z", "updated_at": "2024-08-16T21:33:09Z", "name": "Desktop - Standard (Non-Boarding)", "delivery_time": null, "display_id": 201, "category_id": 19000095369, "product_id": 19000094866, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472044, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "93": {"id": 19000725839, "created_at": "2021-11-01T18:40:45Z", "updated_at": "2024-10-15T03:44:58Z", "name": "Docking Station", "delivery_time": null, "display_id": 104, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472043, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "94": {"id": 19001180851, "created_at": "2023-08-17T20:56:48Z", "updated_at": "2024-08-16T21:33:39Z", "name": "Document Scanning to SharePoint", "delivery_time": null, "display_id": 326, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "95": {"id": 19001162394, "created_at": "2022-08-09T23:39:06Z", "updated_at": "2024-08-16T21:33:16Z", "name": "Internal Drive - Hard Drive or Solid State Drive (SSD)", "delivery_time": null, "display_id": 220, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "96": {"id": 19001166265, "created_at": "2022-10-31T18:42:00Z", "updated_at": "2024-08-16T21:33:18Z", "name": "IT Parts & Accessories", "delivery_time": null, "display_id": 233, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_subject}} - {{item.name}}"}}, "97": {"id": 19000586512, "created_at": "2021-10-08T14:55:32Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Keyboard and Mouse", "delivery_time": null, "display_id": 71, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "98": {"id": 19000737693, "created_at": "2021-11-03T23:22:46Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Laptop - Engineering/Drafting", "delivery_time": null, "display_id": 127, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "99": {"id": 19001192133, "created_at": "2024-02-09T15:41:16Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Laptop - Engineering/Drafting (Non-Bundled)", "delivery_time": null, "display_id": 367, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "100": {"id": 19000737692, "created_at": "2021-11-03T23:18:22Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Laptop - Standard", "delivery_time": null, "display_id": 126, "category_id": 19000095369, "product_id": 19000079995, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472065, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "101": {"id": 19001007592, "created_at": "2021-12-20T19:21:46Z", "updated_at": "2024-09-20T20:23:57Z", "name": "Laptop - Standard (Non-Bundled)", "delivery_time": null, "display_id": 169, "category_id": 19000095369, "product_id": 19000079995, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472065, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "102": {"id": 19001161900, "created_at": "2022-08-02T16:07:59Z", "updated_at": "2024-08-16T21:33:15Z", "name": "Laptop Battery", "delivery_time": null, "display_id": 213, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "103": {"id": 19001193336, "created_at": "2024-02-26T18:16:33Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Laptop Charger", "delivery_time": null, "display_id": 368, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "104": {"id": 19000726038, "created_at": "2021-11-01T19:16:46Z", "updated_at": "2024-10-15T03:44:58Z", "name": "Memory Upgrade", "delivery_time": null, "display_id": 107, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472048, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "105": {"id": 19001170109, "created_at": "2023-02-16T15:25:23Z", "updated_at": "2024-08-16T21:33:27Z", "name": "Mobile Data - BYOD", "delivery_time": null, "display_id": 263, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "106": {"id": 19001169843, "created_at": "2023-02-08T17:56:49Z", "updated_at": "2024-08-16T21:33:27Z", "name": "Mobile Phone - Change Plan/Features", "delivery_time": null, "display_id": 259, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "107": {"id": 19000737593, "created_at": "2021-11-03T22:24:43Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Mobile Phone - New, Reassign or Replace", "delivery_time": null, "display_id": 121, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472051, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} ", "auto_gen_document": false}}, "108": {"id": 19000826847, "created_at": "2021-11-19T20:56:09Z", "updated_at": "2024-08-16T21:32:58Z", "name": "Mobile Phone - Report Lost or Stolen", "delivery_time": null, "display_id": 148, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472051, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "109": {"id": 19000446204, "created_at": "2021-09-08T17:00:31Z", "updated_at": "2024-10-15T03:44:52Z", "name": "Monitor", "delivery_time": null, "display_id": 14, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19001412194, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "110": {"id": 19001166160, "created_at": "2022-10-28T16:52:02Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Network Equipment & Accessories", "delivery_time": null, "display_id": 231, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_subject}} - {{item.name}}"}}, "111": {"id": 19001171274, "created_at": "2023-03-17T15:55:07Z", "updated_at": "2024-08-16T21:33:28Z", "name": "Networking - MAC", "delivery_time": null, "display_id": 273, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472043, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "112": {"id": 19000747713, "created_at": "2021-11-05T19:39:16Z", "updated_at": "2024-08-16T21:32:55Z", "name": "Peripheral Hardware Bundle Purchasing", "delivery_time": null, "display_id": 132, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "113": {"id": 19001200259, "created_at": "2024-07-17T19:41:10Z", "updated_at": "2024-08-16T21:33:56Z", "name": "Power Supply - Laptop", "delivery_time": null, "display_id": 401, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "114": {"id": 19000747716, "created_at": "2021-11-05T19:46:57Z", "updated_at": "2024-08-16T21:32:55Z", "name": "Printer", "delivery_time": null, "display_id": 133, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472053, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "115": {"id": 19001168023, "created_at": "2022-12-13T22:15:24Z", "updated_at": "2024-09-18T17:21:41Z", "name": "Printer - MAC", "delivery_time": null, "display_id": 247, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "116": {"id": 19000990255, "created_at": "2021-12-16T14:49:29Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Printer <PERSON>l", "delivery_time": null, "display_id": 165, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "117": {"id": 19000446220, "created_at": "2021-09-08T17:00:32Z", "updated_at": "2024-10-15T03:44:53Z", "name": "Recover Company Assets", "delivery_time": null, "display_id": 30, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-recover-company-assets-new", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "118": {"id": 19001204110, "created_at": "2024-10-03T13:49:36Z", "updated_at": "2024-10-03T17:54:01Z", "name": "Security Key / Token", "delivery_time": null, "display_id": 441, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request by {{requestor.name}} for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "119": {"id": 19000737602, "created_at": "2021-11-03T22:48:06Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Speaker", "delivery_time": null, "display_id": 123, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472060, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "120": {"id": 19000737696, "created_at": "2021-11-03T23:46:34Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Tablet", "delivery_time": null, "display_id": 128, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472051, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} {{custom_field.cf_ipad_type_selection}}", "auto_gen_document": false}}, "121": {"id": 19001162657, "created_at": "2022-08-15T20:56:42Z", "updated_at": "2024-08-16T21:33:17Z", "name": "Test - consumable Docking Station", "delivery_time": null, "display_id": 224, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19001407250, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "122": {"id": 19001181364, "created_at": "2023-08-21T20:33:05Z", "updated_at": "2024-08-16T21:33:39Z", "name": "UPS - Uninterruptable Power Supply", "delivery_time": null, "display_id": 327, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "123": {"id": 19001161269, "created_at": "2022-07-21T14:55:32Z", "updated_at": "2024-08-16T21:33:15Z", "name": "Video Conferencing System - Teams Room for Boardrooms", "delivery_time": null, "display_id": 210, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} - {{custom_field.cf_location}}"}}, "124": {"id": 19000737604, "created_at": "2021-11-03T22:54:47Z", "updated_at": "2024-08-16T21:32:54Z", "name": "Web Camera", "delivery_time": null, "display_id": 124, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472060, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "125": {"id": 19001181479, "created_at": "2023-08-22T23:46:41Z", "updated_at": "2024-08-16T21:33:40Z", "name": "Wireless Barcode Scanner", "delivery_time": null, "display_id": 328, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "126": {"id": 19000737599, "created_at": "2021-11-03T22:35:55Z", "updated_at": "2024-10-03T19:10:23Z", "name": "Wireless Headset", "delivery_time": null, "display_id": 122, "category_id": 19000095369, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472060, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "127": {"id": 19001173158, "created_at": "2023-05-11T20:02:33Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Employee Onboarding - Azure AD", "delivery_time": null, "display_id": 280, "category_id": 19000247593, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-azuread_si", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": null}, "128": {"id": 19001173085, "created_at": "2023-05-10T18:35:31Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Employee Onboarding - MS Active Directory", "delivery_time": null, "display_id": 279, "category_id": 19000247593, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-microsoftad_si", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": null}, "129": {"id": 19001196603, "created_at": "2024-05-01T14:05:58Z", "updated_at": "2024-08-16T21:33:51Z", "name": "3rd Party Supplier - Verify Compliance Check", "delivery_time": null, "display_id": 381, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "130": {"id": 19001197431, "created_at": "2024-05-16T14:46:05Z", "updated_at": "2024-08-16T21:33:52Z", "name": "Audit - Factory Controls - IT Controls", "delivery_time": null, "display_id": 385, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_title}} | {{item.name}}"}}, "131": {"id": 19001180764, "created_at": "2023-08-16T22:45:12Z", "updated_at": "2024-08-16T21:33:39Z", "name": "ITGC - Recurring IT General Control", "delivery_time": null, "display_id": 325, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "132": {"id": 19001200813, "created_at": "2024-07-29T18:51:07Z", "updated_at": "2024-08-16T21:34:04Z", "name": "Onboard a New IT Services Supplier or Vendor", "delivery_time": null, "display_id": 427, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_supplier_legal_name}}; {{custom_field.cf_supplier_doing_business_as_name}} | {{item.name}}"}}, "133": {"id": ***********, "created_at": "2024-03-19T19:57:21Z", "updated_at": "2024-08-16T21:33:50Z", "name": "User Access Controls (UAC) Review for System/Application", "delivery_time": null, "display_id": 374, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} - {{item.name}}"}}, "134": {"id": ***********, "created_at": "2024-09-23T16:47:58Z", "updated_at": "2024-09-23T20:53:06Z", "name": "Global Block List", "delivery_time": null, "display_id": 439, "category_id": 19000251815, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "signature_list": []}}, "135": {"id": 19001201153, "created_at": "2024-08-06T15:57:03Z", "updated_at": "2024-08-16T21:34:05Z", "name": "HR - Active Directory reconcilliation", "delivery_time": null, "display_id": 428, "category_id": 19000251815, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "136": {"id": 19001199747, "created_at": "2024-07-01T17:57:35Z", "updated_at": "2024-08-20T17:26:46Z", "name": "KA IT Site Visit", "delivery_time": null, "display_id": 395, "category_id": 19000251815, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Site visit planning for {{custom_field.cf_location}} on {{custom_field.cf_scheduled_travel_to}}"}}, "137": {"id": 19000888191, "created_at": "2021-11-29T18:02:37Z", "updated_at": "2024-08-16T21:32:59Z", "name": "SAP KIP - New Vendor", "delivery_time": null, "display_id": 155, "category_id": 19000189259, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": true, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "138": {"id": 19001202269, "created_at": "2024-08-26T13:12:46Z", "updated_at": "2024-08-26T17:12:47Z", "name": "PRTG Service Monitor Setup", "delivery_time": null, "display_id": 436, "category_id": 19000250217, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "139": {"id": 19001187085, "created_at": "2023-11-10T17:30:17Z", "updated_at": "2024-08-16T21:33:42Z", "name": "VPN - Site to Site Network Connection", "delivery_time": null, "display_id": 345, "category_id": 19000250217, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "140": {"id": 19001204987, "created_at": "2024-10-21T14:51:08Z", "updated_at": "2024-10-21T18:51:52Z", "name": "Azure Reservations", "delivery_time": null, "display_id": 442, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "141": {"id": 19001199563, "created_at": "2024-06-25T13:42:08Z", "updated_at": "2024-08-16T21:33:53Z", "name": "Legal Hold", "delivery_time": null, "display_id": 392, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "142": {"id": 19001201872, "created_at": "2024-08-15T18:17:43Z", "updated_at": "2024-08-16T21:34:05Z", "name": "Planned Outage & Maintenance", "delivery_time": null, "display_id": 434, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_brief_description_for_ticket_title}} | {{custom_field.cf_business}}, {{custom_field.cf_location_or_site}}, {{custom_field.cf_start_date_amp_time}} to {{custom_field.cf_end_date_amp_time}} | {{item.name}}"}}, "143": {"id": ***********, "created_at": "2023-01-03T22:56:47Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Professional Services", "delivery_time": null, "display_id": 251, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_description}} - {{item.name}}"}}, "144": {"id": ***********, "created_at": "2023-09-13T15:28:05Z", "updated_at": "2024-08-16T21:33:41Z", "name": "Service Contract Renewal", "delivery_time": null, "display_id": 333, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_system_or_service}} - {{item.name}}"}}, "145": {"id": 19001197736, "created_at": "2024-05-20T15:01:06Z", "updated_at": "2024-08-16T21:33:52Z", "name": "Infrastructure procurement", "delivery_time": null, "display_id": 386, "category_id": 19000251647, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Purchase Request for {{custom_field.cf_project_name}} | {{custom_field.cf_date_needed}}"}}, "146": {"id": 19001195377, "created_at": "2024-04-08T13:25:20Z", "updated_at": "2024-08-16T21:33:51Z", "name": "IT Expensed Purchase Request", "delivery_time": null, "display_id": 377, "category_id": 19000251647, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "147": {"id": 19001196092, "created_at": "2024-04-19T18:42:55Z", "updated_at": "2024-10-31T14:17:45Z", "name": "KA IT New Vendor Setup [DISABLED DO NOT USE]", "delivery_time": null, "display_id": 380, "category_id": 19000251647, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Vendor setup: {{custom_field.cf_vendor}}"}}, "148": {"id": 19001162620, "created_at": "2022-08-15T14:34:17Z", "updated_at": "2024-10-28T18:39:23Z", "name": "Allow List - Allow a Domain", "delivery_time": null, "display_id": 223, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} - {{custom_field.cf_domain_names}}", "auto_gen_document": false}}, "149": {"id": 19001170780, "created_at": "2023-03-03T02:23:54Z", "updated_at": "2024-08-16T21:33:28Z", "name": "Certificate ", "delivery_time": null, "display_id": 266, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "150": {"id": 19001199586, "created_at": "2024-06-25T14:47:20Z", "updated_at": "2024-08-16T21:33:54Z", "name": "Crowdstrike - Falcon", "delivery_time": null, "display_id": 393, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "151": {"id": 19001174593, "created_at": "2023-06-13T23:41:56Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Endpoint Encryption", "delivery_time": null, "display_id": 286, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "152": {"id": ***********, "created_at": "2023-05-22T12:44:51Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Inactive account - Action required SEC-UAC-01-AD", "delivery_time": null, "display_id": 281, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_all_users_dropdown}} | {{item.name}}", "auto_gen_document": false}}, "153": {"id": ***********, "created_at": "2024-03-08T21:26:54Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Mimecast", "delivery_time": null, "display_id": 373, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "154": {"id": 19001185403, "created_at": "2023-10-16T17:31:59Z", "updated_at": "2024-08-16T21:33:41Z", "name": "Multifactor Authentication", "delivery_time": null, "display_id": 338, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "155": {"id": 19001164401, "created_at": "2022-09-22T18:15:55Z", "updated_at": "2024-08-16T21:33:17Z", "name": "Network Access - Allow Device Access to the Network", "delivery_time": null, "display_id": 228, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "156": {"id": 19001168604, "created_at": "2023-01-05T22:59:05Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Single Sign-On Request - SSO", "delivery_time": null, "display_id": 253, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "157": {"id": ***********, "created_at": "2023-05-29T17:00:59Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Terminated account - Immediate action required SEC-UAC-05-AD", "delivery_time": null, "display_id": 283, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_all_users_dropdown}} | {{item.name}}", "auto_gen_document": false}}, "158": {"id": ***********, "created_at": "2023-03-21T14:14:18Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Disk space provisioning on servers", "delivery_time": null, "display_id": 275, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "159": {"id": 19001171016, "created_at": "2023-03-08T21:20:12Z", "updated_at": "2024-08-16T21:33:28Z", "name": "Server", "delivery_time": null, "display_id": 269, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "160": {"id": 19001178162, "created_at": "2023-07-27T12:34:52Z", "updated_at": "2024-08-16T21:33:31Z", "name": "Web Server", "delivery_time": null, "display_id": 297, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "161": {"id": 19001200140, "created_at": "2024-07-12T14:05:41Z", "updated_at": "2024-08-16T21:33:56Z", "name": "3D Printer - Slicer Software Request", "delivery_time": null, "display_id": 398, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472043, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "162": {"id": 19000692968, "created_at": "2021-10-26T18:57:33Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Adobe - Acrobat DC Pro", "delivery_time": null, "display_id": 79, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "163": {"id": 19000693012, "created_at": "2021-10-26T18:59:55Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Adobe - Acrobat Reader", "delivery_time": null, "display_id": 80, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "164": {"id": 19000699320, "created_at": "2021-10-27T16:02:52Z", "updated_at": "2024-10-15T03:44:57Z", "name": "Adobe - Creative Cloud", "delivery_time": null, "display_id": 93, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "165": {"id": 19000736138, "created_at": "2021-11-03T17:30:39Z", "updated_at": "2024-10-15T03:44:59Z", "name": "Adobe - Illustrator", "delivery_time": null, "display_id": 112, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "166": {"id": 19000699276, "created_at": "2021-10-27T16:00:39Z", "updated_at": "2024-10-15T03:44:57Z", "name": "Adobe - Photoshop", "delivery_time": null, "display_id": 92, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "167": {"id": 19001166869, "created_at": "2022-11-16T14:37:12Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Adobe Sign", "delivery_time": null, "display_id": 238, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "168": {"id": 19001184610, "created_at": "2023-10-03T20:49:13Z", "updated_at": "2024-08-16T21:33:41Z", "name": "AMS Eclipse Node", "delivery_time": null, "display_id": 337, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "169": {"id": 19001162361, "created_at": "2022-08-09T17:32:22Z", "updated_at": "2024-08-16T21:33:16Z", "name": "Application Update Request", "delivery_time": null, "display_id": 219, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "170": {"id": 19001168526, "created_at": "2023-01-04T17:27:48Z", "updated_at": "2024-08-16T21:33:21Z", "name": "AS400 Client Install", "delivery_time": null, "display_id": 252, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "171": {"id": 19000698709, "created_at": "2021-10-27T15:17:02Z", "updated_at": "2024-10-15T03:44:56Z", "name": "AutoDesk - AEC Collection - Subscription", "delivery_time": null, "display_id": 85, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "172": {"id": 19000693184, "created_at": "2021-10-26T19:11:38Z", "updated_at": "2024-10-15T03:44:56Z", "name": "AutoDesk - AutoCAD LT - Subscription", "delivery_time": null, "display_id": 82, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "173": {"id": 19000693183, "created_at": "2021-10-26T19:09:20Z", "updated_at": "2024-10-15T03:44:55Z", "name": "AutoDesk - AutoCAD Standard - Subscription", "delivery_time": null, "display_id": 81, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "174": {"id": 19001190626, "created_at": "2024-01-22T19:33:13Z", "updated_at": "2024-08-16T21:33:49Z", "name": "AutoDesk - AutoCAD Standard - Flex Token", "delivery_time": null, "display_id": 359, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "175": {"id": 19000698799, "created_at": "2021-10-27T15:23:37Z", "updated_at": "2024-10-15T03:44:56Z", "name": "AutoDesk - BIM", "delivery_time": null, "display_id": 86, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "176": {"id": 19001177299, "created_at": "2023-07-20T20:47:24Z", "updated_at": "2024-08-16T21:33:31Z", "name": "AutoDesk - DWG TrueView", "delivery_time": null, "display_id": 296, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "177": {"id": 19001190459, "created_at": "2024-01-17T16:58:44Z", "updated_at": "2024-08-16T21:33:44Z", "name": "AutoDesk - Inventor Pro - Flex Token", "delivery_time": null, "display_id": 356, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "178": {"id": 19000698802, "created_at": "2021-10-27T15:26:36Z", "updated_at": "2024-10-15T03:44:57Z", "name": "AutoDesk - Inventor Pro - Subscription", "delivery_time": null, "display_id": 87, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "179": {"id": 19001190461, "created_at": "2024-01-17T17:09:09Z", "updated_at": "2024-08-16T21:33:44Z", "name": "AutoDesk - Revit - Flex Token", "delivery_time": null, "display_id": 357, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "180": {"id": 19000693187, "created_at": "2021-10-26T19:14:27Z", "updated_at": "2024-10-15T03:44:56Z", "name": "AutoDesk - Revit - Subscription", "delivery_time": null, "display_id": 83, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "181": {"id": 19000693317, "created_at": "2021-10-26T19:22:27Z", "updated_at": "2024-10-15T03:44:56Z", "name": "AutoDesk - Revit LT  - Subscription", "delivery_time": null, "display_id": 84, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "182": {"id": 19001168170, "created_at": "2022-12-16T17:17:15Z", "updated_at": "2024-08-16T21:33:20Z", "name": "AutoDesk Software Install", "delivery_time": null, "display_id": 248, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "183": {"id": ***********, "created_at": "2021-10-26T16:18:50Z", "updated_at": "2024-10-15T03:44:55Z", "name": "Big Tin Can -  License Account", "delivery_time": null, "display_id": 78, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "184": {"id": ***********, "created_at": "2022-01-19T19:02:18Z", "updated_at": "2024-08-16T21:33:06Z", "name": "Big Tin Can License Account - Termination", "delivery_time": null, "display_id": 179, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "185": {"id": ***********, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:53Z", "name": "BlueBeam Annual Subscription Plans", "delivery_time": null, "display_id": 59, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_bluebeam_version_release}} - {{item.name}}"}}, "186": {"id": 19000586503, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Browser", "delivery_time": null, "display_id": 62, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "187": {"id": 19001156435, "created_at": "2022-05-10T21:36:45Z", "updated_at": "2024-08-16T21:33:09Z", "name": "Comshare Access", "delivery_time": null, "display_id": 200, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "188": {"id": 19001153688, "created_at": "2022-04-12T14:39:53Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Convergence/Vector Password Reset", "delivery_time": null, "display_id": 196, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "189": {"id": 19001169935, "created_at": "2023-02-10T15:01:06Z", "updated_at": "2024-08-16T21:33:27Z", "name": "Deploy - Automated deployment of Software, Script or Configuration ", "delivery_time": null, "display_id": 260, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_program_script_or_project_name}} | {{item.name}}", "auto_gen_document": false}}, "190": {"id": 19000699372, "created_at": "2021-10-27T16:14:34Z", "updated_at": "2024-10-15T03:44:57Z", "name": "GlobalProtect Application Installation", "delivery_time": null, "display_id": 95, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "191": {"id": 19000736434, "created_at": "2021-11-03T18:10:22Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Jabber Application Install", "delivery_time": null, "display_id": 114, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "192": {"id": 19001153689, "created_at": "2022-04-12T14:53:37Z", "updated_at": "2024-08-16T21:33:09Z", "name": "KIP - SOP Upload Request", "delivery_time": null, "display_id": 197, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": true, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "193": {"id": 19000699365, "created_at": "2021-10-27T16:06:19Z", "updated_at": "2024-10-15T03:44:57Z", "name": "Microsoft - Navision", "delivery_time": null, "display_id": 94, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "194": {"id": 19000736438, "created_at": "2021-11-03T18:22:11Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Microsoft - Project & Planner Subscription", "delivery_time": null, "display_id": 116, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request by {{requestor.name}} for {{requested_for}} : {{custom_field.cf_license}} | {{item.name}}", "auto_gen_document": false}}, "195": {"id": 19000736437, "created_at": "2021-11-03T18:22:07Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Microsoft - Teams Desktop Software", "delivery_time": null, "display_id": 115, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "196": {"id": 19000586504, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:54Z", "name": "Microsoft Office", "delivery_time": null, "display_id": 63, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "197": {"id": 19001173597, "created_at": "2023-05-23T20:32:33Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Microsoft Office 365 Platform License", "delivery_time": null, "display_id": 282, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "198": {"id": 19000736481, "created_at": "2021-11-03T18:26:04Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Microsoft Visio - Flowchart Maker and Diagramming Software", "delivery_time": null, "display_id": 117, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "199": {"id": 19001162106, "created_at": "2022-08-04T19:22:40Z", "updated_at": "2024-08-16T21:33:16Z", "name": "MII - Password Reset", "delivery_time": null, "display_id": 217, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "200": {"id": 19000736524, "created_at": "2021-11-03T18:36:17Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Non-Standard Application - License or Purchase", "delivery_time": null, "display_id": 118, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_application_name}} - {{item.name}}", "auto_gen_document": false}}, "201": {"id": 19000736525, "created_at": "2021-11-03T18:39:31Z", "updated_at": "2024-08-16T21:32:53Z", "name": "Non-Standard Application - No License Required", "delivery_time": null, "display_id": 119, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "202": {"id": 19001198793, "created_at": "2024-06-10T15:00:27Z", "updated_at": "2024-08-16T21:33:53Z", "name": "One Note", "delivery_time": null, "display_id": 391, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "203": {"id": 19000586506, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:54Z", "name": "OneDrive Install", "delivery_time": null, "display_id": 65, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "204": {"id": 19001191644, "created_at": "2024-02-01T18:15:35Z", "updated_at": "2024-08-16T21:33:49Z", "name": "Power Automate - Power Platform - Subscription", "delivery_time": null, "display_id": 363, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "205": {"id": 19001147997, "created_at": "2022-02-24T00:58:01Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Power BI Desktop - Windows App Data Modelling Software", "delivery_time": null, "display_id": 192, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "206": {"id": 19001161274, "created_at": "2022-07-21T15:41:50Z", "updated_at": "2024-08-16T21:33:15Z", "name": "PowerApps - AWIP", "delivery_time": null, "display_id": 212, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "207": {"id": 19001187850, "created_at": "2023-11-29T19:33:36Z", "updated_at": "2024-08-16T21:33:42Z", "name": "PowerPlatform - On-premise Gateway", "delivery_time": null, "display_id": 349, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "208": {"id": 19000986640, "created_at": "2021-12-15T22:46:49Z", "updated_at": "2024-08-16T21:33:05Z", "name": "QuickBooks Client Install/Upgrade", "delivery_time": null, "display_id": 164, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "209": {"id": 19001151407, "created_at": "2022-03-28T16:28:52Z", "updated_at": "2024-08-16T21:33:08Z", "name": "QuickBooks Enterprise SERVER Install/Upgrade", "delivery_time": null, "display_id": 195, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "210": {"id": 19001182437, "created_at": "2023-08-31T16:29:12Z", "updated_at": "2024-08-16T21:33:40Z", "name": "SAP GUI - Install Software", "delivery_time": null, "display_id": 330, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} - {{custom_field.sap_environment}}"}}, "211": {"id": 19001185590, "created_at": "2023-10-19T17:28:43Z", "updated_at": "2024-08-16T21:33:41Z", "name": "Shop Floor Plus", "delivery_time": null, "display_id": 339, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "212": {"id": 19001185717, "created_at": "2023-10-23T12:42:25Z", "updated_at": "2024-08-16T21:33:41Z", "name": "Shop Floor Plus Password Reset", "delivery_time": null, "display_id": 340, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "213": {"id": 19001169963, "created_at": "2023-02-10T22:51:57Z", "updated_at": "2024-08-16T21:33:27Z", "name": "Siesa - Access", "delivery_time": null, "display_id": 261, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "214": {"id": 19001175330, "created_at": "2023-06-23T13:29:22Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Siesa - License Removal & Permits", "delivery_time": null, "display_id": 289, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "{{item.name}} - {{custom_field.cf_user_account}} | {{custom_field.cf_user_account_if_not_available_in_user_dropdown}} : Request for {{requested_for}} by {{requestor.name}}: "}}, "215": {"id": ***********, "created_at": "2021-10-08T14:55:31Z", "updated_at": "2024-10-15T03:44:53Z", "name": "SketchUp", "delivery_time": null, "display_id": 55, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "216": {"id": ***********, "created_at": "2022-01-28T21:03:01Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Snagit - Screen Capture and Recording Software", "delivery_time": null, "display_id": 190, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "217": {"id": 19001167654, "created_at": "2022-12-02T19:53:30Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Soft Phone", "delivery_time": null, "display_id": 244, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "218": {"id": 19001167512, "created_at": "2022-11-30T22:00:36Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Software License Renewal", "delivery_time": null, "display_id": 242, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_software_name}} - {{item.name}}"}}, "219": {"id": 19000996031, "created_at": "2021-12-17T13:44:37Z", "updated_at": "2024-08-16T21:33:05Z", "name": "SolidWorks - E-Drawings Viewer", "delivery_time": null, "display_id": 166, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "220": {"id": 19000698930, "created_at": "2021-10-27T15:32:52Z", "updated_at": "2024-10-15T03:44:57Z", "name": "SolidWorks - License and Subscription", "delivery_time": null, "display_id": 88, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "221": {"id": 19000699148, "created_at": "2021-10-27T15:51:15Z", "updated_at": "2024-10-15T03:44:57Z", "name": "SolidWorks - PDM", "delivery_time": null, "display_id": 91, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "222": {"id": 19001166713, "created_at": "2022-11-12T02:25:28Z", "updated_at": "2024-08-16T21:33:18Z", "name": "SuSE - License and Subscription", "delivery_time": null, "display_id": 237, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "223": {"id": 19001193958, "created_at": "2024-03-06T21:36:14Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Tagetik - TGK", "delivery_time": null, "display_id": 372, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "224": {"id": 19001193828, "created_at": "2024-03-05T18:24:50Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Tanium - RMM - Agent Install", "delivery_time": null, "display_id": 370, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "225": {"id": 19000954438, "created_at": "2021-12-09T22:52:18Z", "updated_at": "2024-08-16T21:33:04Z", "name": "Tekla Structures Software", "delivery_time": null, "display_id": 158, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "226": {"id": 19001175314, "created_at": "2023-06-22T19:30:53Z", "updated_at": "2024-08-16T21:33:30Z", "name": "<PERSON><PERSON><PERSON><PERSON>", "delivery_time": null, "display_id": 288, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "227": {"id": 19001168246, "created_at": "2022-12-20T18:18:57Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Twinmotion - 3D architectural visualization tool", "delivery_time": null, "display_id": 249, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "228": {"id": 19001200258, "created_at": "2024-07-17T19:34:26Z", "updated_at": "2024-08-16T21:33:56Z", "name": "Uninstall Software - Tanium Deploy Package", "delivery_time": null, "display_id": 400, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "229": {"id": 19001199845, "created_at": "2024-07-02T13:19:11Z", "updated_at": "2024-08-16T21:33:56Z", "name": "uniPoint - QMS", "delivery_time": null, "display_id": 396, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "230": {"id": 19001178533, "created_at": "2023-07-28T19:13:20Z", "updated_at": "2024-08-16T21:33:31Z", "name": "Visual Studio License", "delivery_time": null, "display_id": 298, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "231": {"id": 19001200193, "created_at": "2024-07-15T13:00:17Z", "updated_at": "2024-08-16T21:33:56Z", "name": "Yodeck - Access and Controls", "delivery_time": null, "display_id": 399, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "232": {"id": 19000857409, "created_at": "2021-11-24T17:30:15Z", "updated_at": "2024-08-16T21:32:59Z", "name": "Access & Permissions - SharePoint & Teams and Office 365 Outlook Groups", "delivery_time": null, "display_id": 153, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "233": {"id": 19001162488, "created_at": "2022-08-11T16:48:42Z", "updated_at": "2024-08-16T21:33:16Z", "name": "AD User Group - Distribution & Security", "delivery_time": null, "display_id": 221, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} | {{custom_field.cf_requested_group_name}} | {{custom_field.cf_existing_group_name}}"}}, "234": {"id": 19001005865, "created_at": "2021-12-20T14:12:50Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Autodesk - Navisworks Freedom 3D Viewer", "delivery_time": null, "display_id": 168, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "235": {"id": 19001162763, "created_at": "2022-08-17T19:48:07Z", "updated_at": "2024-08-16T21:33:17Z", "name": "AWIP Company Drive - Access & Permissions", "delivery_time": null, "display_id": 225, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "236": {"id": 19001134087, "created_at": "2022-01-11T20:49:42Z", "updated_at": "2024-08-16T21:33:06Z", "name": "ClickUp Project Management License - Member (Enterprise)", "delivery_time": null, "display_id": 173, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "237": {"id": 19001160305, "created_at": "2022-07-05T13:04:02Z", "updated_at": "2024-08-16T21:33:10Z", "name": "Cloud Computing System Request", "delivery_time": null, "display_id": 206, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "238": {"id": 19001140308, "created_at": "2022-01-12T21:27:20Z", "updated_at": "2024-08-16T21:33:06Z", "name": "Cloud Shared File Access - Document Retrieval/Access", "delivery_time": null, "display_id": 174, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "239": {"id": 19000734126, "created_at": "2021-11-03T10:36:41Z", "updated_at": "2024-10-15T03:44:59Z", "name": "C<PERSON> (Reed)", "delivery_time": null, "display_id": 110, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "240": {"id": 19001171790, "created_at": "2023-04-02T18:52:41Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Concur Changes", "delivery_time": null, "display_id": 276, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "241": {"id": 19001169019, "created_at": "2023-01-18T18:21:06Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Copy of SAP S/4 HANA - KIP Access & Permissions", "delivery_time": null, "display_id": 255, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "242": {"id": 19001192075, "created_at": "2024-02-08T15:33:59Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Decommission a Solution, Service or System", "delivery_time": null, "display_id": 366, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_use_in_title}} | {{item.name}}"}}, "243": {"id": 19001201493, "created_at": "2024-08-12T22:29:47Z", "updated_at": "2024-08-16T21:34:05Z", "name": "Deploy a Solution, Service or System", "delivery_time": null, "display_id": 431, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_use_in_title}} | {{item.name}}"}}, "244": {"id": 19001162003, "created_at": "2022-08-03T18:54:47Z", "updated_at": "2024-08-16T21:33:16Z", "name": "Domain Requests", "delivery_time": null, "display_id": 215, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_request_type}} {{custom_field.cf_domains_to_be_changed}} - {{item.name}}", "auto_gen_document": false}}, "245": {"id": 19000782848, "created_at": "2021-11-12T14:22:02Z", "updated_at": "2024-08-16T21:32:55Z", "name": "Easygenerator eLearning Authoring Content Creator", "delivery_time": null, "display_id": 137, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "246": {"id": 19001167729, "created_at": "2022-12-06T17:34:56Z", "updated_at": "2024-08-16T21:33:20Z", "name": "Email - Mailbox Access Request", "delivery_time": null, "display_id": 246, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} for mailbox: {{custom_field.cf_mailbox_address}}. Requested by {{requestor.name}}"}}, "247": {"id": 19001165190, "created_at": "2022-10-06T13:44:10Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Exclaimer Office 365 Managed Email Signatures", "delivery_time": null, "display_id": 229, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "248": {"id": 19000779633, "created_at": "2021-11-11T22:05:56Z", "updated_at": "2024-08-16T21:32:55Z", "name": "External Sharing - OneDrive / SharePoint", "delivery_time": null, "display_id": 136, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "249": {"id": 19001199703, "created_at": "2024-06-28T13:15:21Z", "updated_at": "2024-08-16T21:33:54Z", "name": "Fresh Service KB", "delivery_time": null, "display_id": 394, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "signature_list": []}}, "250": {"id": 19001148934, "created_at": "2022-03-03T20:44:49Z", "updated_at": "2024-08-16T21:33:08Z", "name": "Freshservice - Assign Agent License", "delivery_time": null, "display_id": 193, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_agent_licence_type}} | {{item.name}} - Requested by: {{requestor.name}}", "auto_gen_document": false}}, "251": {"id": 19001183375, "created_at": "2023-09-15T15:48:03Z", "updated_at": "2024-08-16T21:33:41Z", "name": "Freshservice - Buy Licenses", "delivery_time": null, "display_id": 334, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_agent_licence_type}} | {{item.name}} - Requested by: {{requestor.name}}", "auto_gen_document": false}}, "252": {"id": 19001170126, "created_at": "2023-02-16T21:17:50Z", "updated_at": "2024-08-16T21:33:28Z", "name": "Freshservice - Permissions, Roles and Access Requests", "delivery_time": null, "display_id": 264, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}} - Requested by {{requestor.name}}"}}, "253": {"id": 19001163705, "created_at": "2022-09-08T13:14:43Z", "updated_at": "2024-08-16T21:33:17Z", "name": "Freshservice - Project Management License", "delivery_time": null, "display_id": 226, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": true, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "254": {"id": 19000830583, "created_at": "2021-11-20T19:50:00Z", "updated_at": "2024-08-16T21:32:58Z", "name": "FreshService - Requester (User) Access", "delivery_time": null, "display_id": 149, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "255": {"id": 19000830794, "created_at": "2021-11-20T20:24:23Z", "updated_at": "2024-08-16T21:32:58Z", "name": "Freshservice - System, Workflow and Enhancement Requests", "delivery_time": null, "display_id": 150, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description}} {{item.name}}", "auto_gen_document": false}}, "256": {"id": 19000979319, "created_at": "2021-12-14T21:09:21Z", "updated_at": "2024-08-16T21:33:05Z", "name": "GageTrak Lite - License and Software", "delivery_time": null, "display_id": 162, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "257": {"id": 19001187019, "created_at": "2023-11-09T17:10:09Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Group Policy Object - Active Directory", "delivery_time": null, "display_id": 343, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "258": {"id": 19001201179, "created_at": "2024-08-07T13:42:04Z", "updated_at": "2024-08-16T21:34:05Z", "name": "INFOR/XA Access user permission review", "delivery_time": null, "display_id": 429, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "259": {"id": 19000831179, "created_at": "2021-11-20T22:56:24Z", "updated_at": "2024-10-29T18:52:10Z", "name": "Internet - Access Level Request (Cisco Umbrella SIG)", "delivery_time": null, "display_id": 151, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "260": {"id": 19001205393, "created_at": "2024-10-28T21:37:01Z", "updated_at": "2024-10-29T15:41:53Z", "name": "Internet - Access Level Request (Cisco Secure Client - Umbrella SIG)", "delivery_time": null, "display_id": 443, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "261": {"id": 19001158553, "created_at": "2022-06-03T13:21:44Z", "updated_at": "2024-10-05T00:37:45Z", "name": "IT Systems and Portals - Access and Permissions", "delivery_time": null, "display_id": 203, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}}: {{custom_field.cf_request_summary}} | {{item.name}} | Requested by: {{requestor.name}}", "auto_gen_document": false}}, "262": {"id": 19001170886, "created_at": "2023-03-06T21:33:52Z", "updated_at": "2024-08-16T21:33:28Z", "name": "IT Systems and Portals - REMOVE Access and Permissions", "delivery_time": null, "display_id": 267, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}}: {{custom_field.cf_request_summary}} | {{item.name}} | Requested by: {{requestor.name}}", "auto_gen_document": false}}, "263": {"id": 19001184043, "created_at": "2023-09-25T15:17:39Z", "updated_at": "2024-08-16T21:33:41Z", "name": "IT Systems and Portals - REVIEW Access and Permissions", "delivery_time": null, "display_id": 336, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}}: {{custom_field.cf_request_summary}} | {{item.name}} | Requested by: {{requestor.name}}", "auto_gen_document": false}}, "264": {"id": 19001161923, "created_at": "2022-08-02T19:22:48Z", "updated_at": "2024-08-16T21:33:15Z", "name": "KGR SharePoint Site Creation - Group Membership", "delivery_time": null, "display_id": 214, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "265": {"id": 19001153690, "created_at": "2022-04-12T15:00:32Z", "updated_at": "2024-08-16T21:33:09Z", "name": "KIP - Training Event Request", "delivery_time": null, "display_id": 198, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "266": {"id": 19001187621, "created_at": "2023-11-17T18:47:26Z", "updated_at": "2024-08-16T21:33:42Z", "name": "KnowBe4 - Add Users to Existing KnowBe4 Campaign", "delivery_time": null, "display_id": 347, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "267": {"id": 19001190592, "created_at": "2024-01-19T18:22:12Z", "updated_at": "2024-08-16T21:33:44Z", "name": "KnowBe4 - Completed Trainings", "delivery_time": null, "display_id": 358, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "268": {"id": 19001158818, "created_at": "2022-06-07T21:31:02Z", "updated_at": "2024-08-28T23:02:45Z", "name": "KnowBe4 - New Training Campaign ", "delivery_time": null, "display_id": 205, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} Start Date: {{custom_field.cf_campaign_start_date}} | {{item.name}}", "auto_gen_document": false}}, "269": {"id": 19001197293, "created_at": "2024-05-14T13:55:56Z", "updated_at": "2024-08-16T21:33:51Z", "name": "Krish Test | COPY Salesforce CRM", "delivery_time": null, "display_id": 382, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "270": {"id": 19001203825, "created_at": "2024-09-26T17:40:10Z", "updated_at": "2024-09-26T21:40:10Z", "name": "Lansweeper - MAC", "delivery_time": null, "display_id": 440, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "271": {"id": 19000958933, "created_at": "2021-12-10T21:21:12Z", "updated_at": "2024-08-16T21:33:04Z", "name": "LedgerLink Upload (Vicwest)", "delivery_time": null, "display_id": 160, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "272": {"id": 19001202006, "created_at": "2024-08-19T15:22:36Z", "updated_at": "2024-08-19T19:23:55Z", "name": "Manage Data Retention Policy (Office 365) for a SharePoint Site (Enable managing Preservation Hold Library)", "delivery_time": null, "display_id": 435, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}} | {{custom_field.cf_sharepoint_site_name}}"}}, "273": {"id": 19000712715, "created_at": "2021-10-29T15:38:48Z", "updated_at": "2024-10-15T03:44:58Z", "name": "Microsoft DBC - Access and Permissions", "delivery_time": null, "display_id": 98, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "274": {"id": 19001168493, "created_at": "2023-01-03T21:01:25Z", "updated_at": "2024-08-16T21:33:21Z", "name": "Microsoft EA Licensing - Enterprise Agreement Licensing for O365, Azure, Software etc.", "delivery_time": null, "display_id": 250, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_license_being_requested}} - {{item.name}}"}}, "275": {"id": 19001154171, "created_at": "2022-04-18T13:28:29Z", "updated_at": "2024-08-16T21:33:09Z", "name": "Microsoft Teams & Groups - Create New Group and/or Team", "delivery_time": null, "display_id": 199, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}}: {{item.name}}: {{custom_field.cf_what_would_you_like_to_name_the_team}}"}}, "276": {"id": 19001163760, "created_at": "2022-09-09T15:15:10Z", "updated_at": "2024-08-16T21:33:17Z", "name": "Office 365 - Ad<PERSON> Approval for Application or Add-in", "delivery_time": null, "display_id": 227, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : Admin for {{custom_field.cf_name_of_application_or_add_in}} | {{item.name}}"}}, "277": {"id": 19001187856, "created_at": "2023-11-29T20:10:18Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Office 365 - Viva Goals", "delivery_time": null, "display_id": 350, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_ticket_title_description_short}} - {{item.name}}"}}, "278": {"id": 19001187020, "created_at": "2023-11-09T17:18:20Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Organizational Unit - Active Directory", "delivery_time": null, "display_id": 344, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "279": {"id": 19001187658, "created_at": "2023-11-20T19:08:20Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Power Apps - Creation of Canvas & Model-Driven apps", "delivery_time": null, "display_id": 348, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description}} {{item.name}}"}}, "280": {"id": 19001175867, "created_at": "2023-06-30T11:52:30Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Power BI - Access to Workspace or App", "delivery_time": null, "display_id": 291, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}}: {{item.name}}"}}, "281": {"id": 19000734127, "created_at": "2021-11-03T10:38:12Z", "updated_at": "2024-10-15T03:44:59Z", "name": "Power BI - Licensing for User and Workspaces", "delivery_time": null, "display_id": 111, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "282": {"id": 19001014112, "created_at": "2021-12-21T18:34:24Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Power BI - Request New Workspace", "delivery_time": null, "display_id": 170, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "283": {"id": 19001175868, "created_at": "2023-06-30T12:11:42Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Power BI Admin - Workspace Owner Request", "delivery_time": null, "display_id": 292, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}}: {{item.name}}"}}, "284": {"id": 19001169755, "created_at": "2023-02-07T00:22:59Z", "updated_at": "2024-08-16T21:33:22Z", "name": "RDP access to a server", "delivery_time": null, "display_id": 258, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "285": {"id": 19000734124, "created_at": "2021-11-03T10:31:53Z", "updated_at": "2024-10-15T03:44:59Z", "name": "Salesforce CRM", "delivery_time": null, "display_id": 109, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "286": {"id": 19000786271, "created_at": "2021-11-13T03:04:55Z", "updated_at": "2024-08-16T21:32:57Z", "name": "SAP - AWIP Access & Permissions", "delivery_time": null, "display_id": 140, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "287": {"id": 19000786270, "created_at": "2021-11-13T02:45:21Z", "updated_at": "2024-08-16T21:32:55Z", "name": "SAP B1 - Morin Access & Permissions", "delivery_time": null, "display_id": 139, "category_id": ***********, "product_id": ***********, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "288": {"id": 19000820571, "created_at": "2021-11-18T19:40:44Z", "updated_at": "2024-08-16T21:32:58Z", "name": "SAP ECC - Vicwest Access & Permissions", "delivery_time": null, "display_id": 147, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "289": {"id": 19000733954, "created_at": "2021-11-03T10:13:32Z", "updated_at": "2024-10-15T03:44:58Z", "name": "SAP S/4 HANA - KIP/AWIP Access & Permissions", "delivery_time": null, "display_id": 108, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "290": {"id": 19001191541, "created_at": "2024-01-31T17:43:00Z", "updated_at": "2024-08-16T21:33:49Z", "name": "SAP S/4HANA (KIP) - Change Request", "delivery_time": null, "display_id": 362, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "291": {"id": ***********, "created_at": "2023-02-20T16:19:27Z", "updated_at": "2024-08-16T21:33:28Z", "name": "Shared iPad - Special Account", "delivery_time": null, "display_id": 265, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 101, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": true, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "292": {"id": ***********, "created_at": "2021-10-29T16:03:45Z", "updated_at": "2024-10-15T03:44:58Z", "name": "SharePoint - New Site", "delivery_time": null, "display_id": 99, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "293": {"id": 19001067228, "created_at": "2021-12-30T19:23:48Z", "updated_at": "2024-08-16T21:33:05Z", "name": "SharePoint - Site Changes", "delivery_time": null, "display_id": 172, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "294": {"id": 19001187012, "created_at": "2023-11-09T15:12:36Z", "updated_at": "2024-08-16T21:33:42Z", "name": "Short Description for Ticket Title", "delivery_time": null, "display_id": 341, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "295": {"id": 19001179977, "created_at": "2023-08-09T17:48:09Z", "updated_at": "2024-08-16T21:33:39Z", "name": "SOP Creation", "delivery_time": null, "display_id": 324, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "296": {"id": 19001176117, "created_at": "2023-07-03T18:48:18Z", "updated_at": "2024-08-16T21:33:30Z", "name": "SRFax Portal", "delivery_time": null, "display_id": 293, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "297": {"id": 19001175528, "created_at": "2023-06-26T18:44:34Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Stormwind Studios - License for Online IT Training", "delivery_time": null, "display_id": 290, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} by {{requestor.name}} : {{item.name}}"}}, "298": {"id": 19001181911, "created_at": "2023-08-25T20:47:29Z", "updated_at": "2024-08-16T21:33:40Z", "name": "Systems Data Integration Request", "delivery_time": null, "display_id": 329, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "299": {"id": 19001190775, "created_at": "2024-01-23T20:51:04Z", "updated_at": "2024-08-16T21:33:49Z", "name": "Tanium - RMM - ScreenMeet Agent", "delivery_time": null, "display_id": 360, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "300": {"id": 19001166630, "created_at": "2022-11-10T16:08:05Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Teams Room - Operational Verification Checklist", "delivery_time": null, "display_id": 236, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "301": {"id": 19001176313, "created_at": "2023-07-06T18:28:20Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Thycotic Secret Server", "delivery_time": null, "display_id": 294, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "302": {"id": 19000983938, "created_at": "2021-12-15T15:11:04Z", "updated_at": "2024-08-16T21:33:05Z", "name": "Visual Basic XA AS400 Reporting - Access and Permissions", "delivery_time": null, "display_id": 163, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "303": {"id": 19000712714, "created_at": "2021-10-29T15:34:56Z", "updated_at": "2024-10-15T03:44:57Z", "name": "XA / AS400 - Access and Permissions", "delivery_time": 8, "display_id": 97, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 2, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "304": {"id": 19001195789, "created_at": "2024-04-16T15:26:18Z", "updated_at": "2024-08-16T21:33:51Z", "name": "Document request", "delivery_time": null, "display_id": 379, "category_id": 19000251061, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": " {{item.name}} for {{custom_field.cf_working_title}}"}}, "305": {"id": 19001192071, "created_at": "2024-02-08T14:00:17Z", "updated_at": "2024-08-16T21:33:49Z", "name": "End User Training", "delivery_time": null, "display_id": 365, "category_id": 19000251061, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "306": {"id": 19000959149, "created_at": "2021-12-10T21:52:59Z", "updated_at": "2024-08-16T21:33:04Z", "name": "Call Forwarding - Mobile and Direct Number", "delivery_time": null, "display_id": 161, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "307": {"id": ***********, "created_at": "2024-08-14T20:16:07Z", "updated_at": "2024-08-16T21:34:05Z", "name": "Cellular Signal Booster - Business Office", "delivery_time": null, "display_id": 432, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "308": {"id": ***********, "created_at": "2021-11-23T14:18:43Z", "updated_at": "2024-08-16T21:32:59Z", "name": "Cisco - Office Phone", "delivery_time": null, "display_id": 152, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "309": {"id": 19001192025, "created_at": "2024-02-07T20:15:02Z", "updated_at": "2024-08-16T21:33:49Z", "name": "Cisco Meraki MV72", "delivery_time": null, "display_id": 364, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "310": {"id": 19001171319, "created_at": "2023-03-20T19:30:07Z", "updated_at": "2024-08-16T21:33:29Z", "name": "Display - Cable/Adapter", "delivery_time": null, "display_id": 274, "category_id": ***********, "product_id": 19000181959, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472044, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": true, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "311": {"id": 19001167569, "created_at": "2022-12-01T21:49:31Z", "updated_at": "2024-08-16T21:33:19Z", "name": "Fuze - Office Phone", "delivery_time": null, "display_id": 243, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "312": {"id": 19001165214, "created_at": "2022-10-06T19:45:18Z", "updated_at": "2024-08-16T21:33:18Z", "name": "Fuze - System or Feature Configuration Request", "delivery_time": null, "display_id": 230, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "313": {"id": 19001174081, "created_at": "2023-06-05T16:06:24Z", "updated_at": "2024-08-16T21:33:30Z", "name": "Fuze - Training", "delivery_time": null, "display_id": 285, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 1, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}", "auto_gen_document": false}}, "314": {"id": 19001167110, "created_at": "2022-11-20T22:49:29Z", "updated_at": "2024-08-16T21:33:19Z", "name": "International Travel plan for mobile phone", "delivery_time": null, "display_id": 239, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "315": {"id": 19001202883, "created_at": "2024-09-09T13:25:43Z", "updated_at": "2024-09-09T17:25:43Z", "name": "Mobile - MAC", "delivery_time": null, "display_id": 438, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}, "316": {"id": 19001194760, "created_at": "2024-03-25T21:17:21Z", "updated_at": "2024-08-16T21:33:50Z", "name": "Phone Number Porting", "delivery_time": null, "display_id": 376, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_title}} | {{item.name}}"}}, "317": {"id": 19001198549, "created_at": "2024-06-05T14:02:46Z", "updated_at": "2024-08-16T21:33:53Z", "name": "Teams Room - Setup & Installation", "delivery_time": null, "display_id": 390, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 2, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": null, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": true, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{custom_field.cf_short_description_for_ticket_title}} | {{item.name}}"}}, "318": {"id": 19001195426, "created_at": "2024-04-09T13:37:14Z", "updated_at": "2024-08-16T21:33:51Z", "name": "Zoom - Office Phone", "delivery_time": null, "display_id": 378, "category_id": ***********, "product_id": null, "quantity": null, "deleted": false, "icon_name": "service-catalog-services-default", "group_visibility": 1, "agent_group_visibility": 1, "item_type": 1, "ci_type_id": 19000472043, "visibility": 2, "workspace_id": 2, "cost_visibility": false, "delivery_time_visibility": false, "allow_attachments": false, "allow_quantity": false, "is_bundle": false, "create_child": false, "configs": {"auto_gen_document": false, "attachment_mandatory": false, "subject": "Request for {{requested_for}} : {{item.name}}"}}}}