"""
Ticket processing module for FreshConnect.

This module handles fetching, processing, and storing ticket data from FreshService.
"""

import logging
import time
from datetime import datetime
from ..api.freshservice import FreshServiceClient
from ..data.database import initialize_db

logger = logging.getLogger(__name__)

class TicketProcessor:
    """Processor for FreshService tickets."""
    
    def __init__(self, api_client=None, db=None):
        """
        Initialize the ticket processor.
        
        Args:
            api_client (FreshServiceClient, optional): FreshService API client. Defaults to None.
            db (Database, optional): Database instance. Defaults to None.
        """
        self.api_client = api_client or FreshServiceClient()
        self.db = db or initialize_db()
        logger.debug("Initialized TicketProcessor")
    
    def fetch_and_store_tickets(self, updated_since=None, max_tickets=None):
        """
        Fetch tickets from FreshService and store them in the database.
        
        Args:
            updated_since (str, optional): ISO format datetime string. Defaults to None.
            max_tickets (int, optional): Maximum number of tickets to fetch. Defaults to None.
            
        Returns:
            int: Number of tickets fetched and stored
        """
        logger.info(f"Fetching tickets updated since {updated_since}")
        
        page = 1
        per_page = 30
        all_tickets = []
        
        while True:
            try:
                response = self.api_client.get_tickets(updated_since, page, per_page)
                tickets = response.get('tickets', [])
                
                if not tickets:
                    break
                
                all_tickets.extend(tickets)
                logger.info(f"Fetched {len(tickets)} tickets (page {page})")
                
                # Check if we've reached the maximum
                if max_tickets and len(all_tickets) >= max_tickets:
                    all_tickets = all_tickets[:max_tickets]
                    break
                
                # Check if we've reached the last page
                if len(tickets) < per_page:
                    break
                
                page += 1
                
                # Add a small delay to avoid rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error fetching tickets: {str(e)}")
                break
        
        # Store tickets in the database
        if all_tickets:
            try:
                self.db.clear_and_insert_items('tickets', all_tickets)
                logger.info(f"Stored {len(all_tickets)} tickets in the database")
            except Exception as e:
                logger.error(f"Error storing tickets: {str(e)}")
        
        return len(all_tickets)
    
    def fetch_and_store_ticket_history(self, ticket_id):
        """
        Fetch history for a specific ticket and store it in the database.
        
        Args:
            ticket_id (int): Ticket ID
            
        Returns:
            dict: Ticket history data
        """
        logger.info(f"Fetching history for ticket {ticket_id}")
        
        try:
            # Get ticket details
            ticket_response = self.api_client.get_ticket(ticket_id)
            ticket = ticket_response.get('ticket', {})
            
            # Get ticket conversations (history)
            conversations_response = self.api_client.get_ticket_history(ticket_id)
            conversations = conversations_response.get('conversations', [])
            
            # Combine into a history object
            history_data = {
                'ticket_id': ticket_id,
                'ticket': ticket,
                'history': conversations,
                'fetched_at': datetime.utcnow().isoformat()
            }
            
            # Store in database
            history_table = self.db.get_table('ticket_history')
            
            # Check if history already exists for this ticket
            existing_items = self.db.find_items('ticket_history', {'ticket_id': ticket_id})
            
            if existing_items:
                # Update existing history
                self.db.update_items('ticket_history', {'ticket_id': ticket_id}, history_data)
                logger.info(f"Updated history for ticket {ticket_id}")
            else:
                # Insert new history
                self.db.insert_items('ticket_history', [history_data])
                logger.info(f"Stored history for ticket {ticket_id}")
            
            return history_data
            
        except Exception as e:
            logger.error(f"Error fetching history for ticket {ticket_id}: {str(e)}")
            return None
    
    def fetch_and_store_all_ticket_history(self, max_tickets=None):
        """
        Fetch and store history for all tickets in the database.
        
        Args:
            max_tickets (int, optional): Maximum number of tickets to process. Defaults to None.
            
        Returns:
            int: Number of tickets processed
        """
        logger.info("Fetching history for all tickets")
        
        try:
            # Get all tickets from the database
            tickets = self.db.find_items('tickets')
            
            if not tickets:
                logger.warning("No tickets found in database")
                return 0
            
            # Limit the number of tickets if specified
            if max_tickets:
                tickets = tickets[:max_tickets]
            
            processed_count = 0
            
            # Process each ticket
            for ticket in tickets:
                ticket_id = ticket.get('id')
                if ticket_id:
                    self.fetch_and_store_ticket_history(ticket_id)
                    processed_count += 1
                    
                    # Add a small delay to avoid rate limiting
                    time.sleep(0.5)
            
            logger.info(f"Processed history for {processed_count} tickets")
            return processed_count
            
        except Exception as e:
            logger.error(f"Error processing ticket history: {str(e)}")
            return 0


def fetch_tickets(updated_since=None, max_tickets=None):
    """
    Fetch tickets from FreshService and store them in the database.
    
    Args:
        updated_since (str, optional): ISO format datetime string. Defaults to None.
        max_tickets (int, optional): Maximum number of tickets to fetch. Defaults to None.
        
    Returns:
        int: Number of tickets fetched and stored
    """
    processor = TicketProcessor()
    return processor.fetch_and_store_tickets(updated_since, max_tickets)


def fetch_ticket_history(ticket_id):
    """
    Fetch history for a specific ticket and store it in the database.
    
    Args:
        ticket_id (int): Ticket ID
        
    Returns:
        dict: Ticket history data
    """
    processor = TicketProcessor()
    return processor.fetch_and_store_ticket_history(ticket_id)


def fetch_all_ticket_history(max_tickets=None):
    """
    Fetch and store history for all tickets in the database.
    
    Args:
        max_tickets (int, optional): Maximum number of tickets to process. Defaults to None.
        
    Returns:
        int: Number of tickets processed
    """
    processor = TicketProcessor()
    return processor.fetch_and_store_all_ticket_history(max_tickets)
