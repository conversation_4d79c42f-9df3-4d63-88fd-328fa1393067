"""
Test script for the recent agent activity analysis.

This script demonstrates how to use the analyze_recent_agent_activity function
to track agent activity in the last 10 minutes and store it in the database.
"""

import os
import sys
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Import the function
from freshconnect.processors.agents import analyze_recent_agent_activity

def main():
    """Run the recent agent activity analysis."""
    print(f"Starting recent agent activity analysis at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze activity in the last 10 minutes
    results = analyze_recent_agent_activity(minutes=10)
    
    if results:
        print(f"Analysis complete. Found {results['action_count']} actions by {results['unique_actors']} agents.")
        print(f"Active tickets: {results['active_tickets']}")
        
        # Print the top agents
        print("\nTop agents in the last 10 minutes:")
        for agent, count in sorted(results['actor_counts'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {agent}: {count} actions")
            
        # Print the most recent actions
        print("\nMost recent actions:")
        for action in sorted(results['actions'], key=lambda x: x['timestamp'], reverse=True)[:5]:
            print(f"  {action['timestamp']} - {action['actor']} - {action['action']} - Ticket #{action['ticket_id']}")
    else:
        print("No recent activity found or error occurred during analysis.")
    
    print(f"Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
