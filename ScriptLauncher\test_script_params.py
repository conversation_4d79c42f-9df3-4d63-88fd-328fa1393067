"""
Test script to check script parameters.
"""

import sys
import json
from scripts_db import get_script_by_id

# Get script ID from command line
script_id = int(sys.argv[1]) if len(sys.argv) > 1 else 3  # Default to MDM Reconciliation

# Get script details
script = get_script_by_id(script_id)

if script:
    print(f"Script Name: {script['name']}")
    print(f"Script Type: {script['type']}")
    print(f"Script Path: {script['path']}")
    print(f"Parameters: {json.dumps(script['parameters'], indent=2)}")
    
    # Check for file parameters
    file_params = [p for p in script['parameters'] if p.get('type') == 'file']
    print(f"\nFile Parameters: {len(file_params)}")
    for param in file_params:
        print(f"  - {param['name']}: {param['label']} (Required: {param['required']})")
else:
    print(f"Script with ID {script_id} not found")
