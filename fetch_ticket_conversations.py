#!/usr/bin/env python3
"""
FreshService Ticket Conversations Fetcher

This script reads an Excel file containing phishing tickets and fetches the conversation
history for each ticket from FreshService. Results are stored in a JSON file.

Usage:
    python fetch_ticket_conversations.py <excel_file_path>

Environment Variables:
    FRESHSERVICE_DOMAIN: Your FreshService domain (e.g., company.freshservice.com)
    FRESHSERVICE_API_KEY: Your FreshService API key

Author: IT Team
Date: 2025-01-03
"""

import os
import sys
import json
import pandas as pd
import requests
from base64 import b64encode
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

class FreshServiceConversationFetcher:
    """A class to fetch ticket conversations from FreshService API."""
    
    def __init__(self, domain=None, api_key=None):
        """
        Initialize the FreshService conversation fetcher.
        
        Args:
            domain (str): FreshService domain (e.g., 'company.freshservice.com')
            api_key (str): FreshService API key
        """
        # Load environment variables from .env file if it exists
        load_dotenv()
        
        self.domain = domain or os.getenv('FRESHSERVICE_DOMAIN')
        self.api_key = api_key or os.getenv('FRESHSERVICE_API_KEY')
        
        if not self.domain:
            raise ValueError("FRESHSERVICE_DOMAIN must be provided as parameter or environment variable")
        
        if not self.api_key:
            raise ValueError("FRESHSERVICE_API_KEY must be provided as parameter or environment variable")
        
        # Remove protocol if included in domain
        self.domain = self.domain.replace('https://', '').replace('http://', '')
        
        self.base_url = f"https://{self.domain}/api/v2"
        self.session = self._create_session()
        
        print(f"Initialized FreshService client for domain: {self.domain}")
    
    def _create_session(self):
        """Create and configure a requests session for API calls."""
        session = requests.Session()
        auth_token = b64encode(f"{self.api_key}:X".encode()).decode()
        session.headers.update({
            'Authorization': f'Basic {auth_token}',
            'Content-Type': 'application/json'
        })
        return session
    
    def _make_request(self, method, endpoint, params=None, retry_count=0):
        """
        Make an HTTP request to the FreshService API with rate limiting and retry logic.

        Args:
            method (str): HTTP method (GET, POST, PUT, DELETE)
            endpoint (str): API endpoint (without base URL)
            params (dict, optional): Query parameters
            retry_count (int): Current retry attempt

        Returns:
            dict: JSON response from the API

        Raises:
            requests.exceptions.RequestException: If the request fails after retries
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        max_retries = 3
        retry_delay = 2  # seconds

        try:
            print(f"Making {method} request to: {url}")
            if params:
                print(f"Parameters: {params}")

            response = self.session.request(method, url, params=params, timeout=30)

            # Check rate limit headers before processing response
            rate_limit_remaining = response.headers.get('X-Ratelimit-Remaining')
            if rate_limit_remaining:
                try:
                    remaining = int(rate_limit_remaining)
                    print(f"Rate limit remaining: {remaining}")

                    # If remaining requests is less than 65, wait 10 seconds
                    if remaining < 65:
                        print(f"Rate limit remaining ({remaining}) is below 65. Waiting 10 seconds...")
                        sys.stdout.flush()  # Force output to display immediately
                        import time
                        time.sleep(10)
                        print(f"Rate limit wait completed. Continuing...")
                        sys.stdout.flush()  # Force output to display immediately
                except ValueError:
                    # If we can't parse the header, continue normally
                    pass

            # Handle rate limiting (429 status)
            if response.status_code == 429:
                if retry_count < max_retries:
                    print(f"Rate limited (429). Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                    sys.stdout.flush()  # Force output to display immediately
                    import time
                    time.sleep(retry_delay)
                    return self._make_request(method, endpoint, params, retry_count + 1)
                else:
                    raise requests.exceptions.RequestException("Rate limit exceeded after maximum retries")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            if retry_count < max_retries:
                print(f"Request failed: {e}. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                import time
                time.sleep(retry_delay)
                return self._make_request(method, endpoint, params, retry_count + 1)
            else:
                print(f"Request failed after {max_retries} retries: {e}")
                raise
    
    def get_ticket_conversations(self, ticket_id):
        """
        Get conversations for a specific ticket.
        
        Args:
            ticket_id (int): Ticket ID
            
        Returns:
            list: List of conversation dictionaries
        """
        try:
            print(f"Fetching conversations for ticket {ticket_id}")
            response = self._make_request('GET', f'tickets/{ticket_id}/conversations')
            conversations = response.get('conversations', [])
            print(f"Found {len(conversations)} conversations for ticket {ticket_id}")
            return conversations
        except Exception as e:
            print(f"Error fetching conversations for ticket {ticket_id}: {e}")
            return []
    
    def get_ticket_details(self, ticket_id):
        """
        Get basic ticket details.
        
        Args:
            ticket_id (int): Ticket ID
            
        Returns:
            dict: Ticket details
        """
        try:
            print(f"Fetching details for ticket {ticket_id}")
            response = self._make_request('GET', f'tickets/{ticket_id}')
            ticket = response.get('ticket', {})
            return ticket
        except Exception as e:
            print(f"Error fetching details for ticket {ticket_id}: {e}")
            return {}

def read_excel_file(file_path, limit=10):
    """
    Read the Excel file and extract ticket numbers.

    Args:
        file_path (str): Path to the Excel file
        limit (int or None): Maximum number of tickets to process, None for all tickets

    Returns:
        list: List of ticket numbers
    """
    try:
        print(f"Reading Excel file: {file_path}")
        
        # Try to read the Excel file
        df = pd.read_excel(file_path)
        
        print(f"Excel file loaded successfully. Shape: {df.shape}")
        print(f"Columns found: {list(df.columns)}")
        
        # Look for the ticket number column (case insensitive)
        ticket_column = None
        for col in df.columns:
            if 'ticket number' in col.lower() or 'ticket_number' in col.lower():
                ticket_column = col
                break
        
        if not ticket_column:
            # Try other common variations
            for col in df.columns:
                if any(term in col.lower() for term in ['ticket', 'id', 'number']):
                    ticket_column = col
                    print(f"Using column '{col}' as ticket number column")
                    break
        
        if not ticket_column:
            print("Available columns:")
            for i, col in enumerate(df.columns):
                print(f"  {i}: {col}")
            raise ValueError("Could not find 'ticket number' column. Please check the column headers.")
        
        print(f"Using column '{ticket_column}' for ticket numbers")

        # Extract ticket numbers (limit to top N or all if limit is None)
        if limit is None:
            ticket_numbers = df[ticket_column].tolist()
            print(f"Processing ALL {len(ticket_numbers)} tickets from the file")
        else:
            ticket_numbers = df[ticket_column].head(limit).tolist()
            print(f"Processing first {limit} tickets from the file")
        
        # Clean up ticket numbers (remove NaN, convert to int if possible)
        clean_tickets = []
        for ticket in ticket_numbers:
            if pd.notna(ticket):
                try:
                    # Try to convert to int (remove any decimal points)
                    clean_ticket = int(float(ticket))
                    clean_tickets.append(clean_ticket)
                except (ValueError, TypeError):
                    # If conversion fails, keep as string
                    clean_tickets.append(str(ticket))
        
        print(f"Found {len(clean_tickets)} valid ticket numbers: {clean_tickets}")
        return clean_tickets
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return []

def fetch_conversations_for_tickets(fetcher, ticket_numbers):
    """
    Fetch conversations for a list of ticket numbers with incremental saving.

    Args:
        fetcher: FreshServiceConversationFetcher instance
        ticket_numbers: List of ticket numbers

    Returns:
        dict: Dictionary with ticket data and conversations
    """
    # Generate timestamp for consistent filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    progress_filename = f"ticket_conversations_progress_{timestamp}.json"
    final_filename = f"ticket_conversations_{timestamp}.json"

    results = {
        'metadata': {
            'total_tickets': len(ticket_numbers),
            'fetch_timestamp': datetime.now().isoformat(),
            'domain': fetcher.domain,
            'progress_filename': progress_filename,
            'final_filename': final_filename
        },
        'tickets': []
    }
    
    # Create initial progress file
    print(f"Creating initial progress file: {progress_filename}")
    save_results_to_json(results, progress_filename, is_final=False)

    for i, ticket_id in enumerate(ticket_numbers, 1):
        print(f"\n--- Processing ticket {i}/{len(ticket_numbers)}: {ticket_id} ---")

        # Progress update and incremental save every 100 tickets
        if i % 100 == 0:
            print(f"*** PROGRESS UPDATE: Completed {i}/{len(ticket_numbers)} tickets ({i/len(ticket_numbers)*100:.1f}%) ***")

            # Save progress incrementally
            print(f"Saving incremental progress...")
            save_results_to_json(results, progress_filename, is_final=False)

            sys.stdout.flush()  # Force output to display immediately
        
        try:
            # Get ticket details
            ticket_details = fetcher.get_ticket_details(ticket_id)
            
            # Get conversations
            conversations = fetcher.get_ticket_conversations(ticket_id)
            
            ticket_data = {
                'ticket_id': ticket_id,
                'ticket_details': ticket_details,
                'conversations': conversations,
                'conversation_count': len(conversations),
                'fetch_status': 'success'
            }
            
            results['tickets'].append(ticket_data)
            
            print(f"Successfully fetched data for ticket {ticket_id}")
            
        except Exception as e:
            print(f"Failed to fetch data for ticket {ticket_id}: {e}")
            
            # Add error entry
            ticket_data = {
                'ticket_id': ticket_id,
                'ticket_details': {},
                'conversations': [],
                'conversation_count': 0,
                'fetch_status': 'error',
                'error_message': str(e)
            }
            
            results['tickets'].append(ticket_data)

    # Save final results
    print(f"\nSaving final results...")
    save_results_to_json(results, final_filename, is_final=True)

    return results

def save_results_to_json(results, filename=None, is_final=True):
    """
    Save results to a JSON file.

    Args:
        results (dict): Results dictionary
        filename (str): Output filename (optional)
        is_final (bool): Whether this is the final save or incremental
    """
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if is_final:
            filename = f"ticket_conversations_{timestamp}.json"
        else:
            filename = f"ticket_conversations_progress_{timestamp}.json"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        if is_final:
            print(f"\nFinal results saved to: {filename}")
        else:
            print(f"Progress saved to: {filename}")

        # Print summary
        total_tickets = results['metadata']['total_tickets']
        processed_tickets = len(results['tickets'])
        successful_fetches = len([t for t in results['tickets'] if t['fetch_status'] == 'success'])
        total_conversations = sum(t['conversation_count'] for t in results['tickets'])

        if is_final:
            print(f"\nFINAL SUMMARY:")
            print(f"Total tickets processed: {processed_tickets}")
            print(f"Successful fetches: {successful_fetches}")
            print(f"Failed fetches: {processed_tickets - successful_fetches}")
            print(f"Total conversations fetched: {total_conversations}")
        else:
            print(f"Progress: {processed_tickets}/{total_tickets} tickets processed ({processed_tickets/total_tickets*100:.1f}%)")
            print(f"Conversations so far: {total_conversations}")

        sys.stdout.flush()  # Force output to display immediately
        return filename

    except Exception as e:
        print(f"Error saving results to file: {e}")
        sys.stdout.flush()
        return None

def main():
    """Main function to fetch ticket conversations."""
    if len(sys.argv) < 2:
        print("Usage: python fetch_ticket_conversations.py <excel_file_path>")
        print("Example: python fetch_ticket_conversations.py phishing_tickets.xlsx")
        sys.exit(1)
    
    excel_file_path = sys.argv[1]
    
    if not os.path.exists(excel_file_path):
        print(f"Error: File '{excel_file_path}' not found.")
        sys.exit(1)
    
    print("FreshService Ticket Conversations Fetcher")
    print("=" * 60)
    sys.stdout.flush()  # Force output to display immediately
    
    try:
        # Read ticket numbers from Excel file
        print("\nStep 1: Reading Excel file...")
        sys.stdout.flush()  # Force output to display immediately
        ticket_numbers = read_excel_file(excel_file_path, limit=None)  # Process all tickets
        
        if not ticket_numbers:
            print("No valid ticket numbers found in the Excel file.")
            sys.exit(1)
        
        # Initialize the fetcher
        print("\nStep 2: Initializing FreshService client...")
        sys.stdout.flush()  # Force output to display immediately
        fetcher = FreshServiceConversationFetcher()
        
        # Fetch conversations (includes incremental and final saving)
        print("\nStep 3: Fetching ticket conversations...")
        sys.stdout.flush()  # Force output to display immediately
        results = fetch_conversations_for_tickets(fetcher, ticket_numbers)
        
        print(f"\n{'='*60}")
        print("Process completed successfully!")
        print(f"{'='*60}")
        
    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("\nPlease ensure you have set the following environment variables:")
        print("- FRESHSERVICE_DOMAIN (e.g., company.freshservice.com)")
        print("- FRESHSERVICE_API_KEY (your FreshService API key)")
        sys.exit(1)
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
