# Standalone Scripts

This directory contains standalone scripts and utilities that were separated from the FreshConnect project. These scripts handle specific tasks and are not integrated into the modular package structure of FreshConnect.

## Scripts

- **agentStats.py**: Analyzes agent activity based on ticket history data
- **ConnectionFunctionsOld.py**: Legacy connection functions
- **dbFunctions.py**: Database utility functions
- **GetChanges.py**: Returns a list of ticket numbers that have changed since a specified date/time
- **serviceItems.py**: Fetches and processes service catalog items from FreshService
- **telus-bill-processor.py**: Processes Telus bills
- **ticketHistory.py**: Fetches and processes ticket history from FreshService

## Directories

- **Recons**: Contains reconciliation scripts
- **Storage**: Data storage directory
- **WebCatcher**: Web-related utilities

## Usage

These scripts are designed to be run independently and are not part of the FreshConnect package. They may have dependencies on each other or external libraries.

## Note

These scripts were moved from the FreshConnect project as they are standalone utilities that don't fit into the modular package structure. They may be refactored or integrated into other projects in the future.
