"""
MDM Reconciliation Script

This script compares MDM and LS data to identify mismatches.
It accepts file paths as command-line arguments.
"""

import os
import sys
import argparse
import datetime
import pandas as pd
from pathlib import Path

def run_reconciliation(mdm_file=None, ls_file=None):
    """
    Run reconciliation between MDM and LS data.
    
    Args:
        mdm_file: Path to the MDM data Excel file
        ls_file: Path to the LS data Excel file
    """
    try:
        # Get the Recons directory path for output
        recons_dir = Path.cwd()
        
        # Use provided file paths or default ones
        mdm_file_path = mdm_file if mdm_file else recons_dir / 'MDMtoLS.xlsx'
        ls_file_path = ls_file if ls_file else recons_dir / 'LStoMDM.xlsx'
        
        print(f"Using MDM file: {mdm_file_path}")
        print(f"Using LS file: {ls_file_path}")
        
        # Read in the two XLSX files
        mdm_data = pd.read_excel(mdm_file_path)
        ls_data = pd.read_excel(ls_file_path)

        # Create a new XLSX file with the reconciliation
        today = datetime.date.today().strftime('%Y.%m.%d')
        output_file = recons_dir / f'KC_ITM_21.1_{today}.xlsx'

        # Initialize lists for mismatches
        not_in_ls = []
        not_in_mdm = []
        device_not_in_mdm = []

        # Check MDM records not in LS
        for _, row in mdm_data.iterrows():
            if row['Serial number'] not in ls_data['Serialnumber'].values:
                not_in_ls.append(row.to_dict())

        # Check LS records not in MDM
        for _, row in ls_data.iterrows():
            if row['Serialnumber'] not in mdm_data['Serial number'].values:
                not_in_mdm.append(row.to_dict())
                
                # Add to the "Device not in MDM" tab with action columns
                device_entry = row.to_dict()
                # Add action columns
                device_entry['Action Required'] = ''
                device_entry['Action Taken'] = ''
                device_entry['Notes'] = ''
                device_not_in_mdm.append(device_entry)

        # Create DataFrames for mismatches
        not_in_ls_df = pd.DataFrame(not_in_ls)
        not_in_mdm_df = pd.DataFrame(not_in_mdm)
        device_not_in_mdm_df = pd.DataFrame(device_not_in_mdm)

        # Write to Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            mdm_data.to_excel(writer, sheet_name='MDMtoLS', index=False)
            ls_data.to_excel(writer, sheet_name='LStoMDM', index=False)
            if not not_in_ls_df.empty:
                not_in_ls_df.to_excel(writer, sheet_name='Not in LS', index=False)
            if not not_in_mdm_df.empty:
                not_in_mdm_df.to_excel(writer, sheet_name='Not in MDM', index=False)
            if not device_not_in_mdm_df.empty:
                device_not_in_mdm_df.to_excel(writer, sheet_name='Device not in MDM', index=False)

        print(f"Reconciliation completed. Output file: {output_file}")
        print(f"Found {len(not_in_ls)} records not in LS")
        print(f"Found {len(not_in_mdm)} records not in MDM")
        
        return str(output_file)
        
    except FileNotFoundError as e:
        print(f"Error: Could not find input file - {e}")
        raise
    except Exception as e:
        print(f"Error occurred during reconciliation: {e}")
        raise

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Run MDM reconciliation')
    parser.add_argument('--mdm_file', help='Path to the MDM data Excel file')
    parser.add_argument('--ls_file', help='Path to the LS data Excel file')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    run_reconciliation(args.mdm_file, args.ls_file)
