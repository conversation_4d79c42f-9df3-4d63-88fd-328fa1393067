from flask import Flask, request, jsonify
from sqlalchemy import create_engine, Column, Integer, String, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import json
import os
import logging
from logging.handlers import RotatingFileHandler

# Import your existing tools
from serviceItems import ServiceCatalogFetcher
from ticketHistory import TicketHistoryFetcher
from agentStats import analyze_agent_activity

# Initialize Flask app
app = Flask(__name__)

# Configure logging
if not os.path.exists('logs'):
    os.makedirs('logs')

file_handler = RotatingFileHandler('logs/webhook_events.log', maxBytes=10240, backupCount=10)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.INFO)
app.logger.addHandler(file_handler)
app.logger.setLevel(logging.INFO)
app.logger.info('Webhook service startup')

# Database setup
Base = declarative_base()

class WebhookEvent(Base):
    __tablename__ = 'webhook_events'
    
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    event_type = Column(String)
    source = Column(String)
    payload = Column(JSON)
    action_taken = Column(String)
    status = Column(String)

def init_db():
    engine = create_engine('sqlite:///Storage/webhooks.db')
    Base.metadata.create_all(engine)
    return sessionmaker(bind=engine)()

db_session = init_db()

def load_config():
    """Load configuration from environment variables"""
    return {
        'domain': os.getenv('FRESHSERVICE_DOMAIN'),
        'api_key': os.getenv('FRESHSERVICE_API_KEY')
    }

@app.route('/webhook/freshservice', methods=['POST'])
def freshservice_webhook():
    try:
        # Extract webhook data
        event_type = request.headers.get('X-Freshservice-Event', 'unknown')
        payload = request.get_json()
        
        # Log the incoming webhook
        app.logger.info(f'Received {event_type} webhook')
        
        # Initialize webhook event
        event = WebhookEvent(
            event_type=event_type,
            source='freshservice',
            payload=payload,
            status='received'
        )
        
        # Process based on event type
        action_taken = []
        config = load_config()
        
        if event_type == 'ticket_created' or event_type == 'ticket_updated':
            # Update ticket history
            fetcher = TicketHistoryFetcher(config['domain'], config['api_key'])
            ticket_id = payload.get('ticket', {}).get('id')
            if ticket_id:
                fetcher.get_ticket_history(ticket_id)
                action_taken.append('updated_ticket_history')
                
        elif event_type == 'service_item_updated':
            # Update service catalog
            fetcher = ServiceCatalogFetcher(config['domain'], config['api_key'])
            fetcher.get_service_items()
            action_taken.append('updated_service_catalog')
            
        elif event_type == 'agent_assignment_updated':
            # Trigger agent stats analysis
            analyze_agent_activity()
            action_taken.append('updated_agent_stats')
        
        # Update and save event
        event.action_taken = ', '.join(action_taken)
        event.status = 'processed'
        db_session.add(event)
        db_session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'Processed {event_type} webhook',
            'actions': action_taken
        }), 200
        
    except Exception as e:
        app.logger.error(f'Error processing webhook: {str(e)}')
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/webhook/events', methods=['GET'])
def list_events():
    """Endpoint to view recent webhook events"""
    try:
        events = db_session.query(WebhookEvent).order_by(WebhookEvent.timestamp.desc()).limit(100).all()
        return jsonify([{
            'id': e.id,
            'timestamp': e.timestamp.isoformat(),
            'event_type': e.event_type,
            'source': e.source,
            'action_taken': e.action_taken,
            'status': e.status
        } for e in events])
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, port=5000)