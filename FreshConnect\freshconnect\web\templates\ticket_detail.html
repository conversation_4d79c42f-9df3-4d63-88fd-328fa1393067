{% extends "base.html" %}

{% block title %}FreshConnect - Ticket #{{ ticket.id }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-ticket-alt me-2"></i>Ticket #{{ ticket.id }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('tickets') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Tickets
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshTicket">
                <i class="fas fa-sync-alt me-1"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" id="fetchTicketHistory" data-ticket-id="{{ ticket.id }}">
                <i class="fas fa-history me-1"></i> Fetch History
            </button>
        </div>
    </div>
</div>

<!-- Ticket Details -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ ticket.subject }}</h5>
                    <div>
                        {% if ticket.status == 2 %}
                        <span class="badge bg-primary">Open</span>
                        {% elif ticket.status == 3 %}
                        <span class="badge bg-warning">Pending</span>
                        {% elif ticket.status == 4 %}
                        <span class="badge bg-success">Resolved</span>
                        {% elif ticket.status == 5 %}
                        <span class="badge bg-secondary">Closed</span>
                        {% else %}
                        <span class="badge bg-info">Other</span>
                        {% endif %}
                        
                        {% if ticket.priority == 1 %}
                        <span class="badge bg-success">Low</span>
                        {% elif ticket.priority == 2 %}
                        <span class="badge bg-info">Medium</span>
                        {% elif ticket.priority == 3 %}
                        <span class="badge bg-warning">High</span>
                        {% elif ticket.priority == 4 %}
                        <span class="badge bg-danger">Urgent</span>
                        {% else %}
                        <span class="badge bg-secondary">Unknown</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Requester:</dt>
                            <dd class="col-sm-8">{{ ticket.requester_name|default('Unknown') }}</dd>
                            
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ ticket.created_at|default('') }}</dd>
                            
                            <dt class="col-sm-4">Updated:</dt>
                            <dd class="col-sm-8">{{ ticket.updated_at|default('') }}</dd>
                            
                            <dt class="col-sm-4">Due By:</dt>
                            <dd class="col-sm-8">{{ ticket.due_by|default('Not set') }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Group:</dt>
                            <dd class="col-sm-8">{{ ticket.group_name|default('None') }}</dd>
                            
                            <dt class="col-sm-4">Agent:</dt>
                            <dd class="col-sm-8">{{ ticket.agent_name|default('Unassigned') }}</dd>
                            
                            <dt class="col-sm-4">Type:</dt>
                            <dd class="col-sm-8">{{ ticket.type|default('Not specified') }}</dd>
                            
                            <dt class="col-sm-4">Source:</dt>
                            <dd class="col-sm-8">{{ ticket.source|default('Not specified') }}</dd>
                        </dl>
                    </div>
                </div>
                
                <h6 class="card-subtitle mb-2 text-muted">Description</h6>
                <div class="ticket-description mb-3">
                    {{ ticket.description|default('No description provided')|safe }}
                </div>
                
                {% if ticket.tags %}
                <h6 class="card-subtitle mb-2 text-muted">Tags</h6>
                <div class="mb-3">
                    {% for tag in ticket.tags %}
                    <span class="badge bg-secondary">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if ticket.custom_fields %}
                <h6 class="card-subtitle mb-2 text-muted">Custom Fields</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field, value in ticket.custom_fields.items() %}
                            <tr>
                                <td>{{ field }}</td>
                                <td>{{ value|default('') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Ticket History -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history me-1"></i> Ticket History
            </div>
            <div class="card-body">
                {% if history %}
                <div class="ticket-timeline">
                    {% for event in history %}
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            {% if event.type == 'note' %}
                            <i class="fas fa-comment"></i>
                            {% elif event.type == 'reply' %}
                            <i class="fas fa-reply"></i>
                            {% elif event.type == 'status_change' %}
                            <i class="fas fa-exchange-alt"></i>
                            {% elif event.type == 'assignment' %}
                            <i class="fas fa-user-edit"></i>
                            {% else %}
                            <i class="fas fa-info-circle"></i>
                            {% endif %}
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <span class="timeline-author">{{ event.actor.name|default('System') }}</span>
                                <span class="timeline-date">{{ event.created_at }}</span>
                            </div>
                            <div class="timeline-body">
                                {{ event.content|safe }}
                            </div>
                            {% if event.attachments %}
                            <div class="timeline-attachments">
                                <h6>Attachments:</h6>
                                <ul>
                                    {% for attachment in event.attachments %}
                                    <li>
                                        <a href="{{ attachment.url }}" target="_blank">
                                            <i class="fas fa-paperclip"></i> {{ attachment.name }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    No history available for this ticket. Click "Fetch History" to retrieve the ticket history.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .ticket-timeline {
        position: relative;
        padding: 1rem 0;
    }
    
    .timeline-item {
        display: flex;
        margin-bottom: 1.5rem;
    }
    
    .timeline-marker {
        flex: 0 0 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #007bff;
        border: 2px solid #007bff;
    }
    
    .timeline-content {
        flex: 1;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        position: relative;
    }
    
    .timeline-content:before {
        content: '';
        position: absolute;
        left: -10px;
        top: 10px;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-right: 10px solid #f8f9fa;
    }
    
    .timeline-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.5rem;
    }
    
    .timeline-author {
        font-weight: bold;
    }
    
    .timeline-date {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .timeline-body {
        margin-bottom: 0.5rem;
    }
    
    .timeline-attachments {
        background-color: #fff;
        border-radius: 0.25rem;
        padding: 0.5rem;
    }
    
    .ticket-description {
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        white-space: pre-wrap;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Refresh ticket button
        document.getElementById('refreshTicket').addEventListener('click', function() {
            window.location.reload();
        });
        
        // Fetch ticket history button
        document.getElementById('fetchTicketHistory').addEventListener('click', function() {
            const ticketId = this.getAttribute('data-ticket-id');
            
            // Show loading indicator
            const button = this;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Fetching...';
            button.disabled = true;
            
            // Make AJAX request to fetch history
            fetch(`{{ url_for('fetch_history') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `history_type=specific&ticket_id=${ticketId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Ticket history fetched successfully. Refreshing page...');
                    window.location.reload();
                } else {
                    // Show error message
                    alert('Error fetching ticket history: ' + data.message);
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while fetching ticket history.');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
    });
</script>
{% endblock %}
