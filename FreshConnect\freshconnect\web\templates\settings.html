{% extends "base.html" %}

{% block title %}FreshConnect - Settings{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-cog me-2"></i>Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-outline-secondary" id="testConnection">
            <i class="fas fa-plug me-1"></i> Test Connection
        </button>
    </div>
</div>

<!-- Settings Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-sliders-h me-1"></i> Configuration Settings
            </div>
            <div class="card-body">
                <form id="settingsForm" action="{{ url_for('save_settings') }}" method="post">
                    <!-- FreshService API Settings -->
                    <h5 class="card-title mb-3">FreshService API</h5>
                    <div class="mb-3">
                        <label for="freshserviceDomain" class="form-label">FreshService Domain</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="freshserviceDomain" name="freshservice_domain" value="{{ settings.FRESHSERVICE_DOMAIN }}" required>
                            <span class="input-group-text">.freshservice.com</span>
                        </div>
                        <div class="form-text">Your FreshService domain (e.g., 'company' for company.freshservice.com)</div>
                    </div>
                    <div class="mb-3">
                        <label for="freshserviceApiKey" class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="freshserviceApiKey" name="freshservice_api_key" value="{{ settings.FRESHSERVICE_API_KEY }}" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Your FreshService API key</div>
                    </div>

                    <!-- Database Settings -->
                    <h5 class="card-title mb-3 mt-4">Database</h5>
                    <div class="mb-3">
                        <label for="dbPath" class="form-label">Database Path</label>
                        <input type="text" class="form-control" id="dbPath" name="db_path" value="{{ settings.DATABASE_CONFIG.path }}" required>
                        <div class="form-text">Path to the SQLite database file</div>
                    </div>

                    <!-- Web Server Settings -->
                    <h5 class="card-title mb-3 mt-4">Web Server</h5>
                    <div class="mb-3">
                        <label for="webHost" class="form-label">Host</label>
                        <input type="text" class="form-control" id="webHost" name="web_host" value="{{ settings.WEB_CONFIG.host }}" required>
                        <div class="form-text">Host to bind the web server to (use 0.0.0.0 to listen on all interfaces)</div>
                    </div>
                    <div class="mb-3">
                        <label for="webPort" class="form-label">Port</label>
                        <input type="number" class="form-control" id="webPort" name="web_port" value="{{ settings.WEB_CONFIG.port }}" min="1" max="65535" required>
                        <div class="form-text">Port to run the web server on</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="webDebug" name="web_debug" value="true" {% if settings.WEB_CONFIG.debug %}checked{% endif %}>
                        <label class="form-check-label" for="webDebug">Debug Mode</label>
                        <div class="form-text">Enable debug mode for the web server (not recommended for production)</div>
                    </div>

                    <!-- Logging Settings -->
                    <h5 class="card-title mb-3 mt-4">Logging</h5>
                    <div class="mb-3">
                        <label for="logLevel" class="form-label">Log Level</label>
                        <select class="form-select" id="logLevel" name="log_level">
                            <option value="DEBUG" {% if settings.LOGGING_CONFIG.level == 'DEBUG' %}selected{% endif %}>Debug</option>
                            <option value="INFO" {% if settings.LOGGING_CONFIG.level == 'INFO' %}selected{% endif %}>Info</option>
                            <option value="WARNING" {% if settings.LOGGING_CONFIG.level == 'WARNING' %}selected{% endif %}>Warning</option>
                            <option value="ERROR" {% if settings.LOGGING_CONFIG.level == 'ERROR' %}selected{% endif %}>Error</option>
                            <option value="CRITICAL" {% if settings.LOGGING_CONFIG.level == 'CRITICAL' %}selected{% endif %}>Critical</option>
                        </select>
                        <div class="form-text">The minimum log level to record</div>
                    </div>
                    <div class="mb-3">
                        <label for="logFile" class="form-label">Log File</label>
                        <input type="text" class="form-control" id="logFile" name="log_file" value="{{ settings.LOGGING_CONFIG.file }}">
                        <div class="form-text">Path to the log file (leave empty to log to console only)</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-secondary me-md-2">Reset</button>
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- System Information -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-info-circle me-1"></i> System Information
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">Version:</dt>
                    <dd class="col-sm-7">{{ system_info.version }}</dd>

                    <dt class="col-sm-5">Python:</dt>
                    <dd class="col-sm-7">{{ system_info.python_version }}</dd>

                    <dt class="col-sm-5">Platform:</dt>
                    <dd class="col-sm-7">{{ system_info.platform }}</dd>

                    <dt class="col-sm-5">Database:</dt>
                    <dd class="col-sm-7">SQLite</dd>

                    <dt class="col-sm-5">Web Server:</dt>
                    <dd class="col-sm-7">Flask {{ system_info.flask_version }}</dd>
                </dl>
            </div>
        </div>

        <!-- Environment Variables -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-file-code me-1"></i> Environment Variables
            </div>
            <div class="card-body">
                <p>You can also configure FreshConnect using environment variables or a <code>.env</code> file:</p>

                <pre class="bg-light p-3" style="font-size: 0.8rem;">
# FreshService API Configuration
FRESHSERVICE_DOMAIN=yourdomain
FRESHSERVICE_API_KEY=your_api_key

# Database Configuration
DB_PATH=storage/freshconnect.db

# Web Server Configuration
WEB_HOST=0.0.0.0
WEB_PORT=5000
WEB_DEBUG=False

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/freshconnect.log</pre>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i> Environment variables take precedence over settings saved in the database.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Test Modal -->
<div class="modal fade" id="connectionTestModal" tabindex="-1" aria-labelledby="connectionTestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="connectionTestModalLabel">Connection Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="connectionTestSpinner" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Testing connection...</span>
                    </div>
                    <p class="mt-2">Testing connection to FreshService API...</p>
                </div>

                <div id="connectionTestResult" class="d-none">
                    <div id="connectionTestSuccess" class="d-none">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-1"></i> Connection successful!
                        </div>
                        <div id="connectionTestDetails"></div>
                    </div>

                    <div id="connectionTestError" class="d-none">
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-1"></i> Connection failed!
                        </div>
                        <div id="connectionTestErrorDetails"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle API key visibility
        document.getElementById('toggleApiKey').addEventListener('click', function() {
            const apiKeyInput = document.getElementById('freshserviceApiKey');
            const icon = this.querySelector('i');

            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                apiKeyInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Test connection button
        document.getElementById('testConnection').addEventListener('click', function() {
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('connectionTestModal'));
            modal.show();

            // Reset the modal content
            document.getElementById('connectionTestSpinner').classList.remove('d-none');
            document.getElementById('connectionTestResult').classList.add('d-none');
            document.getElementById('connectionTestSuccess').classList.add('d-none');
            document.getElementById('connectionTestError').classList.add('d-none');

            // Get the current form values
            const domain = document.getElementById('freshserviceDomain').value;
            const apiKey = document.getElementById('freshserviceApiKey').value;

            // Make the test connection request
            fetch('{{ url_for("test_connection") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    domain: domain,
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                // Hide the spinner
                document.getElementById('connectionTestSpinner').classList.add('d-none');
                document.getElementById('connectionTestResult').classList.remove('d-none');

                if (data.success) {
                    // Show success message
                    document.getElementById('connectionTestSuccess').classList.remove('d-none');

                    // Display connection details
                    const detailsDiv = document.getElementById('connectionTestDetails');
                    detailsDiv.innerHTML = `
                        <dl class="row">
                            <dt class="col-sm-4">Account:</dt>
                            <dd class="col-sm-8">${data.account_name}</dd>

                            <dt class="col-sm-4">Domain:</dt>
                            <dd class="col-sm-8">${data.domain}</dd>

                            <dt class="col-sm-4">API Version:</dt>
                            <dd class="col-sm-8">${data.api_version}</dd>

                            <dt class="col-sm-4">Rate Limit:</dt>
                            <dd class="col-sm-8">${data.rate_limit} requests per minute</dd>
                        </dl>
                    `;
                } else {
                    // Show error message
                    document.getElementById('connectionTestError').classList.remove('d-none');

                    // Display error details
                    const errorDiv = document.getElementById('connectionTestErrorDetails');
                    errorDiv.innerHTML = `
                        <p><strong>Error:</strong> ${data.message}</p>
                        <p>Please check your FreshService domain and API key.</p>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Hide the spinner
                document.getElementById('connectionTestSpinner').classList.add('d-none');
                document.getElementById('connectionTestResult').classList.remove('d-none');

                // Show error message
                document.getElementById('connectionTestError').classList.remove('d-none');

                // Display error details
                const errorDiv = document.getElementById('connectionTestErrorDetails');
                errorDiv.innerHTML = `
                    <p><strong>Error:</strong> An unexpected error occurred.</p>
                    <p>Please check your network connection and try again.</p>
                `;
            });
        });
    });
</script>
{% endblock %}
