{% extends "base.html" %}

{% block title %}FreshConnect - Dashboard{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-dashboard">
                <i class="fas fa-sync-alt me-1"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="export-dashboard">
                <i class="fas fa-download me-1"></i> Export
            </button>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="far fa-calendar-alt me-1"></i> Last 7 Days
            </button>
            <ul class="dropdown-menu" aria-labelledby="timeRangeDropdown">
                <li><a class="dropdown-item" href="#" data-range="7">Last 7 Days</a></li>
                <li><a class="dropdown-item" href="#" data-range="30">Last 30 Days</a></li>
                <li><a class="dropdown-item" href="#" data-range="90">Last 90 Days</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-range="custom">Custom Range</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-stat">
            <div class="card-body">
                <div class="stat-value text-primary">{{ stats.total_tickets|default('0') }}</div>
                <div class="stat-label">Total Tickets</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-stat">
            <div class="card-body">
                <div class="stat-value text-success">{{ stats.resolved_tickets|default('0') }}</div>
                <div class="stat-label">Resolved Tickets</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-stat">
            <div class="card-body">
                <div class="stat-value text-warning">{{ stats.pending_tickets|default('0') }}</div>
                <div class="stat-label">Pending Tickets</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-stat">
            <div class="card-body">
                <div class="stat-value text-info">{{ stats.agent_count|default('0') }}</div>
                <div class="stat-label">Active Agents</div>
            </div>
        </div>
    </div>
</div>

<!-- Ticket Activity Chart -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line me-1"></i> Ticket Activity
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="ticketActivityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-1"></i> Ticket Status
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="ticketStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agent Performance and Recent Tickets -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-tie me-1"></i> Top Agent Performance
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Agent</th>
                                <th>Tickets Resolved</th>
                                <th>Avg. Resolution Time</th>
                                <th>Satisfaction</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in top_agents|default([]) %}
                            <tr>
                                <td>{{ agent.name }}</td>
                                <td>{{ agent.tickets_resolved }}</td>
                                <td>{{ agent.avg_resolution_time }}</td>
                                <td>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ agent.satisfaction }}%;" 
                                            aria-valuenow="{{ agent.satisfaction }}" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">No agent data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-ticket-alt me-1"></i> Recent Tickets
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ticket in recent_tickets|default([]) %}
                            <tr>
                                <td><a href="{{ url_for('ticket_detail', ticket_id=ticket.id) }}">#{{ ticket.id }}</a></td>
                                <td>{{ ticket.subject }}</td>
                                <td>
                                    {% if ticket.status == 2 %}
                                    <span class="badge bg-success">Resolved</span>
                                    {% elif ticket.status == 3 %}
                                    <span class="badge bg-warning">Pending</span>
                                    {% elif ticket.status == 4 %}
                                    <span class="badge bg-danger">Closed</span>
                                    {% else %}
                                    <span class="badge bg-primary">Open</span>
                                    {% endif %}
                                </td>
                                <td>{{ ticket.updated_at }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center">No recent tickets</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sample data for charts
        const ticketActivityData = {
            labels: {{ chart_data.activity_labels|default(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])|tojson }},
            datasets: [
                {
                    label: 'New Tickets',
                    data: {{ chart_data.new_tickets|default([12, 19, 3, 5, 2, 3, 7])|tojson }},
                    borderColor: 'rgba(0, 123, 255, 1)',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Resolved Tickets',
                    data: {{ chart_data.resolved_tickets|default([7, 11, 5, 8, 3, 7, 2])|tojson }},
                    borderColor: 'rgba(40, 167, 69, 1)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        };

        const ticketStatusData = {
            labels: ['Open', 'Pending', 'Resolved', 'Closed'],
            datasets: [{
                data: {{ chart_data.status_counts|default([15, 11, 25, 9])|tojson }},
                backgroundColor: [
                    'rgba(0, 123, 255, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // Create charts
        const activityCtx = document.getElementById('ticketActivityChart').getContext('2d');
        const activityChart = new Chart(activityCtx, {
            type: 'line',
            data: ticketActivityData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        const statusCtx = document.getElementById('ticketStatusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: ticketStatusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Refresh dashboard button
        document.getElementById('refresh-dashboard').addEventListener('click', function() {
            window.location.reload();
        });

        // Time range dropdown
        document.querySelectorAll('[data-range]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const range = this.getAttribute('data-range');
                const dropdownButton = document.getElementById('timeRangeDropdown');
                
                if (range === 'custom') {
                    // Show a modal or form for custom date range
                    alert('Custom date range selector would appear here');
                } else {
                    dropdownButton.innerHTML = `<i class="far fa-calendar-alt me-1"></i> Last ${range} Days`;
                    // Here you would reload the dashboard with the new date range
                    // window.location.href = `/?range=${range}`;
                }
            });
        });
    });
</script>
{% endblock %}
