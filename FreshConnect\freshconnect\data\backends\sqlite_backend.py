"""
SQLite backend implementation for the Database interface.
"""

import os
import json
import logging
import sqlite3
from ..database import Database

logger = logging.getLogger(__name__)

class SQLiteBackend(Database):
    """SQLite implementation of the Database interface."""
    
    def __init__(self, db_path):
        """
        Initialize the SQLite backend.
        
        Args:
            db_path (str): Path to the database file
        """
        self.db_path = db_path
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        logger.debug(f"Initialized SQLite backend at {db_path}")
        
        # Create tables table to track created tables
        self._execute('''
            CREATE TABLE IF NOT EXISTS _tables (
                name TEXT PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    
    def _execute(self, query, params=None):
        """
        Execute a SQL query.
        
        Args:
            query (str): SQL query
            params (tuple, optional): Query parameters. Defaults to None.
            
        Returns:
            sqlite3.Cursor: Query cursor
        """
        cursor = self.conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        self.conn.commit()
        return cursor
    
    def _ensure_table_exists(self, table_name):
        """
        Ensure a table exists in the database.
        
        Args:
            table_name (str): Name of the table
        """
        # Check if table exists in our tracking table
        cursor = self._execute("SELECT name FROM _tables WHERE name = ?", (table_name,))
        if cursor.fetchone() is None:
            # Create the table with a flexible schema
            self._execute(f'''
                CREATE TABLE IF NOT EXISTS "{table_name}" (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Add to tracking table
            self._execute("INSERT INTO _tables (name) VALUES (?)", (table_name,))
            logger.info(f"Created table '{table_name}'")
    
    def get_table(self, table_name):
        """
        Get a table from the database.
        
        Args:
            table_name (str): Name of the table
            
        Returns:
            str: Table name (for consistency with the interface)
        """
        self._ensure_table_exists(table_name)
        return table_name
    
    def clear_and_insert_items(self, table_name, items):
        """
        Clear a table and insert new items.
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
            
        Returns:
            int: Number of items inserted
        """
        self._ensure_table_exists(table_name)
        
        # Clear the table
        self._execute(f'DELETE FROM "{table_name}"')
        
        # Insert new items
        for item in items:
            self._execute(
                f'INSERT INTO "{table_name}" (data) VALUES (?)',
                (json.dumps(item),)
            )
        
        logger.info(f"Cleared table '{table_name}' and inserted {len(items)} items")
        return len(items)
    
    def insert_items(self, table_name, items):
        """
        Insert new items without clearing the table.
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
            
        Returns:
            int: Number of items inserted
        """
        self._ensure_table_exists(table_name)
        
        # Insert new items
        for item in items:
            self._execute(
                f'INSERT INTO "{table_name}" (data) VALUES (?)',
                (json.dumps(item),)
            )
        
        logger.info(f"Inserted {len(items)} items into table '{table_name}'")
        return len(items)
    
    def find_items(self, table_name, query=None):
        """
        Find items in a table.
        
        Args:
            table_name (str): Name of the table
            query (dict, optional): Query criteria. Defaults to None.
            
        Returns:
            list: Matching items
        """
        self._ensure_table_exists(table_name)
        
        if query is None:
            # Return all items
            cursor = self._execute(f'SELECT data FROM "{table_name}"')
        else:
            # This is a simplified implementation that only supports exact matches
            # A more robust implementation would parse the query and build a SQL WHERE clause
            where_clauses = []
            params = []
            
            for key, value in query.items():
                where_clauses.append(f"json_extract(data, '$.{key}') = ?")
                params.append(value)
            
            where_clause = " AND ".join(where_clauses)
            cursor = self._execute(f'SELECT data FROM "{table_name}" WHERE {where_clause}', tuple(params))
        
        # Parse JSON data
        return [json.loads(row['data']) for row in cursor.fetchall()]
    
    def update_items(self, table_name, query, update_data):
        """
        Update items in a table.
        
        Args:
            table_name (str): Name of the table
            query (dict): Query criteria
            update_data (dict): Data to update
            
        Returns:
            int: Number of items updated
        """
        self._ensure_table_exists(table_name)
        
        # Find matching items
        items = self.find_items(table_name, query)
        
        # Update each item
        for item in items:
            # Apply updates
            item.update(update_data)
            
            # Update in database
            self._execute(
                f'UPDATE "{table_name}" SET data = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                (json.dumps(item), item.get('id'))
            )
        
        logger.info(f"Updated {len(items)} items in table '{table_name}'")
        return len(items)
    
    def delete_items(self, table_name, query):
        """
        Delete items from a table.
        
        Args:
            table_name (str): Name of the table
            query (dict): Query criteria
            
        Returns:
            int: Number of items deleted
        """
        self._ensure_table_exists(table_name)
        
        # This is a simplified implementation that only supports exact matches
        where_clauses = []
        params = []
        
        for key, value in query.items():
            where_clauses.append(f"json_extract(data, '$.{key}') = ?")
            params.append(value)
        
        where_clause = " AND ".join(where_clauses)
        cursor = self._execute(f'DELETE FROM "{table_name}" WHERE {where_clause}', tuple(params))
        
        deleted_count = cursor.rowcount
        logger.info(f"Deleted {deleted_count} items from table '{table_name}'")
        return deleted_count
