"""
Database interface for FreshConnect.

This module provides a unified interface for different database backends.
"""

import os
import logging
from ..config import settings

logger = logging.getLogger(__name__)

class DatabaseFactory:
    """Factory class for creating database instances."""

    @staticmethod
    def get_database():
        """
        Get a SQLite database instance based on configuration.

        Returns:
            Database: A SQLite database instance
        """
        db_path = settings.DATABASE_CONFIG['path']

        # Ensure the directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        from .backends.sqlite_backend import SQLiteBackend
        logger.info(f"Using SQLite backend at {db_path}")
        return SQLiteBackend(db_path)


class Database:
    """Base class for database backends."""

    def get_table(self, table_name):
        """
        Get a table or collection from the database.

        Args:
            table_name (str): Name of the table

        Returns:
            object: Table or collection object
        """
        raise NotImplementedError("Subclasses must implement get_table")

    def clear_and_insert_items(self, table_name, items):
        """
        Clear a table and insert new items.

        Args:
            table_name (str): Name of the table
            items (list): List of items to insert

        Returns:
            int: Number of items inserted
        """
        raise NotImplementedError("Subclasses must implement clear_and_insert_items")

    def insert_items(self, table_name, items):
        """
        Insert new items without clearing the table.

        Args:
            table_name (str): Name of the table
            items (list): List of items to insert

        Returns:
            int: Number of items inserted
        """
        raise NotImplementedError("Subclasses must implement insert_items")

    def find_items(self, table_name, query=None):
        """
        Find items in a table.

        Args:
            table_name (str): Name of the table
            query (dict, optional): Query criteria. Defaults to None.

        Returns:
            list: Matching items
        """
        raise NotImplementedError("Subclasses must implement find_items")

    def update_items(self, table_name, query, update_data):
        """
        Update items in a table.

        Args:
            table_name (str): Name of the table
            query (dict): Query criteria
            update_data (dict): Data to update

        Returns:
            int: Number of items updated
        """
        raise NotImplementedError("Subclasses must implement update_items")

    def delete_items(self, table_name, query):
        """
        Delete items from a table.

        Args:
            table_name (str): Name of the table
            query (dict): Query criteria

        Returns:
            int: Number of items deleted
        """
        raise NotImplementedError("Subclasses must implement delete_items")


def initialize_db():
    """
    Initialize the database based on configuration.

    Returns:
        Database: A database instance
    """
    try:
        db = DatabaseFactory.get_database()
        logger.info(f"Successfully initialized database")
        return db
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise
