import time
import requests
from base64 import b64encode
from dbFunctions import initialize_db
from datetime import datetime
import os
from dotenv import load_dotenv

class ServiceCatalogFetcher:
    def __init__(self, domain, api_key):
        """
        Initialize the FreshService service catalog fetcher
        
        Args:
            domain (str): Your FreshService domain (e.g., 'company.freshservice.com')
            api_key (str): Your FreshService API key
        """
        self.base_url = f"https://{domain}/api/v2"
        self.auth_token = b64encode(f"{api_key}:X".encode()).decode()
        self.headers = {
            'Authorization': f'Basic {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
    def get_service_items(self):
        """
        Fetch all service catalog items with rate limit handling
        
        Returns:
            list: List of service catalog items
        """
        items = []
        page = 1
        per_page = 30
        
        while True:
            url = f"{self.base_url}/service_catalog/items"
            params = {
                'page': page,
                'per_page': per_page
            }
            
            response = requests.get(url, headers=self.headers, params=params)
            
            # Check rate limits
            remaining = int(response.headers.get('X-Ratelimit-Remaining', 100))
            if remaining < 50:
                time.sleep(1)  # Pause for 1 second when rate limit is low
                
            if response.status_code == 200:
                data = response.json()
                current_items = data.get('service_items', [])
                
                if not current_items:  # No more items to fetch
                    break
                    
                items.extend(current_items)
                page += 1
            else:
                raise Exception(f"Failed to fetch service items: {response.status_code}")
                
        return items
    

def get_env_config():
    """
    Retrieve configuration from environment variables with fallback to .env file
    
    Returns:
        tuple: (domain, api_key)
    """
    # Try getting from environment variables first
    domain = os.getenv('FRESHSERVICE_DOMAIN')
    api_key = os.getenv('FRESHSERVICE_API_KEY')
    
    # If not found in environment, try loading from .env file
    if not (domain and api_key):
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            domain = os.getenv('FRESHSERVICE_DOMAIN')
            api_key = os.getenv('FRESHSERVICE_API_KEY')
        except ImportError:
            print("python-dotenv not installed. Using only environment variables.")
    
    # Validate configuration
    if not domain:
        raise ValueError("FRESHSERVICE_DOMAIN environment variable not set")
    
    if not api_key:
        raise ValueError("FRESHSERVICE_API_KEY environment variable not set")
    
    return domain, api_key

def main():
    # Load environment variables
    domain, api_key = get_env_config()
    
    # Initialize fetcher
    fetcher = ServiceCatalogFetcher(domain, api_key)
    
    try:
        # Fetch all service items
        items = fetcher.get_service_items()
        
        # Store items using DatabaseManager
        db_manager = initialize_db('ServiceItems.json')
        items_stored = db_manager.clear_and_insert_items('service_items', items)
        
        print(f"Successfully stored {items_stored} service catalog items")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
