{"metadata": {"total_tickets": 10, "fetch_timestamp": "2025-07-11T11:17:09.157567", "domain": "kaservicedesk.freshservice.com"}, "tickets": [{"ticket_id": 1244, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] Surprise", "group_id": 19000171671, "department_id": 19000141829, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000460949, "responder_id": ***********, "due_by": "2021-12-07T13:55:44Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-29T21:55:44Z", "id": 1244, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T13:54:57Z", "updated_at": "2021-11-29T19:33:44Z", "workspace_id": 2, "requested_for_id": 19000460949, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div dir=\"ltr\">\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">Hi <PERSON><PERSON> </div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">\n<br>\n<span style=\"color:rgb(34,34,34);font-family:Arial,Helvetica,sans-serif;font-size:small\">I'm planning a little surprise for some of the staff today, and y</span>our assistance is needed.</div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">Get back to me soon, thanks.<br>\n</div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\nBest regards,</div>\n<PERSON>calf<br>\nPresident<br>\n<div style=\"color:rgb(0,0,0);font-family:<PERSON><PERSON><PERSON>;font-size:12px\">sent from my mobile device</div>\n</div>\n\n\n", "description_text": "\r\n\r\n \r\n\r\n\r\n \r\n Hi <PERSON><PERSON> \r\n \r\nI'm planning a little surprise for some of the staff today, and your assistance is needed.\r\n Get back to me soon, thanks. \r\n\r\n \r\n \r\n \r\n \r\n \r\n \r\n \r\nBest regards,\r\n<PERSON> \r\nPresident \r\n sent from my mobile device\r\n\r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked \r\n\r\n<EMAIL>\r\n\r\nno malicious links", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333435&Signature=HvMzNPl1fx3XHE3NU6AkiO2KufciK7PFiEu5xNlvnOLmFQ2LMjQWa7q8AAQI~MgHVEG2~OeNoaRbiaRQp1HpYGF~-~0VN64Mud24BFeoP3yis-lDf25o5aPDf~njB1bWGCXbzhAk-jplX7BhmoP2foz5e6b0NluRN9lq9qhymcYuxy~PEU2kZTw2sjlbrSIn~UkQlJ4ZGB1ES0wVNqNdD60-7sJmV4ATeJ3H81TNUC2cafFVVVl39drw6p7uDZ04EHWlqHA6Bm0SZ8aMp-U1PqpiB8QxSdQBEQnq48u643dA2HYKuvuav-8u0ZgJrAUfz7Mm14dE99XGNBWgnJLGKQ__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T13:54:57Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 8798, "updated_at": "2021-11-29T13:54:57Z"}]}, "conversations": [], "conversation_count": 0, "fetch_status": "success"}, {"ticket_id": 1248, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] ", "group_id": 19000171671, "department_id": 19000141829, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000460537, "responder_id": ***********, "due_by": "2021-12-07T14:43:02Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-29T22:43:02Z", "id": 1248, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T14:42:36Z", "updated_at": "2021-11-29T19:33:02Z", "workspace_id": 2, "requested_for_id": 19000460537, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div>Hi <PERSON><br><br><br>Send me your cell number real quick, I need to convey a message to you<br>over the phone..<br><br><br>Thanks,<br><PERSON><br>Sales Director at Tate Access Floors<br><br><br>Get  Outlook for iOS<br><br><br>\n</div>\n", "description_text": "Hi <PERSON>\r\n\r\n\r\nSend me your cell number real quick, I need to convey a message to you\r\nover the phone..\r\n\r\n\r\nThanks,\r\n<PERSON>\r\nSales Director at Tate Access Floors\r\n\r\n\r\nGet  Outlook for iOS\r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked \r\n\r\nkate<PERSON><EMAIL>\r\n\r\nno malicious links", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333435&Signature=n10bCWGl2ubeo~WF9Du~F6HVrUZir8k-ajWZtyfl0e7p4iO90Dy9XWc~nDd2tOPx7fC3-37wZexfWz6zagb39PkTa5-mn0riUZTwHh3WTZojMjK-IYm4RIJmS9WFcjMWCjK0ubFvGCbIX0VVgf9n0at-0t4p9NlHARRqakjYMw1BQ~BjWl1itbh3bak~A5B-AK4zA5DbZEJy61EysLvx-qG4opU5XC7Ntz-fbqD-3EAtExMMlFrP7hiJv47lVtJuaFa4dDQwPVuKJUKfwDQSS3~NlkAQCtyjqzDQ-KWZaup-4acSh-Ggwpxej2A5OU4L6T2ZFi20YAJN69COrzeXZQ__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T14:42:36Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 7892, "updated_at": "2021-11-29T14:42:36Z"}]}, "conversations": [], "conversation_count": 0, "fetch_status": "success"}, {"ticket_id": 1254, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] Re: Caledon Review", "group_id": 19000171671, "department_id": 19000141831, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000460615, "responder_id": ***********, "due_by": "2021-12-07T15:15:36Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-29T23:15:36Z", "id": 1254, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T15:14:53Z", "updated_at": "2021-11-29T19:27:36Z", "workspace_id": 2, "requested_for_id": 19000460615, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div>Hello! I have sent you some additional info about the recent contract and payslip. To close this problem, please follow steps via the link lower:<br>\n<br>\n<br>\n1)portal.squarefeetasia.com/facilisenim/doloremquetenetur-7380934<br>\n<br>\n2)nuhydrocrm.ehostinguk.com/temporeodio/culpaex-7380934<br>\n<br>\n</div>\n<blockquote>\n\n\n<font><span style=\"font-size:11pt;\">\n<div></div>\n<div></div>\n<div>.......................................<font> <br>\n</font>To join the meeting on a computer or mobile phone: <a href=\"https://bluejeans.com/*********?src=calendarLink\" rel=\"noreferrer\">\n<font color=\"blue\"><u>https://bluejeans.com/*********?src=calendarLink</u></font></a><font>\n</font>\n</div>\n<div></div>\n<div></div>\n<div></div>\n<div><font><span style=\"font-size:12pt;\">Phone Dial-in<font>\n<br>\n</font>+61.2.8103.4256 (Sydney, Australia)<font> <br>\n</font>+32.3.808.4256 (Belgium (Antwerp))<font> <br>\n</font>+**********.12.56 (France (Southeast France))<font> <br>\n</font>+353.1.697.1256 (Ireland (Dublin))<font> <br>\n</font>+31.20.808.2256 (Netherlands (Amsterdam))<font> <br>\n</font>+65.3157.6747 (Singapore (National))<font> <br>\n</font>+44.************ (UK (London))<font> <br>\n</font>*************** (US (San Jose))<font> <br>\n</font>Global Numbers: <a href=\"https://www.bluejeans.com/premium-numbers\" rel=\"noreferrer\"><font color=\"blue\"><u>https://www.bluejeans.com/premium-numbers</u></font></a><font>\n</font></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\">Meeting ID: ***********<font>\n</font></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\">Room System<font> <br>\n</font>199.48.152.152 or bjn.vc<font> </font></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\">Meeting ID: ***********<font>\n</font></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\">Want to test your video connection?<font>\n<br>\n</font><a href=\"https://bluejeans.com/111\" rel=\"noreferrer\"><font color=\"blue\"><u>https://bluejeans.com/111</u></font></a><font>\n</font></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div><font><span style=\"font-size:12pt;\"></span></font></div>\n<div>Is this your meeting? Do you need your <a href=\"https://KIP.bluejeans.com/settings/room-settings\" rel=\"noreferrer\">\n<font color=\"blue\"><u>moderator passcode</u></font></a>?<font> <br>\n</font>.......................................<font> </font>\n</div>\n<div></div>\n<div></div>\n</span></font>\n<div dir=\"ltr\" style=\"mso-line-height-rule:exactly;-webkit-text-size-adjust:100%;direction:ltr;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#060000;font-style:normal;font-weight:700;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"padding:10px 0 0;vertical-align:top;font-family:'Century Gothic';\">\nViv Gibney<span style=\"font-family:remialcxesans;font-size:1px;color:#FFFFFF;line-height:1px;\"></span>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#0B0000;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"padding:0 0 10px;vertical-align:top;font-family:'Century Gothic';\">\nGlobal Engineering Director</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;line-height:normal;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"padding:10px 0;vertical-align:top;\"><a href=\"http://www.kingspan.com/\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img width=\"100\" alt=\"\" style=\"width:100px;min-width:100px;max-width:100px;font-size:0;\" src=\"cid:image093102.png@31397B4A.83605646\"></a></td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:12px;color:#1D1515;font-style:normal;font-weight:700;white-space:nowrap;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">Kingspan Ltd</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#2D2E31;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">Carrickmacross Road</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">|</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">Kingscourt</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">|</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">Co. Cavan</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">|</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">A82 E897</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:12px;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">Ireland</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#2D2E31;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">T:<a href=\"tel:+353%2042%209698%20683\" target=\"_blank\" style=\"text-decoration:none;color:#2D2E31;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">+353 42 9698 683</strong></a>\n</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">|</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">M:<a href=\"tel:+353%2087%20915%200220\" target=\"_blank\" style=\"text-decoration:none;color:#2D2E31;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">+353 87 915 0220</strong></a>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:12px;color:#2D2E31;font-style:normal;font-weight:400;white-space:nowrap;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\">E: <a href=\"mailto:<EMAIL>\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\">\n<strong style=\"font-weight:400;\"><EMAIL></strong></a>\n</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:20px;\">\n<td align=\"left\" style=\"padding:20px 0 10px;vertical-align:top;font-family:'Century Gothic';\">\n<a href=\"https://www.kingspan.com/irl/en-ie/product-groups/insulated-panel-systems\" target=\"_blank\" title=\"Kingspan Insulated Panels Ireland\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">www.kingspanpanels.ie</strong></a>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:0;line-height:normal;\">\n<td align=\"left\" style=\"vertical-align:top;\"><a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-insulated-panels\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img alt=\"\" style=\"font-size:0;\" src=\"cid:image563040.gif@1E4FD601.60D2FFF3\"></a></td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"padding:0 0 6px;vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"white-space:normal;color:#C0C0C0;font-size:12px;font-family:'Century Gothic';font-weight:400;font-style:normal;text-align:justify;width:100%;\">\n<tbody>\n<tr style=\"font-size:10.67px;\">\n<td style=\"font-family:Calibri;\">\n<span style=\"font-size:9.33px;\">This email and its attachments may be confidential and are intended solely for the use of the individual to whom it is addressed. Any views or opinions expressed are solely those of the author\n and do not necessarily represent those of Kingspan Limited. If you are not the intended recipient of this email and its attachments, you must take no action based upon them, nor must you copy or show them to anyone. Please contact the sender if you believe\n you have received this email in error.</span> v1037468<br>\n<br>\n<span style=\"font-size:9.33px;\">In accordance with GDPR, Kingspan is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at\n<span style=\"color:#333333;font-family:Roboto;\"><a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-insulated-panels/privacy-statements\" target=\"_blank\" style=\"text-decoration:none;color:#C0C0C0;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\"><span style=\"text-decoration:underline;\">https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-insulated-panels/privacy-statements</span></strong></a><br>\n<span style=\"color:#333333;\"></span></span></span><br>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n<tr style=\"font-size:0;\">\n<td align=\"left\" style=\"vertical-align:top;\">\n<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"white-space:normal;color:#C0C0C0;font-size:12px;font-family:'Century Gothic';font-weight:400;font-style:normal;text-align:justify;width:100%;\">\n<tbody>\n<tr style=\"font-size:10.67px;\">\n<td style=\"font-family:'Century Gothic';\">\n<span style=\"font-size:9.33px;font-family:Calibri;\">Kingspan Limited, a company registered in England and Wales,\n<br>\ncompany no. 1037468. Registered Office: Greenfield Business Park No. 2, <br>\nGreenfield, Holywell, Flintshire, North Wales. CH8 7GJ Tel: +44 (0) 1352 716100</span><br>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</div>\n</blockquote>\n\n\n", "description_text": "Hello! I have sent you some additional info about the recent contract and payslip. To close this problem, please follow steps via the link lower:\r\n\r\n\r\n1)portal.squarefeetasia.com/facilisenim/doloremquetenetur-7380934\r\n\r\n2)nuhydrocrm.ehostinguk.com/temporeodio/culpaex-7380934\r\n\r\n.......................................\r\nTo join the meeting on a computer or mobile phone: https://bluejeans.com/*********?src=calendarLink\r\nPhone Dial-in\r\n+61.2.8103.4256 (Sydney, Australia)\r\n+32.3.808.4256 (Belgium (Antwerp))\r\n+**********.12.56 (France (Southeast France))\r\n+353.1.697.1256 (Ireland (Dublin))\r\n+31.20.808.2256 (Netherlands (Amsterdam))\r\n+65.3157.6747 (Singapore (National))\r\n+44.************ (UK (London))\r\n*************** (US (San Jose))\r\nGlobal Numbers: https://www.bluejeans.com/premium-numbers\r\nMeeting ID: ***********\r\nRoom System\r\n199.48.152.152 or bjn.vc\r\nMeeting ID: ***********\r\nWant to test your video connection?\r\nhttps://bluejeans.com/111\r\nIs this your meeting? Do you need your moderator passcode<https://KIP.bluejeans.com/settings/room-settings>?\r\n.......................................\r\nViv Gibney\r\nGlobal Engineering Director\r\n[cid:image093102.png@31397B4A.83605646]<http://www.kingspan.com/>\r\nKingspan Ltd\r\nCarrickmacross Road     |       Kingscourt      |       Co. Cavan       |       A82 E897\r\nIreland\r\nT:+353 42 9698 683<tel:+353%2042%209698%20683>  |       M:+353 87 915 0220<tel:+353%2087%20915%200220>\r\nE: <EMAIL><mailto:<EMAIL>>\r\nwww.kingspanpanels.ie<https://www.kingspan.com/irl/en-ie/product-groups/insulated-panel-systems>\r\n[cid:image563040.gif@1E4FD601.60D2FFF3]<https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-insulated-panels>\r\nThis email and its attachments may be confidential and are intended solely for the use of the individual to whom it is addressed. Any views or opinions expressed are solely those of the author and do not necessarily represent those of Kingspan Limited. If you are not the intended recipient of this email and its attachments, you must take no action based upon them, nor must you copy or show them to anyone. Please contact the sender if you believe you have received this email in error. v1037468\r\n\r\nIn accordance with GDPR, Kingspan is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-insulated-panels/privacy-statements\r\n\r\nKingspan Limited, a company registered in England and Wales,\r\ncompany no. 1037468. Registered Office: Greenfield Business Park No. 2,\r\nGreenfield, Holywell, Flintshire, North Wales. CH8 7GJ Tel: +44 (0) 1352 716100\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked:\r\n<EMAIL>\r\nemail.tecnip.com \r\n", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333436&Signature=YXZPCR0fIc-PnYiZqwr3W-7x-Wno4yshuURl3b58hh0B2Ury7wpGySM2qIPLYSnG8QCZ4NPPFE~U8reOxoAo18aFQH4mAKNmqHydNfLm0xnLkjSf-Yfd-uV2rYP1jsikQAnHrGGivXeA08Zh~S3dQZ9nsQsjdZJ4WH62ZlqpblQ5dOHCZUi7x5pD~k513AkZgP0nyJiMCwyHOmqVJVUWlEpRV-c5SSy9YXlHOfFIiml-DEfB~oe4MSpl2kx67yKewTXTYl1ID-2o7sPHEG1JWxhIfSk-wsrwcVEBH3X2KngLs1JhcfwQ5CFZvmjsjHX2io7rbk7Wkt3SzLLBE5zqFA__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T15:14:53Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 22616, "updated_at": "2021-11-29T15:14:53Z"}]}, "conversations": [], "conversation_count": 0, "fetch_status": "success"}, {"ticket_id": 1255, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] ", "group_id": 19000171671, "department_id": 19000141829, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000460915, "responder_id": ***********, "due_by": "2021-12-07T15:19:43Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-29T23:19:43Z", "id": 1255, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T15:19:37Z", "updated_at": "2021-11-29T18:50:43Z", "workspace_id": 2, "requested_for_id": 19000460915, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div>Hi <PERSON><br><br><br>Send me your cell number real quick, I need to convey a message to you<br>over the phone..<br><br><br>Thanks,<br><PERSON><br>Sales Director at Tate Access Floors<br><br><br>Get  Outlook for iOS<br><br><br>\n</div>\n", "description_text": "Hi <PERSON>\r\n\r\n\r\nSend me your cell number real quick, I need to convey a message to you\r\nover the phone..\r\n\r\n\r\nThanks,\r\n<PERSON>\r\nSales Director at Tate Access Floors\r\n\r\n\r\nGet  Outlook for iOS\r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked <PERSON> <<EMAIL>>\r\n\r\nno malicious links\r\n", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333436&Signature=o3esu-B5yRuQlmBddkTFxIiWfiirRW-~MBjmGIz5Fo0OX5c0L7LtkOTMdqZ~mtYeYYWS0y1h4xuxK2TccR53BCz7fv3Z1LAp3VbCBWEknBAWqnLM1nth~P7gs8pmLn~va9z0B-4QpKER5i~Kh8BO9SXRw0fFgXgqN4e-BfbFNXVMy1RH8veN8n8kk65fSu6CDx4eQdz5s1TbIk6JR4DkD4aNWR8v59BBW2T0VREQPQOuhmPfXmjFBXS--tGJm6ttlUCRUVTUFpW99GtunZM5cePf2X04GTlQNEHLNrOT2DJAi-OVU~s1bgc~iwKuz7DuGvruaS6r~W6N5AOJKTi1-w__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T15:19:37Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 7889, "updated_at": "2021-11-29T15:19:37Z"}]}, "conversations": [], "conversation_count": 0, "fetch_status": "success"}, {"ticket_id": 1265, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] Surprise", "group_id": 19000171671, "department_id": 19000141829, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000460879, "responder_id": ***********, "due_by": "2021-12-07T16:21:12Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-30T00:21:12Z", "id": 1265, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T16:20:26Z", "updated_at": "2021-11-29T18:27:12Z", "workspace_id": 2, "requested_for_id": 19000460879, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div dir=\"ltr\">\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">Hi <PERSON> </div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">\n<br>\n<span style=\"color:rgb(34,34,34);font-family:Arial,Helvetica,sans-serif;font-size:small\">I'm planning a little surprise for some of the staff today, and y</span>our assistance is needed.</div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">Get back to me soon, thanks.<br>\n</div>\n<div style=\"color:rgb(0,0,0);font-family:Verdana;font-size:12px\">\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\n<br>\nBest regards,</div>\n<PERSON><br>\nPresident<br>\n<div style=\"color:rgb(0,0,0);font-family:<PERSON><PERSON><PERSON>;font-size:12px\">sent from my mobile device</div>\n</div>\n\n\n", "description_text": "\r\n\r\n \r\n\r\n\r\n \r\n Hi <PERSON> \r\n \r\nI'm planning a little surprise for some of the staff today, and your assistance is needed.\r\n Get back to me soon, thanks. \r\n\r\n \r\n \r\n \r\n \r\n \r\n \r\n \r\nBest regards,\r\n<PERSON> \r\nPresident \r\n sent from my mobile device\r\n\r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked <PERSON> Metcalf <<EMAIL>>\r\n\r\nNo malicious links.\r\nMail server cannot be blocked as it's rooted to google domain", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333436&Signature=O~~wV9sd~~~qZaQ8kPMqzKGWFeGwjjy1K5DMOzZeXWzNp0pr2ZCAJwUYnTsLq84rVVhHAwv81XUNCsqBL-aznb2DJ6j8E~6lZLp4xFBagKunaz7RqD451bqsWBXgPUsXvWBhsP-UPSxMVdEHZIIeIQ~e2n6Dkw5FuMeFVXQvv4tkAbAxafI3ICgwiilJ~JpWjU66pbdhfpNgiEuNgG1ffAG1yFXwJH0ib~M2DXM3IgwVurlGjVjGQE81OtVdkxtrK1tyfGcszMHCG0G8klFZ40sRbLQnlqICMjOBZuBfrvE3H~vjISczhtHpN9qJge2sUqCqDDSE2yDq~jWXYoEyXw__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T16:20:26Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 8799, "updated_at": "2021-11-29T16:20:26Z"}]}, "conversations": [], "conversation_count": 0, "fetch_status": "success"}, {"ticket_id": 1287, "ticket_details": {"planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "Possible Phishing email.", "group_id": 19000171670, "department_id": 19000141863, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000463919, "responder_id": ***********, "due_by": "2021-12-07T18:58:11Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": [], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-11-30T14:58:23Z", "id": 1287, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-29T18:57:48Z", "updated_at": "2021-11-29T20:25:11Z", "workspace_id": 2, "requested_for_id": 19000463919, "to_emails": ["Helpdesk <<EMAIL>>"], "type": "Incident", "description": "<div>\n<div>I get an email today that I think might be a Phishing  scam or something. Are you able to tell me if this is legit or not?</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div><b><span lang=\"EN-CA\" style='font-size:14.0pt;font-family:\"Arial\",sans-serif;color:#0070C0'>Jeffrey</span></b></div>\n<div></div>\n<div></div>\n<div>\n<span lang=\"EN-CA\">Shipping Coordinator</span><span lang=\"EN-CA\" style='font-family:\"Wide Latin\",serif'></span>\n</div>\n<div></div>\n<div></div>\n<div>\n<span lang=\"EN-CA\"><img width=\"360\" height=\"34\" style=\"width:3.75in;height:.3541in\" src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NDEwNjcsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.dpG09AMtIOBzRGIih-qaeG9A521tXjjhRUbm0QgLW3o\" class=\"inline-image\" data-id = \"19003441067\" data-store-type = \"1\"></span><span lang=\"EN-CA\"></span>\n</div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\"></span></div>\n<div> </div>\n<div></div>\n<div><span lang=\"EN-CA\">Vicwest Saskatoon</span></div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\">3542 Millar Avenue</span></div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\">Saskatoon, SK   S7P 0B6</span></div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\">Tel: ************ </span></div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\">Fax: ************</span></div>\n<div></div>\n<div></div>\n<div><span lang=\"EN-CA\"></span></div>\n<div> </div>\n<div></div>\n<div>\n<span lang=\"EN-CA\" style=\"font-size:9.0pt;color:black\">This email is confidential.  If you are not the intended recipient, please notify us immediately and delete the email without retaining or printing a copy.  Vicwest takes responsibility\n</span><span lang=\"EN-CA\" style=\"font-size:9.0pt\">for<span style=\"color:black\"> your privacy and the information we collect.  To view our updated Privacy Policy, please visit our website at\n</span><a href=\"https://vicwest.com/privacy-policy/\" rel=\"noreferrer\"><span style=\"color:black\">https://vicwest.com/privacy-policy/</span></a><span style=\"color:black\"> for the latest version.  If you would like a copy of this policy mailed to you, please contact our Privacy\n Team at </span><a href=\"mailto:<EMAIL>\" rel=\"noreferrer\"><span style=\"color:black\"><EMAIL></span></a><span style=\"color:black\">.  If you would like to unsubscribe from email communication from Vicwest, please reply to this email and type UNSUBSCRIBE. Vicwest\n can still communicate information about product and pricing updates.</span></span>\n</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n</div>\n\n\n", "description_text": "I get an email today that I think might be a Phishing  scam or something. Are you able to tell me if this is legit or not?\r\n\r\n\r\n\r\n\r\n\r\n\r\n<PERSON>\r\nShipping Coordinator\r\n[cid:image001.png@01D7E520.AA17CE40]\r\n\r\nVicwest Saskatoon\r\n3542 Millar Avenue\r\nSaskatoon, SK   S7P 0B6\r\nTel: ************\r\nFax: ************\r\n\r\nThis email is confidential.  If you are not the intended recipient, please notify us immediately and delete the email without retaining or printing a copy.  Vicwest takes responsibility for your privacy and the information we collect.  To view our updated Privacy Policy, please visit our website at https://vicwest.com/privacy-policy/ for the latest version.  If you would like a copy of this policy mailed to you, please contact our Privacy <NAME_EMAIL><mailto:<EMAIL>>.  If you would like to unsubscribe from email communication from Vicwest, please reply to this email and type UNSUBSCRIBE. Vicwest can still communicate information about product and pricing updates.\r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Service Delivery", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "Instructed user on how to report phish alerts", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/attachment.eml?response-content-type=application/octet-stream&Expires=1752333436&Signature=mOKllbtJH5JNTczr7CNWSPrCw7sQUFKX-w0oP9nKidbIi0IayB0Chjvz0WY9KW4sjEpHLqaFJASzYOAW33YLilKQ~l20G9IBD3p40HrkZjggz0x36PKCM3JTUhye4Yl1GUGTFTKC25Ue15NjexNcNByrqBTrbIJhXh-e1BeKtOD0TAqpuizwQDkEoNp7AlK5cQPxHCRzgg0pGmAgTm5zSKYNiH5UySar4WVqnkk0yN3lAkj1YI0EjtjVSgrl6hs0fzRqSBdNpCWtR8gPTDzf~XckJGr6gLwYb9aUJHBJCe2Sqe~AZ3ubTx7umUEzZaLFZMfxu6vj-Vsbrgtv3-LPIA__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-29T18:57:47Z", "id": ***********, "name": "attachment.eml", "size": 452299, "updated_at": "2021-11-29T18:57:48Z"}]}, "conversations": [{"id": ***********, "user_id": ***********, "to_emails": ["<EMAIL>"], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, sans-serif;'>\n<div></div>\n<div>\n<div>Hi <span style=\"\"><PERSON>,</span>\n</div>\n<div><br></div>\n<div style=\"margin-bottom:15px;\"><span rel=\"tempredactor\" style=\"color: rgb(18, 52, 77);\">We appreciate you for alerting us about a phishing email. However, this is not the correct to submit phishing emails. Please refer to the following link for reporting phishing emails. <a href=\"https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx%C2%A0\" rel=\"noreferrer\">https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx </a></span></div>\n<div style=\"margin-bottom:15px;\">\n<font color=\"#12344d\"><br>Thank you,<br></font><br>\n</div>\n</div>\n</div>", "body_text": "<PERSON>,     We appreciate you for alerting us about a phishing email. However, this is not the correct to submit phishing emails. Please refer to the following link for reporting phishing emails. https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx    Thank you,", "ticket_id": 1287, "created_at": "2021-11-29T20:24:02Z", "updated_at": "2021-11-29T20:24:02Z", "incoming": false, "private": false, "support_email": "<EMAIL>", "source": 0, "from_email": "\"<PERSON><PERSON> Gobel\" <<EMAIL>>", "cc_emails": [], "bcc_emails": [], "attachments": []}], "conversation_count": 1, "fetch_status": "success"}, {"ticket_id": 1401, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON> Alert] Eastern Corporation Final RFP EC90833", "group_id": 19000171671, "department_id": 19000141831, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000461222, "responder_id": ***********, "due_by": "2021-12-08T16:35:14Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-12-01T00:35:14Z", "id": 1401, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-30T16:34:44Z", "updated_at": "2021-11-30T21:27:14Z", "workspace_id": 2, "requested_for_id": 19000461222, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div>\n<div><a href=\"https://lnkd.in/egJRNtHf\" rel=\"noreferrer\"><span style=\"color:windowtext;text-decoration:none\"><img width=\"285\" height=\"64\" style=\"width:2.9687in;height:.6666in\" alt=\"Graphical user interface, text\n\nDescription automatically generated\" src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NTU1ODcsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.76IGiQs7JzNVzZ6R3gCDvMvvn5wOF-jdQ1OMLJsJnS8\" class=\"inline-image\" data-id = \"19003455587\" data-store-type = \"1\"></span></a></div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div>See attached ,</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div style=\"margin:0in;background:white\"><span style=\"font-size:12.0pt;color:black\">Ann Seo</span></div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\"></span>\n</div>\n<div> </div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black;border:none windowtext 1.0pt;padding:0in\">Eastern Corporation</span><span style=\"font-size:12.0pt;color:black\"></span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">124 Franklin Park Drive</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Youngsville, NC 27596</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Mobile ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Office ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Fax ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\"><a href=\"http://usaeastern.com\" rel=\"noreferrer\">http://usaeastern.com</a></span>\n</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n</div>\n<div dir=\"ltr\" style=\"mso-line-height-rule:exactly;-webkit-text-size-adjust:100%;direction:ltr;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:10px 0 0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:700;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">John Buchanan<span style=\"font-family:remialcxesans;font-size:1px;color:#FFFFFF;line-height:1px;\">​</span>\n</td></tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Project Estimator</td></tr>\n</tbody>\n</table></td></tr></tbody></table></td></tr>\n<tr style=\"font-size:0;line-height:normal;\"><td align=\"left\" style=\"padding:20px 0 10px;vertical-align:middle;\"><a href=\"https://www.kingspan.com/us/en-us/about-kingspan/kingspan-insulated-panels\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NTU1ODgsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.r_r4LO8psm0x1xgZsqJEPMy3i5-U9S9j8hc1Bt5Fp4I\" class=\"inline-image\" data-id = \"19003455588\" data-store-type = \"1\" width=\"75\" title=\"Kingspan Insulated Panels Website\" alt=\"Kingspan Insulated Panels Website\" style=\"width:75px;min-width:75px;max-width:75px;font-size:12px;\"></a></td></tr>\n<tr style=\"font-size:12px;color:#2D2E31;font-style:normal;font-weight:700;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Kingspan Insulated Panels</td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;color:#2D2E31;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">720 Marion Road</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Columbus</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">OH</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">43207</td>\n</tr></tbody></table></td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">D: 614‑502‑7591</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | <br>\n</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">T: <a href=\"tel:************\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">************</strong></a>\n</td>\n</tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">E: <a href=\"mailto:<EMAIL>\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\"><EMAIL></strong></a>\n</td></tr>\n<tr style=\"font-size:12px;color:#030303;font-style:normal;font-weight:700;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">\n<a href=\"http://www.kingspanpanels.com/\" target=\"_blank\" style=\"text-decoration:none;color:#030303;\" rel=\"noreferrer\"><strong style=\"font-weight:700;\">www.kingspanpanels.com</strong></a><br>\n</td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:10px 0 0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;color:#909090;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;line-height:normal;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0 0 10px;vertical-align:top;\"><a href=\"https://www.kingspan.com/us/en-us/about-us/planet-passionate\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NTU1ODksImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.UyoMYMeI-X0YbRuRTItVNZg1TEhZM-wBTtKaqzvxWog\" class=\"inline-image\" data-id = \"19003455589\" data-store-type = \"1\" width=\"385\" alt=\"\" style=\"width:385px;min-width:385px;max-width:385px;font-size:0;\"></a></td></tr></tbody></table></td></tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',sans-serif;\">\n<span style=\"color:#919191;font-size:8px;\"><span style=\"font-family:Arial,sans-serif;\">In</span><a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-environmental/sustainability-and-compliance/compliance/privacy-policy\" target=\"_blank\" style=\"text-decoration:none;color:#919191;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\"> accordance with GDPR, Kingspan is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at <br></strong></a> <a href=\"https://www.kingspan.com/us/en-us/website-privacy-notice\" target=\"_blank\" style=\"text-decoration:none;color:#919191;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">www.kingspan.com/us/en-us/website-privacy-notice</strong></a> <a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-environmental/sustainability-and-compliance/compliance/privacy-policy\" target=\"_blank\" style=\"text-decoration:none;color:#919191;font-family:'Century Gothic';\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">for further information.</strong></a></span><br><span style=\"font-size:8px;\"><br>​This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e‑mail. Please notify the sender immediately by e‑mail<br> <span style=\"font-family:Arial,sans-serif;\">​</span>if you have received this e‑mail by mistake and delete this e‑mail from your system. E‑mail transmission cannot be guaranteed to be secureor error‑free as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, <br> <span style=\"font-family:Arial,sans-serif;\">​</span>or contain viruses. The sender therefore does not accept liability for any errors or omissions in the contents of this message, which arise as a result of e‑mail transmission. If verification is required please request a hard‑copy version.</span><br> <br> <br> </td></tr>\n</tbody>\n</table></td></tr>\n</tbody>\n</table></td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"white-space:nowrap;color:#FFFFFF;font-size:12px;font-family:'Century Gothic';font-weight:400;font-style:normal;text-align:left;line-height:1.33px;\"><tbody><tr style=\"font-size:1.33px;\"><td style=\"font-family:'Century Gothic';\">SignV1</td></tr></tbody></table></td></tr>\n</tbody>\n</table></div>\n\n\n", "description_text": "[Graphical user interface, text  Description automatically generated]<https://lnkd.in/egJRNtHf>\r\n\r\nSee attached ,\r\n\r\n\r\nAnn Seo\r\n\r\n\r\n\r\nEastern Corporation\r\n\r\n124 Franklin Park Drive\r\n\r\nYoungsville, NC 27596\r\n\r\nMobile ************\r\n\r\nOffice ************\r\n\r\nFax ************\r\n\r\nhttp://usaeastern.com\r\n\r\n\r\n\r\nJohn Buchanan\r\nProject Estimator\r\nKingspan Insulated Panels\r\n720 Marion Road | Columbus | OH | 43207\r\nD: ************ |\r\nT: ************\r\nE: <EMAIL>\r\nwww.kingspanpanels.com\r\nIn accordance with GDPR, Kingspan is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at\r\n www.kingspan.com/us/en-us/website-privacy-notice for further information.\r\n\r\n​This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e‑mail. Please notify the sender immediately by e‑mail\r\n ​if you have received this e‑mail by mistake and delete this e‑mail from your system. E‑mail transmission cannot be guaranteed to be secureor error‑free as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete,\r\n ​or contain viruses. The sender therefore does not accept liability for any errors or omissions in the contents of this message, which arise as a result of e‑mail transmission. If verification is required please request a hard‑copy version.\r\n\r\n\r\n\r\nSignV1\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked:\r\n\r\n<EMAIL>\r\nhttps://lnkd.in/egJRNtHf\r\n\r\n\r\nAll user who have clicked the embedded link have changed their passwords", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333437&Signature=LfnsBZxubxlkhlyU70BZyDdP-f5LO52kic~-eyWPaKMoi9j~hldPXhEemeGmqz~G9RanQaduTkmlxJlIsC6zffxRe1DpdhdW8anWor4UE8PjnneJqkEvRnNLVjuKJZuwef-QltAIgPuwwnA-rf2RjKPY5jsKsmRUXNuPaILv4N0oCYLd8kqQsOVgWET0DSMii8cTlkKr37GQrd5YUcLQ61KkQvA4sZSqVoHu0kMeQgBuuxVi0Tr2JrzJzxl6gUhUJFRhdVgBeb2Hws0Q15J181jBIr9Pk-iaCwFLgWRKdJgmN-GEDOZqW4qKlcIM18k0I07eAOKowWpAymuOiHC-kQ__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-30T16:34:44Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 235417, "updated_at": "2021-11-30T16:34:44Z"}]}, "conversations": [{"id": ***********, "user_id": ***********, "to_emails": [], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON><PERSON>, sans-serif;'>\n<div>All passwords changed</div>\n</div>", "body_text": "All passwords changed", "ticket_id": 1401, "created_at": "2021-11-30T21:26:35Z", "updated_at": "2021-11-30T21:26:35Z", "incoming": false, "private": true, "support_email": null, "source": 2, "from_email": null, "cc_emails": [], "bcc_emails": null, "attachments": []}, {"id": 19003807120, "user_id": ***********, "to_emails": [], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", <PERSON>l, sans-serif;'>\n<div>blocked:</div>\n<div><br></div>\n<div><EMAIL>\n</div>\n<div><a href=\"https://lnkd.in/egJRNtHf\" rel=\"noreferrer\">https://lnkd.in/egJRNtHf</a></div>\n<div>\n</div>\n</div>", "body_text": "blocked:     <EMAIL>\n  https://lnkd.in/egJRNtHf", "ticket_id": 1401, "created_at": "2021-11-30T18:50:58Z", "updated_at": "2021-11-30T18:50:58Z", "incoming": false, "private": true, "support_email": null, "source": 2, "from_email": null, "cc_emails": [], "bcc_emails": null, "attachments": []}, {"id": 19003807107, "user_id": ***********, "to_emails": [], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", Aria<PERSON>, sans-serif;'>\n<div></div>\n<div>Link was clicked by 4 users - contacting them now</div>\n<div><br></div>\n<div><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NjIzMDUsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.RxFnI-bWCaght9YFgmYcPkjQ3wlvaTFqX1K8rOHBQ40\" class=\"inline-image\" data-id=\"19003462305\" data-store-type=\"1\" style=\"cursor: default;\"></div>\n<br><div></div>\n</div>", "body_text": "<PERSON> was clicked by 4 users - contacting them now", "ticket_id": 1401, "created_at": "2021-11-30T18:50:20Z", "updated_at": "2021-11-30T18:50:20Z", "incoming": false, "private": true, "support_email": null, "source": 2, "from_email": null, "cc_emails": [], "bcc_emails": null, "attachments": []}], "conversation_count": 3, "fetch_status": "success"}, {"ticket_id": 1427, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON> Alert] Eastern Corporation Final RFP EC90833", "group_id": 19000171671, "department_id": 19000141831, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": 19000461237, "responder_id": ***********, "due_by": "2021-12-08T20:43:45Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-12-01T16:43:45Z", "id": 1427, "priority": 1, "status": 5, "source": 1, "created_at": "2021-11-30T20:43:28Z", "updated_at": "2021-12-01T13:16:45Z", "workspace_id": 2, "requested_for_id": 19000461237, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div>\n<div><a href=\"https://lnkd.in/egJRNtHf\" rel=\"noreferrer\"><span style=\"color:windowtext;text-decoration:none\"><img width=\"285\" height=\"64\" style=\"width:2.9687in;height:.6666in\" alt=\"Graphical user interface, text\n\nDescription automatically generated\" src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NjQ2MDMsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.Ru0TSQNcQdDM0jL_ZkMl6TNzNsme5414VRX1ZCQD3jg\" class=\"inline-image\" data-id = \"19003464603\" data-store-type = \"1\"></span></a></div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div>See attached ,</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div style=\"margin:0in;background:white\"><span style=\"font-size:12.0pt;color:black\">Ann Seo</span></div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\"></span>\n</div>\n<div> </div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black;border:none windowtext 1.0pt;padding:0in\">Eastern Corporation</span><span style=\"font-size:12.0pt;color:black\"></span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">124 Franklin Park Drive</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Youngsville, NC 27596</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Mobile ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Office ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\">Fax ************</span>\n</div>\n<div></div>\n<div></div>\n<div style=\"margin:0in;background:white;font-variant-ligatures: normal;font-variant-caps: normal;orphans: 2;text-align:start;widows: 2;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;word-spacing:0px\">\n<span style=\"font-size:12.0pt;color:black\"><a href=\"http://usaeastern.com\" rel=\"noreferrer\">http://usaeastern.com</a></span>\n</div>\n<div></div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n<div></div>\n<div> </div>\n<div></div>\n</div>\n<div dir=\"ltr\" style=\"mso-line-height-rule:exactly;-webkit-text-size-adjust:100%;direction:ltr;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:10px 0 0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:700;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Russell Hensley<span style=\"font-family:remialcxesans;font-size:1px;color:#FFFFFF;line-height:1px;\">​</span>\n</td></tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Project Manager/Customer Excellence Supervisor</td></tr>\n</tbody>\n</table></td></tr></tbody></table></td></tr>\n<tr style=\"font-size:0;line-height:normal;\"><td align=\"left\" style=\"padding:20px 0 10px;vertical-align:middle;\"><a href=\"https://www.kingspan.com/us/en-us/about-kingspan/kingspan-insulated-panels\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NjQ2MDQsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.M2kCD2FPw9nkBlyogUJa7P1NrwB9DACPdGaPJUzugGY\" class=\"inline-image\" data-id = \"19003464604\" data-store-type = \"1\" width=\"75\" title=\"Kingspan Insulated Panels Website\" alt=\"Kingspan Insulated Panels Website\" style=\"width:75px;min-width:75px;max-width:75px;font-size:12px;\"></a></td></tr>\n<tr style=\"font-size:12px;color:#2D2E31;font-style:normal;font-weight:700;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Kingspan Insulated Panels</td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;color:#2D2E31;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">720 Marion Road</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">Columbus</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">OH</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">43207</td>\n</tr></tbody></table></td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><tbody><tr style=\"font-size:12px;\">\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">D: 614‑502‑7592</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | <br>\n</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">T: <a href=\"tel:************\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">************</strong></a>\n</td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic';\"> | </td>\n<td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">M: <a href=\"tel:************\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">************</strong></a>\n</td>\n</tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;color:#000001;font-style:normal;font-weight:400;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">E: <a href=\"mailto:<EMAIL>\" target=\"_blank\" style=\"text-decoration:none;color:#000001;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\"><EMAIL></strong></a>\n</td></tr>\n<tr style=\"font-size:12px;color:#030303;font-style:normal;font-weight:700;white-space:nowrap;\"><td align=\"left\" style=\"padding:0;vertical-align:top;font-family:'Century Gothic',Arial,Segoe,sans-serif;\">\n<a href=\"http://www.kingspanpanels.com/\" target=\"_blank\" style=\"text-decoration:none;color:#030303;\" rel=\"noreferrer\"><strong style=\"font-weight:700;\">www.kingspanpanels.com</strong></a><br>\n</td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:10px 0 0;vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;color:#909090;font-style:normal;font-weight:400;white-space:nowrap;\">\n<tbody>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:100%;font-size:0;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"font-size:0;line-height:normal;\"><tbody><tr style=\"font-size:0;\"><td align=\"left\" style=\"padding:0 0 10px;vertical-align:top;\"><a href=\"https://www.kingspan.com/us/en-us/about-us/planet-passionate\" target=\"_blank\" style=\"text-decoration:none;\" rel=\"noreferrer\"><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0NjQ2MDUsImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.U-Je1T4iT_-VdOKXdBu_ru86sVi-rmUkHSjPJ3t5btQ\" class=\"inline-image\" data-id = \"19003464605\" data-store-type = \"1\" width=\"385\" alt=\"\" style=\"width:385px;min-width:385px;max-width:385px;font-size:0;\"></a></td></tr></tbody></table></td></tr></tbody></table></td></tr>\n<tr style=\"font-size:12px;\"><td align=\"left\" style=\"vertical-align:top;font-family:'Century Gothic',sans-serif;\">\n<span style=\"color:#919191;font-size:8px;\"><span style=\"font-family:Arial,sans-serif;\">In</span><a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-environmental/sustainability-and-compliance/compliance/privacy-policy\" target=\"_blank\" style=\"text-decoration:none;color:#919191;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\"> accordance with GDPR, Kingspan is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at <br></strong></a> <a href=\"https://www.kingspan.com/us/en-us/website-privacy-notice\" target=\"_blank\" style=\"text-decoration:none;color:#919191;\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">www.kingspan.com/us/en-us/website-privacy-notice</strong></a> <a href=\"https://www.kingspan.com/gb/en-gb/about-kingspan/kingspan-environmental/sustainability-and-compliance/compliance/privacy-policy\" target=\"_blank\" style=\"text-decoration:none;color:#919191;font-family:'Century Gothic';\" rel=\"noreferrer\"><strong style=\"font-weight:400;\">for further information.</strong></a></span><br><span style=\"font-size:8px;\"><br>​This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e‑mail. Please notify the sender immediately by e‑mail<br> <span style=\"font-family:Arial,sans-serif;\">​</span>if you have received this e‑mail by mistake and delete this e‑mail from your system. E‑mail transmission cannot be guaranteed to be secureor error‑free as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete, <br> <span style=\"font-family:Arial,sans-serif;\">​</span>or contain viruses. The sender therefore does not accept liability for any errors or omissions in the contents of this message, which arise as a result of e‑mail transmission. If verification is required please request a hard‑copy version.</span><br> <br> <br> </td></tr>\n</tbody>\n</table></td></tr>\n</tbody>\n</table></td></tr>\n<tr style=\"font-size:0;\"><td align=\"left\" style=\"vertical-align:top;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"white-space:nowrap;color:#FFFFFF;font-size:12px;font-family:'Century Gothic';font-weight:400;font-style:normal;text-align:left;line-height:1.33px;\"><tbody><tr style=\"font-size:1.33px;\"><td style=\"font-family:'Century Gothic';\">SignV1</td></tr></tbody></table></td></tr>\n</tbody>\n</table></div>\n\n\n", "description_text": "[Graphical user interface, text  Description automatically generated]<https://lnkd.in/egJRNtHf>\r\n\r\nSee attached ,\r\n\r\n\r\nAnn <PERSON>o\r\n\r\n\r\n\r\nEastern Corporation\r\n\r\n124 Franklin Park Drive\r\n\r\nYoungsville, NC 27596\r\n\r\nMobile ************\r\n\r\nOffice ************\r\n\r\nFax ************\r\n\r\nhttp://usaeastern.com\r\n\r\n\r\n\r\n<PERSON>\r\nProject Manager/Customer Excellence Supervisor\r\nKingspan Insulated Panels\r\n720 Marion Road | Columbus | OH | 43207\r\nD: ************ |\r\nT: ************ | M: ************\r\nE: <PERSON>@kingspan.com\r\nwww.kingspanpanels.com\r\nIn accordance with GDPR, <PERSON><PERSON> is committed to complying with data privacy obligations in a clear and concise manner. We have updated our privacy policy which can be viewed at\r\n www.kingspan.com/us/en-us/website-privacy-notice for further information.\r\n\r\n​This message contains confidential information and is intended only for the individual named. If you are not the named addressee you should not disseminate, distribute or copy this e‑mail. Please notify the sender immediately by e‑mail\r\n ​if you have received this e‑mail by mistake and delete this e‑mail from your system. E‑mail transmission cannot be guaranteed to be secureor error‑free as information could be intercepted, corrupted, lost, destroyed, arrive late or incomplete,\r\n ​or contain viruses. The sender therefore does not accept liability for any errors or omissions in the contents of this message, which arise as a result of e‑mail transmission. If verification is required please request a hard‑copy version.\r\n\r\n\r\n\r\nSignV1\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "already resolved from ticket INC-1401", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333437&Signature=KGr7H377tBWM-LPeIuBhfIh0OiM-luxIvWNVVKd9WhqUaL31xWRDTl1jDOXRrFDKLmea5SY-vI3mcUtSm8Vv6QChg5Dqr2cw9k2Z~~f0XyfH-L8TGVrluIXr2VQYcBL-OzolQY1TkBNh3mSbvQtwCTx1eYc~Nd-QeaVR3tWU-~r~8ZkQR8Acmrr7PxGqtEUyQEG9OOba2~BH~3wkcb81Epq~L-LlZ3VoaawZyElDk0mepJ2Pxbbdjtp7b2UAbmFzvL~guqfykzbRTIerF2FNg4e2-twluqzZpvT0n6NomFfnHfya6tD1Zx8wFeROsLrynleL9wBSAKHvsvIASmi2mw__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-11-30T20:43:28Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 235454, "updated_at": "2021-11-30T20:43:28Z"}]}, "conversations": [{"id": ***********, "user_id": ***********, "to_emails": [], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;'>\n<div>already resolved from ticket INC-1401</div>\n</div>", "body_text": "already resolved from ticket INC-1401", "ticket_id": 1427, "created_at": "2021-12-01T13:16:09Z", "updated_at": "2021-12-01T13:16:09Z", "incoming": false, "private": true, "support_email": null, "source": 2, "from_email": null, "cc_emails": [], "bcc_emails": null, "attachments": []}], "conversation_count": 1, "fetch_status": "success"}, {"ticket_id": 1461, "ticket_details": {"planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "Curious email", "group_id": 19000171670, "department_id": 19000141863, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": ***********, "responder_id": ***********, "due_by": "2021-12-09T15:44:46Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": [], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-12-01T23:20:53Z", "id": 1461, "priority": 1, "status": 5, "source": 1, "created_at": "2021-12-01T15:20:23Z", "updated_at": "2021-12-01T19:52:55Z", "workspace_id": 2, "requested_for_id": ***********, "to_emails": ["Helpdesk <<EMAIL>>"], "type": "Incident", "description": "<div>Good day. \n</div>\n<div>\n<br>\n</div>\n<div>I received this in my email. It seems suspicious. I don’t want to open anything that could be dangerous. </div>\n<div>\n<br>\n</div>\n<div>Let me know if this is something that <PERSON><PERSON><PERSON> would send. </div>\n<div>\n<br>\n</div>\n<div><img src=\"https://attachment.freshservice.com/inline/attachment?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTkwMDM0ODg1ODksImRvbWFpbiI6Imthc2VydmljZWRlc2suZnJlc2hzZXJ2aWNlLmNvbSIsInR5cGUiOjF9.Du-ecoQDByNWZiM2NzabEBSRcnjfrKM7Z2BSBtMu4rE\" class=\"inline-image\" data-id = \"19003488589\" data-store-type = \"1\"></div>\n<div>\n<br>\n</div>\n<div>Thank you  <br>\n<br>\n<div dir=\"ltr\">Sent from my iPhone</div>\n</div>\n\n\n", "description_text": "Good day.\r\n\r\nI received this in my email. It seems suspicious. I don’t want to open anything that could be dangerous.\r\n\r\nLet me know if this is something that Vic<PERSON><PERSON> would send.\r\n\r\n[cid:99A5DF2B-53C4-4283-8765-5FF9C3B86D1F-L0-001]\r\n\r\nThank you\r\n\r\nSent from my iPhone\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Service Delivery", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "Instructed  user on how to submit phishing alerts ", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": []}, "conversations": [{"id": ***********, "user_id": ***********, "to_emails": ["KA IT Service Desk <<EMAIL>>"], "body": "<div>Thank you. I will use the link moving forward. \n</div>\n<div>\n<br>\n</div>\n<div>Have a great day <br>\n<br>\n<div dir=\"ltr\">Sent from my iPhone</div>\n<div dir=\"ltr\">\n<br>\n<blockquote></blockquote>\n</div>\n</div>", "body_text": "Thank you. I will use the link moving forward.\r\n\r\nHave a great day\r\n\r\nSent from my iPhone\r\n\r\n", "ticket_id": 1461, "created_at": "2021-12-01T19:13:45Z", "updated_at": "2021-12-01T19:13:45Z", "incoming": true, "private": false, "support_email": "<EMAIL>", "source": 0, "from_email": "<EMAIL>", "cc_emails": [], "bcc_emails": [], "attachments": []}, {"id": ***********, "user_id": ***********, "to_emails": ["<EMAIL>"], "body": "<div style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Helvetica Neue\", Aria<PERSON>, sans-serif;'>\n<div></div>\n<div>\n<div>Hi <span style=\"\"><PERSON>,</span>\n</div>\n<div><br></div>\n<div style=\"margin-bottom:15px;\"><span rel=\"tempredactor\" style='font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif; color: rgb(18, 52, 77);'>We appreciate you for alerting us about a phishing email. However, this is not the correct to submit phishing emails. Please refer to the following link for reporting phishing emails. <a href=\"https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx%C2%A0\" rel=\"noreferrer\" target=\"_blank\" style=\"\">https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx </a></span></div>\n<div>Link to your Ticket: <a href=\"https://kaservicedesk.kingspan.com/helpdesk/tickets/1461\" rel=\"noreferrer\">https://kaservicedesk.kingspan.com/helpdesk/tickets/1461</a>\n</div>\n<br><div>Thank you,</div>\n</div>\n</div>", "body_text": "Hi <PERSON>,     We appreciate you for alerting us about a phishing email. However, this is not the correct to submit phishing emails. Please refer to the following link for reporting phishing emails. https://kingspangroup.sharepoint.com/sites/KA-IT/SitePages/KA-IT-Communication---How-to-Report-Suspicious-Email-2020-12-17.aspx   Link to your Ticket: https://kaservicedesk.kingspan.com/helpdesk/tickets/1461 \n  Thank you,", "ticket_id": 1461, "created_at": "2021-12-01T18:49:12Z", "updated_at": "2021-12-01T18:49:12Z", "incoming": false, "private": false, "support_email": "<EMAIL>", "source": 0, "from_email": "\"<PERSON><PERSON> Gobel\" <<EMAIL>>", "cc_emails": ["<EMAIL>"], "bcc_emails": [], "attachments": []}], "conversation_count": 2, "fetch_status": "success"}, {"ticket_id": 1468, "ticket_details": {"acknowledged_by_id": null, "acknowledged_at": null, "planned_start_date": null, "planned_end_date": null, "planned_effort": null, "subject": "[<PERSON><PERSON>] Order Received", "group_id": 19000171671, "department_id": 19000141829, "category": "Security", "sub_category": "Spam/Phishing", "item_category": "<PERSON><PERSON>", "requester_id": ***********, "responder_id": ***********, "due_by": "2021-12-09T16:47:35Z", "fr_escalated": false, "deleted": false, "spam": false, "email_config_id": null, "fwd_emails": [], "reply_cc_emails": ["<EMAIL>"], "cc_emails": ["<EMAIL>"], "is_escalated": false, "fr_due_by": "2021-12-02T00:47:35Z", "id": 1468, "priority": 1, "status": 5, "source": 1, "created_at": "2021-12-01T16:47:31Z", "updated_at": "2021-12-01T18:57:20Z", "workspace_id": 2, "requested_for_id": ***********, "to_emails": ["KNA Support Desk <<EMAIL>>"], "type": "Incident", "description": "<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n <img style=\"\" alt=\"image\" width=\"142.5pt\" height=\"40.5pt\"> \n                                                                                                                                                                                                                                                Nov 29 2021 09:28:45</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n                                                                                                                                                                                                                                                                 \n                                  Transaction ID: <strong>TX9129787545</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\">Hello,</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\"></span> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; COLOR: #ed7d31; LINE-HEIGHT: 107%\">You sent a payment of $268.99 USD to eBay.com</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\">(</span><a href=\"mailto:<EMAIL>\" rel=\"noreferrer\"><span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\"><EMAIL></span></a><span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\">)</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\"></span> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\">It may take a few moments for this transaction to appear in your account.</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<strong>Merchant                                                                                                                                                                                                                                                 \n                                           Instruction to the Merchant</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\neBay LLC                                                                                                                                                                                                                                                        \n                             You haven’t entered any instructions</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<a href=\"mailto:<EMAIL>\" rel=\"noreferrer\"><EMAIL></a>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; BORDER-TOP: windowtext 1pt solid; FONT-FAMILY: 'Calibri',sans-serif; BORDER-RIGHT: medium none; BORDER-BOTTOM: windowtext 1pt solid; PADDING-BOTTOM: 1pt; PADDING-TOP: 1pt; PADDING-LEFT: 0cm; BORDER-LEFT: medium none; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%; PADDING-RIGHT: 0cm\">\n<div style=\"FONT-SIZE: 15px; BORDER-TOP: medium none; FONT-FAMILY: 'Calibri',sans-serif; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0cm; PADDING-TOP: 0cm; PADDING-LEFT: 0cm; BORDER-LEFT: medium none; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%; PADDING-RIGHT: 0cm\">\nDescription                                                                                                                         Unit Price                                                            QTY                                                   \n                                             Price</div>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<strong>Fossil Julianna Women Stainless Steel Mesh                                                              $268.99 USD                                                        1                                                                             \n                    $268.99 USD</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<strong>Digital Dial Smartwatch FS55</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong>                                                                                                                                                                                                                                Subtotal:               \n                                                                 $268.99 USD</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong>                                                                                                                                                                                                                                Total:                   \n                                                                   $268.99 USD</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong>                                                                                                                                                                                                                                Payment:                 \n                                                              $268.99 USD</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong></strong> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong>                                                                                                                </strong>Charge will appear on your Credit Card statement as PAYPAL *eBay.com*</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n                                                                                                                                                                                Payment Sent to\n<a href=\"mailto:<EMAIL>\" rel=\"noreferrer\"><EMAIL></a>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; BORDER-TOP: medium none; FONT-FAMILY: 'Calibri',sans-serif; BORDER-RIGHT: medium none; BORDER-BOTTOM: windowtext 1pt solid; PADDING-BOTTOM: 1pt; PADDING-TOP: 0cm; PADDING-LEFT: 0cm; BORDER-LEFT: medium none; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%; PADDING-RIGHT: 0cm\">\n<div style=\"FONT-SIZE: 15px; BORDER-TOP: medium none; FONT-FAMILY: 'Calibri',sans-serif; BORDER-RIGHT: medium none; BORDER-BOTTOM: medium none; PADDING-BOTTOM: 0cm; PADDING-TOP: 0cm; PADDING-LEFT: 0cm; BORDER-LEFT: medium none; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%; PADDING-RIGHT: 0cm\">\n<strong>INVOICE ID: 719284G</strong>\n</div>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong></strong> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n<strong>Issue with the Transaction?</strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\nYou have 24 hours from the date of transaction to open a dispute in the resolution centre.</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\nTo reach PayPal customer Service <strong><span style=\"FONT-SIZE: 17px; LINE-HEIGHT: 107%\">Call us: 1-800-324-4958</span></strong>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm 0cm 8pt; LINE-HEIGHT: 107%\">\n </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\">For more information on automatic payments, go to the PayPal website and click HELP in the upper right corner. The type “billing agreements” in the search box.</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\"></span> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\">Please do not reply to this email. This mailbox is not monitored and you PayPal account and click\n<strong>Help</strong> in the top corner of any PayPal page.</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\"></span> </div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\">You can receive plain text emails instead of HTML emails. To change your notifications preferences, log in to your PayPal account at <a href=\"http://www.PayPal.com\" rel=\"noreferrer\">www.PayPal.com</a>.</span>\n</div>\n<div style=\"FONT-SIZE: 15px; FONT-FAMILY: 'Calibri',sans-serif; MARGIN: 0cm; LINE-HEIGHT: 107%\">\n<span style=\"FONT-SIZE: 12px; LINE-HEIGHT: 107%\"></span> </div>\n\n\n", "description_text": "\r\n\r\n \r\n\r\n\r\n \r\n \r\n Nov 29 2021 09:28:45\r\n \r\n \r\n Transaction ID: TX9129787545\r\n \r\n \r\n \r\nHello,\r\n \r\n \r\n \r\nYou sent a payment of $268.99 USD to eBay.com\r\n \r\n(<EMAIL>)\r\n \r\n \r\n \r\nIt may take a few moments for this transaction to appear in your account.\r\n \r\n \r\n \r\nMerchant \r\n Instruction to the Merchant\r\n \r\neBay LLC \r\n You haven’t entered any instructions\r\n \r\n<EMAIL>\r\n \r\n \r\n \r\n \r\nDescription Unit Price QTY \r\n Price\r\n\r\n \r\nFossil Julianna Women Stainless Steel Mesh $268.99 USD 1 \r\n $268.99 USD\r\n \r\nDigital Dial Smartwatch FS55\r\n \r\n Subtotal: \r\n $268.99 USD\r\n \r\n Total: \r\n $268.99 USD\r\n \r\n Payment: \r\n $268.99 USD\r\n \r\n \r\n \r\n Charge will appear on your Credit Card statement as PAYPAL *eBay.com*\r\n \r\n Payment Sent to\r\n<EMAIL>\r\n \r\n \r\n \r\n \r\nINVOICE ID: 719284G\r\n\r\n \r\n \r\n \r\nIssue with the Transaction?\r\n \r\nYou have 24 hours from the date of transaction to open a dispute in the resolution centre.\r\n \r\n \r\n \r\nTo reach PayPal customer Service Call us: 1-800-324-4958\r\n \r\n \r\n \r\nFor more information on automatic payments, go to the PayPal website and click HELP in the upper right corner. The type “billing agreements” in the search box.\r\n \r\n \r\n \r\nPlease do not reply to this email. This mailbox is not monitored and you PayPal account and click\r\nHelp in the top corner of any PayPal page.\r\n \r\n \r\n \r\nYou can receive plain text emails instead of HTML emails. To change your notifications preferences, log in to your PayPal account at www.PayPal.com.\r\n \r\n \r\n\r\n\r\n", "custom_fields": {"boarding": null, "division": "KA IT", "ignore_ticket_reason": null, "impact_of_the_issue": null, "is_call_type_amp_priority_correct": null, "major_incident_type": null, "onboard_start_date": null, "onboarding_critical": null, "onboarding_status": null, "original_due_date": null, "otif": null, "parent_or_child": null, "priority_flag": null, "qualys_detection_score": null, "request_type": null, "sd_complete": null, "security_incident_result": null, "security_threat_assessment": null, "sub_status": null, "subdivision": "KA IT Operations", "trigger_update": null, "unassigned": true, "urgency_of_the_issue": null, "business_impact": null, "impacted_locations": null, "no_of_customers_impacted": null, "new_onboard_first_name": null, "new_onboard_last_name": null, "new_onboard_job_title": null, "new_onboard_manager": null, "new_onboard_employee_type": null, "new_onboard_primary_office_location": null, "issues": null, "additional_status_details": null, "closure_comments": "blocked: \r\n\r\n<EMAIL>\r\nmail-qk1-f195.google.com\r\n\r\nno malicious links", "problem_association": null, "closing_agent": null, "associated_ticket_id": null, "hidden_subject_change_field_for_bulk_trigger_update": null, "escalated_by": null}, "tasks_dependency_type": 0, "sla_policy_id": ***********, "impact": 1, "urgency": 1, "bcc_emails": [], "applied_business_hours": ***********, "created_within_business_hours": true, "resolution_notes": null, "resolution_notes_html": null, "attachments": [{"attachment_url": "https://kaservicedesk.attachments.freshservice.com/data/helpdesk/attachments/production/***********/original/phish_alert_sp2_2.0.0.0.eml?response-content-type=application/octet-stream&Expires=1752333437&Signature=gjXiAOFWlX84GbCZm-4AMyfElQhiTQ3YW5UCuVUgFuph0PzEULvT7Q1D2Czz4Hm4s0EWThqAydDRdBeHVzoArJWEUF4h8UNZa-9IgEOyDs~23Hxa698XHUjHqiyb9CaR3-9xUCE4cPyy8WH5qYp8sbDTAbxc5cq~g6XLUPVRk3YlHaqqlPU0UvtNy-2pOME0bKANatNO3ZTxahtszEEkVWL0VJXH0q7tUWP4Lk817nyOaBIkQWO~FDbWwofGymDfvf1PfLTvPGv42~jq~fKkwQ2zeITsNz-byEUMOWT0xG3JOi1UIl9ISJRwimJwe2N1tApGS4gsa1~ds8t-jZFxkg__&Key-Pair-Id=APKAIPHBXWY2KT5RCMPQ", "content_type": "application/octet-stream", "created_at": "2021-12-01T16:47:31Z", "id": ***********, "name": "phish_alert_sp2_2.0.0.0.eml", "size": 47549, "updated_at": "2021-12-01T16:47:31Z"}]}, "conversations": [{"id": ***********, "user_id": ***********, "to_emails": null, "body": "<div>Requester has submitted the survey response</div>", "body_text": "Not given.", "ticket_id": 1468, "created_at": "2021-12-01T18:57:20Z", "updated_at": "2021-12-01T18:57:20Z", "incoming": true, "private": false, "support_email": null, "source": 5, "from_email": null, "cc_emails": [], "bcc_emails": null, "attachments": []}], "conversation_count": 1, "fetch_status": "success"}]}