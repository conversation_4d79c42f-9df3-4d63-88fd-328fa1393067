import os
import datetime
import pandas as pd
from pathlib import Path

def run_reconciliation():
    try:
        # Get the Recons directory path
        recons_dir = Path(__file__).parent
        
        # Read in the two XLSX files
        mdm_data = pd.read_excel(recons_dir / 'MDMtoLS.xlsx')
        ls_data = pd.read_excel(recons_dir / 'LStoMDM.xlsx')

        # Create a new XLSX file with the reconciliation
        today = datetime.date.today().strftime('%Y.%m.%d')
        output_file = recons_dir / f'KC_ITM_21.1_{today}.xlsx'

        # Initialize lists for mismatches
        not_in_ls = []
        not_in_mdm = []

        # Check MDM records not in LS
        for _, row in mdm_data.iterrows():
            if row['Serial number'] not in ls_data['Serialnumber'].values:
                not_in_ls.append(row.to_dict())

        # Check LS records not in MDM
        for _, row in ls_data.iterrows():
            if row['Serialnumber'] not in mdm_data['Serial number'].values:
                not_in_mdm.append(row.to_dict())

        # Create DataFrames for mismatches
        not_in_ls_df = pd.DataFrame(not_in_ls)
        not_in_mdm_df = pd.DataFrame(not_in_mdm)

        # Write to Excel
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            mdm_data.to_excel(writer, sheet_name='MDMtoLS', index=False)
            ls_data.to_excel(writer, sheet_name='LStoMDM', index=False)
            if not not_in_ls_df.empty:
                not_in_ls_df.to_excel(writer, sheet_name='Not in LS', index=False)
            if not not_in_mdm_df.empty:
                not_in_mdm_df.to_excel(writer, sheet_name='Not in MDM', index=False)

        print(f"Reconciliation completed. Output file: {output_file}")
        print(f"Found {len(not_in_ls)} records not in LS")
        print(f"Found {len(not_in_mdm)} records not in MDM")
        
    except FileNotFoundError as e:
        print(f"Error: Could not find input file - {e}")
    except Exception as e:
        print(f"Error occurred during reconciliation: {e}")

if __name__ == "__main__":
    run_reconciliation()

