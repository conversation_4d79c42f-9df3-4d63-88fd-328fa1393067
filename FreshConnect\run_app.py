"""
Simple entry point to run the FreshConnect web application.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import the app after loading environment variables
from freshconnect.web.app import app

if __name__ == '__main__':
    # Run the Flask application directly
    app.run(
        host=os.getenv('WEB_HOST', '0.0.0.0'),
        port=int(os.getenv('WEB_PORT', '5000')),
        debug=os.getenv('WEB_DEBUG', 'False').lower() == 'true'
    )
