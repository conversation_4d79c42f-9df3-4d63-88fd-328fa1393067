# FreshService Users Fetcher

A standalone Python script that fetches the first 10 users (requesters) from your FreshService instance and displays all their attributes.

## Features

- Fetches user data from FreshService API
- Displays comprehensive user information including:
  - Basic details (name, email, phone numbers)
  - Job information (title, department, location)
  - Account status and activity
  - Custom fields
  - All other available attributes
- Saves user data to a JSON file for further processing
- Handles API rate limiting and retries
- Supports environment variables and .env files for configuration

## Prerequisites

- Python 3.6 or higher
- FreshService API access
- Valid FreshService API key

## Installation

1. Install required dependencies:
```bash
pip install -r requirements_users.txt
```

2. Set up your FreshService credentials:

### Option 1: Environment Variables
```bash
export FRESHSERVICE_DOMAIN="your-domain.freshservice.com"
export FRESHSERVICE_API_KEY="your_api_key_here"
```

### Option 2: .env File
1. Copy the template file:
```bash
cp .env.template .env
```

2. Edit the `.env` file and add your credentials:
```
FRESHSERVICE_DOMAIN=your-domain.freshservice.com
FRESHSERVICE_API_KEY=your_api_key_here
```

## Usage

Run the script:
```bash
python fetch_freshservice_users.py
```

## Output

The script will:
1. Display detailed information about each user in the console
2. Save all user data to a JSON file named `freshservice_users_YYYYMMDD_HHMMSS.json`

### Sample Output
```
FreshService Users Fetcher
==================================================

Initialized FreshService client for domain: your-domain.freshservice.com
Making GET request to: https://your-domain.freshservice.com/api/v2/requesters
Parameters: {'page': 1, 'per_page': 10}

Successfully fetched 10 users

================================================================================
FRESHSERVICE USERS - FIRST 10 USERS
================================================================================

------------------------------------------------------------
USER #1 - ID: 12345
------------------------------------------------------------
Name: John Doe
Email: <EMAIL>
Primary Email: <EMAIL>
Mobile: ******-0123
Work Phone: ******-0124
Job Title: Software Engineer
Department: Engineering
Location: New York Office
Language: en
Time Zone: Eastern Time (US & Canada)
Active: True
VIP User: False
Created At: 2023-01-15T10:30:00Z
Updated At: 2023-12-01T14:22:00Z

All Attributes:
  address: 123 Main St, New York, NY
  background_information: Senior developer with 5 years experience
  ...
```

## API Endpoints Used

- `GET /api/v2/requesters` - Fetches users (requesters) from FreshService

## Error Handling

The script includes comprehensive error handling for:
- Missing or invalid API credentials
- Network connectivity issues
- API rate limiting (with automatic retries)
- Invalid responses from the API

## Customization

You can modify the script to:
- Fetch more or fewer users by changing the `per_page` parameter
- Add additional filtering parameters
- Modify the output format
- Add data processing or analysis

## Getting Your FreshService API Key

1. Log in to your FreshService instance as an admin
2. Go to Admin > API Settings
3. Generate or copy your existing API key
4. Use this key in your environment variables or .env file

## Troubleshooting

### Common Issues

1. **"FRESHSERVICE_DOMAIN must be provided"**
   - Ensure you've set the FRESHSERVICE_DOMAIN environment variable or .env file

2. **"FRESHSERVICE_API_KEY must be provided"**
   - Ensure you've set the FRESHSERVICE_API_KEY environment variable or .env file

3. **"401 Unauthorized"**
   - Check that your API key is correct and has the necessary permissions

4. **"Rate limit exceeded"**
   - The script automatically handles rate limiting, but if you see this error, wait a few minutes and try again

5. **"No users found"**
   - Check that your FreshService instance has users/requesters
   - Verify your API key has permission to read user data

## Files Created

- `freshservice_users_YYYYMMDD_HHMMSS.json` - JSON file containing all user data

## Security Notes

- Never commit your .env file or API keys to version control
- Keep your API key secure and rotate it regularly
- The script only reads user data and doesn't modify anything in FreshService
