"""
Example script to fetch and analyze service catalog items.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to the path so we can import freshconnect
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file
load_dotenv()

# Import freshconnect modules
from freshconnect.processors.services import fetch_service_items, analyze_service_catalog, get_service_categories
from freshconnect.config import settings

# Set up logging
settings.setup_logging()
logger = logging.getLogger(__name__)

def main():
    """Main function to demonstrate service catalog processing."""
    try:
        # Fetch service catalog items
        logger.info("Fetching service catalog items...")
        count = fetch_service_items()
        
        if count > 0:
            logger.info(f"Successfully fetched {count} service catalog items")
        else:
            logger.warning("No service catalog items were fetched")
        
        # Get service categories
        logger.info("\nGetting service categories...")
        categories = get_service_categories()
        
        if categories:
            logger.info(f"Found {len(categories)} service categories:")
            for category in categories:
                logger.info(f"  - {category}")
        else:
            logger.warning("No service categories found")
        
        # Analyze service catalog
        logger.info("\nAnalyzing service catalog...")
        analysis = analyze_service_catalog()
        
        if analysis:
            logger.info("Service catalog analysis complete")
            logger.info(f"Total items: {analysis['total_items']}")
            logger.info(f"Items with attachments: {analysis['with_attachments']}")
            logger.info(f"Items without attachments: {analysis['without_attachments']}")
            
            logger.info("\nItems by category:")
            for category, count in sorted(analysis['categories'].items(), key=lambda x: x[1], reverse=True):
                logger.info(f"  {category}: {count} items")
            
            logger.info("\nItems by visibility:")
            for visibility, count in analysis['visibility'].items():
                logger.info(f"  {visibility}: {count} items")
        else:
            logger.error("Failed to analyze service catalog")
            return 1
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
