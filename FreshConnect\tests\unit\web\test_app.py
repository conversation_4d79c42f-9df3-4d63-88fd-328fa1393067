"""
Unit tests for the web application.
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from flask import Flask

# Patch the FreshServiceClient before importing app
with patch('freshconnect.api.freshservice.FreshServiceClient'):
    from freshconnect.web.app import app, init_db, WebhookEvent, load_agent_details


@pytest.fixture
def client():
    """Create a test client for the Flask app."""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client


@pytest.mark.unit
@pytest.mark.web
class TestWebApp:
    """Tests for the web application."""

    def test_index(self, client, monkeypatch):
        """Test the index route."""
        # Mock the render_template function to return a simple string
        monkeypatch.setattr('freshconnect.web.app.render_template', lambda *args, **kwargs: "Test Index")

        # Make a request to the index route
        response = client.get('/')

        # Verify the response
        assert response.status_code == 200
        assert response.data.decode('utf-8') == "Test Index"

    @patch('freshconnect.web.app.fetch_ticket_history')
    def test_freshservice_webhook_ticket_updated(self, mock_fetch_history, client, monkeypatch):
        """Test the FreshService webhook route for ticket updates."""
        # Mock the database session
        mock_session = MagicMock()
        monkeypatch.setattr('freshconnect.web.app.db_session', mock_session)

        # Create test data
        webhook_data = {
            "ticket": {
                "id": 1,
                "subject": "Test Ticket"
            }
        }

        # Make a request to the webhook route
        response = client.post(
            '/webhook/freshservice',
            data=json.dumps(webhook_data),
            content_type='application/json',
            headers={'X-Freshservice-Event': 'ticket_updated'}
        )

        # Verify the response
        assert response.status_code == 200
        response_data = json.loads(response.data.decode('utf-8'))
        assert response_data['status'] == 'success'
        assert 'updated_ticket_history' in response_data['actions']

        # Verify the ticket history was fetched
        mock_fetch_history.assert_called_once_with(1)

        # Verify the event was stored in the database
        assert mock_session.add.call_count == 1
        assert mock_session.commit.call_count == 1

    @patch('freshconnect.web.app.fetch_service_items')
    def test_freshservice_webhook_service_item_updated(self, mock_fetch_services, client, monkeypatch):
        """Test the FreshService webhook route for service item updates."""
        # Mock the database session
        mock_session = MagicMock()
        monkeypatch.setattr('freshconnect.web.app.db_session', mock_session)

        # Create test data
        webhook_data = {
            "service_item": {
                "id": 1,
                "name": "Test Service Item"
            }
        }

        # Make a request to the webhook route
        response = client.post(
            '/webhook/freshservice',
            data=json.dumps(webhook_data),
            content_type='application/json',
            headers={'X-Freshservice-Event': 'service_item_updated'}
        )

        # Verify the response
        assert response.status_code == 200
        response_data = json.loads(response.data.decode('utf-8'))
        assert response_data['status'] == 'success'
        assert 'updated_service_catalog' in response_data['actions']

        # Verify the service items were fetched
        mock_fetch_services.assert_called_once()

        # Verify the event was stored in the database
        assert mock_session.add.call_count == 1
        assert mock_session.commit.call_count == 1

    @patch('freshconnect.web.app.analyze_agent_activity')
    def test_freshservice_webhook_agent_assignment_updated(self, mock_analyze_agents, client, monkeypatch):
        """Test the FreshService webhook route for agent assignment updates."""
        # Mock the database session
        mock_session = MagicMock()
        monkeypatch.setattr('freshconnect.web.app.db_session', mock_session)

        # Create test data
        webhook_data = {
            "agent": {
                "id": 1,
                "name": "Test Agent"
            }
        }

        # Make a request to the webhook route
        response = client.post(
            '/webhook/freshservice',
            data=json.dumps(webhook_data),
            content_type='application/json',
            headers={'X-Freshservice-Event': 'agent_assignment_updated'}
        )

        # Verify the response
        assert response.status_code == 200
        response_data = json.loads(response.data.decode('utf-8'))
        assert response_data['status'] == 'success'
        assert 'updated_agent_stats' in response_data['actions']

        # Verify the agent stats were analyzed
        mock_analyze_agents.assert_called_once_with(show_plot=False)

        # Verify the event was stored in the database
        assert mock_session.add.call_count == 1
        assert mock_session.commit.call_count == 1

    def test_list_events(self, client, monkeypatch):
        """Test the list events route."""
        # Mock the database session
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query

        # Create mock events
        mock_event = MagicMock()
        mock_event.id = 1
        mock_event.timestamp.isoformat.return_value = "2023-01-01T00:00:00Z"
        mock_event.event_type = "ticket_updated"
        mock_event.source = "freshservice"
        mock_event.action_taken = "updated_ticket_history"
        mock_event.status = "processed"

        mock_query.all.return_value = [mock_event]

        monkeypatch.setattr('freshconnect.web.app.db_session', mock_session)

        # Make a request to the list events route
        response = client.get('/webhook/events')

        # Verify the response
        assert response.status_code == 200
        response_data = json.loads(response.data.decode('utf-8'))
        assert len(response_data) == 1
        assert response_data[0]['id'] == 1
        assert response_data[0]['event_type'] == "ticket_updated"

    def test_init_db(self, monkeypatch):
        """Test the init_db function."""
        # Mock the SQLAlchemy create_engine and sessionmaker
        mock_engine = MagicMock()
        mock_sessionmaker = MagicMock()
        mock_session = MagicMock()

        mock_create_engine = MagicMock(return_value=mock_engine)
        mock_sessionmaker_func = MagicMock(return_value=mock_sessionmaker)
        mock_sessionmaker_func.return_value.return_value = mock_session

        monkeypatch.setattr('freshconnect.web.app.create_engine', mock_create_engine)
        monkeypatch.setattr('freshconnect.web.app.sessionmaker', mock_sessionmaker_func)

        # Mock the Base.metadata.create_all method
        mock_create_all = MagicMock()
        monkeypatch.setattr('freshconnect.web.app.Base.metadata.create_all', mock_create_all)

        # Call the function
        session = init_db()

        # Verify the engine and session were created
        mock_create_engine.assert_called_once()
        mock_create_all.assert_called_once_with(mock_engine)
        assert session is mock_session

    @patch('freshconnect.web.app.fetch_agent_details')
    def test_load_agent_details(self, mock_fetch_agent_details):
        """Test the load_agent_details function."""
        # Setup mock
        mock_fetch_agent_details.return_value = [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>",
                "active": True,
                "groups": [
                    {"id": 101, "name": "Support"},
                    {"id": 102, "name": "IT"}
                ]
            },
            {
                "id": 2,
                "name": "Jane Smith",
                "email": "<EMAIL>",
                "active": True,
                "groups": [
                    {"id": 101, "name": "Support"}
                ]
            }
        ]

        # Call the function
        result = load_agent_details()

        # Verify the function was called
        mock_fetch_agent_details.assert_called_once()

        # Verify the result
        assert result == 2  # Two agents were loaded
