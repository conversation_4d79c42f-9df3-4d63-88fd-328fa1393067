#!/usr/bin/env python3
"""
JSON File Splitter

This script splits a large JSON file containing ticket conversations into multiple smaller files
under 80MB while maintaining JSON integrity. Each split file contains complete ticket records.

Usage:
    python split_json_file.py <input_json_file>

Example:
    python split_json_file.py ticket_conversations_20250711_115454.json
"""

import sys
import os
import json
from datetime import datetime


def get_file_size_mb(file_path):
    """Get file size in MB."""
    return os.path.getsize(file_path) / (1024 * 1024)


def split_json_file(input_file, max_size_mb=80):
    """
    Split a large JSON file into smaller files under the specified size limit.
    
    Args:
        input_file (str): Path to the input JSON file
        max_size_mb (int): Maximum size per file in MB (default: 80)
    
    Returns:
        list: List of created file paths
    """
    print(f"Loading JSON file: {input_file}")
    
    # Load the original JSON file
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Original file size: {get_file_size_mb(input_file):.2f} MB")
    print(f"Total tickets: {len(data['tickets'])}")
    
    # Extract metadata and tickets
    metadata = data['metadata'].copy()
    tickets = data['tickets']
    
    # Calculate approximate tickets per file
    original_size_mb = get_file_size_mb(input_file)
    total_tickets = len(tickets)
    approx_tickets_per_file = int((total_tickets * max_size_mb) / original_size_mb)
    
    print(f"Estimated tickets per file: {approx_tickets_per_file}")
    
    # Split tickets into chunks
    created_files = []
    file_counter = 1
    start_idx = 0
    
    while start_idx < total_tickets:
        # Determine end index for this chunk
        end_idx = min(start_idx + approx_tickets_per_file, total_tickets)
        
        # Create chunk data
        chunk_tickets = tickets[start_idx:end_idx]
        chunk_data = {
            'metadata': metadata.copy(),
            'tickets': chunk_tickets
        }
        
        # Update metadata for this chunk
        chunk_data['metadata']['chunk_info'] = {
            'chunk_number': file_counter,
            'tickets_in_chunk': len(chunk_tickets),
            'ticket_range': f"{start_idx + 1}-{end_idx}",
            'split_timestamp': datetime.now().isoformat()
        }
        
        # Generate output filename
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_part{file_counter:02d}.json"
        
        # Write chunk to file
        print(f"Creating {output_file} with {len(chunk_tickets)} tickets...")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(chunk_data, f, indent=2, ensure_ascii=False, default=str)
        
        # Check file size
        file_size_mb = get_file_size_mb(output_file)
        print(f"  File size: {file_size_mb:.2f} MB")
        
        # If file is too large, reduce chunk size and retry
        if file_size_mb > max_size_mb and len(chunk_tickets) > 1:
            print(f"  File too large ({file_size_mb:.2f} MB > {max_size_mb} MB), reducing chunk size...")
            os.remove(output_file)  # Remove the oversized file
            
            # Reduce chunk size by 20%
            new_chunk_size = int(len(chunk_tickets) * 0.8)
            end_idx = start_idx + new_chunk_size
            
            # Retry with smaller chunk
            chunk_tickets = tickets[start_idx:end_idx]
            chunk_data = {
                'metadata': metadata.copy(),
                'tickets': chunk_tickets
            }
            
            chunk_data['metadata']['chunk_info'] = {
                'chunk_number': file_counter,
                'tickets_in_chunk': len(chunk_tickets),
                'ticket_range': f"{start_idx + 1}-{end_idx}",
                'split_timestamp': datetime.now().isoformat()
            }
            
            print(f"  Retrying with {len(chunk_tickets)} tickets...")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(chunk_data, f, indent=2, ensure_ascii=False, default=str)
            
            file_size_mb = get_file_size_mb(output_file)
            print(f"  New file size: {file_size_mb:.2f} MB")
        
        created_files.append(output_file)
        start_idx = end_idx
        file_counter += 1
    
    return created_files


def create_index_file(created_files, original_file):
    """Create an index file listing all the split files."""
    index_data = {
        'original_file': original_file,
        'original_size_mb': get_file_size_mb(original_file),
        'split_timestamp': datetime.now().isoformat(),
        'total_parts': len(created_files),
        'parts': []
    }
    
    total_tickets = 0
    for i, file_path in enumerate(created_files, 1):
        # Load file to get ticket count
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        ticket_count = len(data['tickets'])
        total_tickets += ticket_count
        
        part_info = {
            'part_number': i,
            'filename': file_path,
            'size_mb': get_file_size_mb(file_path),
            'ticket_count': ticket_count,
            'ticket_range': data['metadata']['chunk_info']['ticket_range']
        }
        index_data['parts'].append(part_info)
    
    index_data['total_tickets'] = total_tickets
    
    # Create index filename
    base_name = os.path.splitext(original_file)[0]
    index_file = f"{base_name}_index.json"
    
    with open(index_file, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nIndex file created: {index_file}")
    return index_file


def main():
    """Main function to split JSON file."""
    if len(sys.argv) < 2:
        print("Usage: python split_json_file.py <input_json_file>")
        print("Example: python split_json_file.py ticket_conversations_20250711_115454.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    
    if not os.path.exists(input_file):
        print(f"Error: File '{input_file}' not found.")
        sys.exit(1)
    
    print("JSON File Splitter")
    print("=" * 50)
    
    try:
        # Split the file
        created_files = split_json_file(input_file)
        
        # Create index file
        index_file = create_index_file(created_files, input_file)
        
        # Print summary
        print(f"\n{'='*50}")
        print("Split completed successfully!")
        print(f"{'='*50}")
        print(f"Original file: {input_file}")
        print(f"Original size: {get_file_size_mb(input_file):.2f} MB")
        print(f"Created {len(created_files)} split files:")
        
        total_size = 0
        for file_path in created_files:
            size_mb = get_file_size_mb(file_path)
            total_size += size_mb
            print(f"  {file_path}: {size_mb:.2f} MB")
        
        print(f"\nTotal size of split files: {total_size:.2f} MB")
        print(f"Index file: {index_file}")
        
    except Exception as e:
        print(f"Error splitting file: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
