import os
import pdfplumber
import pandas as pd
import re
from datetime import datetime

def extract_pdf_text(pdf_path, first_page_only=False):
    """Extract text from a PDF file

    Args:
        pdf_path: Path to the PDF file
        first_page_only: If True, only extract text from the first page
    """
    text_content = ""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            # Only process the first page if specified
            pages_to_process = [pdf.pages[0]] if first_page_only and len(pdf.pages) > 0 else pdf.pages
            for page in pages_to_process:
                text_content += page.extract_text() + "\n"
        return text_content
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {str(e)}")
        return ""

def extract_bill_info(text_content):
    """Extract account number, total due, invoice date, and invoice number from Verizon bill text"""
    # Initialize variables with default values
    account_number = "Not found"
    total_due = "Not found"
    invoice_date = "Not found"
    invoice_number = "Not found"

    # Extract account number - pattern may vary based on actual bill format
    account_match = re.search(r'Account Number:?\s*(\d[\d-]*\d)', text_content)
    if account_match:
        account_number = account_match.group(1)

    # Extract invoice number - looking for invoice number patterns
    invoice_num_match = re.search(r'(Invoice|Bill) Number:?\s*(\d+)', text_content, re.IGNORECASE)
    if invoice_num_match:
        invoice_number = invoice_num_match.group(2)
    else:
        # Try alternative patterns for invoice number
        alt_invoice_match = re.search(r'Invoice #:?\s*(\d+)', text_content, re.IGNORECASE)
        if alt_invoice_match:
            invoice_number = alt_invoice_match.group(1)

    # Extract total due - looking for "Total Current Charges" as requested
    total_due_match = re.search(r'Total Current Charges\s*\$?([\d,]+\.\d{2})', text_content, re.IGNORECASE)
    if total_due_match:
        total_due = total_due_match.group(1).replace(',', '')
    else:
        # Fallback to looking for other total patterns
        total_due_match = re.search(r'Total Due:?\s*\$?([\d,]+\.\d{2})', text_content, re.IGNORECASE)
        if total_due_match:
            total_due = total_due_match.group(1).replace(',', '')

    # Extract invoice date - looking for date formats
    date_match = re.search(r'(Invoice|Bill|Statement) Date:?\s*(\w+\s+\d{1,2},?\s+\d{4})', text_content, re.IGNORECASE)
    if date_match:
        invoice_date = date_match.group(2)
        # Try to standardize the date format
        try:
            date_obj = datetime.strptime(invoice_date, "%B %d, %Y")
            invoice_date = date_obj.strftime("%Y-%m-%d")
        except:
            # If parsing fails, keep the original format
            pass

    return {
        'Account Number': account_number,
        'Invoice Number': invoice_number,
        'Total Due': total_due,
        'Invoice Date': invoice_date
    }

def process_verizon_bills():
    """Process all Verizon bills in the Inputs folder"""
    # Define input and output directories
    input_dir = os.path.join(os.path.dirname(__file__), 'Inputs')
    output_dir = os.path.join(os.path.dirname(__file__), 'Outputs')

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Get current timestamp for the output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"verizon_bill_summary_{timestamp}.xlsx")

    # List to store bill information
    all_bills = []

    # Process each PDF file in the Inputs directory
    for filename in os.listdir(input_dir):
        if filename.endswith('.pdf'):
            pdf_path = os.path.join(input_dir, filename)
            print(f"Processing {filename}...")

            # Extract text from first page of PDF only
            text_content = extract_pdf_text(pdf_path, first_page_only=True)

            # Extract bill information
            if text_content:
                bill_info = extract_bill_info(text_content)
                bill_info['Filename'] = filename
                all_bills.append(bill_info)
                print(f"  Extracted: Account: {bill_info['Account Number']}, Invoice #: {bill_info['Invoice Number']}, Total Due: ${bill_info['Total Due']}, Date: {bill_info['Invoice Date']}")
            else:
                print(f"  Failed to extract text from {filename}")

    # Create DataFrame and save to Excel
    if all_bills:
        df = pd.DataFrame(all_bills)

        # Reorder columns
        columns = ['Filename', 'Account Number', 'Invoice Number', 'Total Due', 'Invoice Date']
        df = df[columns]

        # Save to Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Verizon Bills', index=False)

            # Auto-adjust column widths
            worksheet = writer.sheets['Verizon Bills']
            for i, column in enumerate(df.columns):
                column_width = max(df[column].astype(str).map(len).max(), len(column)) + 2
                worksheet.column_dimensions[chr(65 + i)].width = column_width

        print(f"\nSummary saved to {output_file}")
        return output_file
    else:
        print("No bills were processed successfully.")
        return None

if __name__ == "__main__":
    print("Starting Verizon bill processing...")
    output_file = process_verizon_bills()
    if output_file:
        print(f"Processing complete. Results saved to {output_file}")
    else:
        print("Processing failed. No output file was created.")
