"""
Unit tests for the agents processor module.
"""

import pytest
import os
from datetime import datetime
from unittest.mock import patch, MagicMock

from freshconnect.processors.agents import AgentProcessor, analyze_agent_activity, generate_agent_report, fetch_agent_details


@pytest.mark.unit
@pytest.mark.processors
class TestAgentProcessor:
    """Tests for the AgentProcessor class."""

    def test_init(self, mock_db):
        """Test initialization of the processor."""
        processor = AgentProcessor(db=mock_db)
        assert processor.db is mock_db

    def test_analyze_activity(self, mock_db, monkeypatch):
        """Test analyzing agent activity."""
        # Mock the plotly figure to avoid browser opening
        mock_fig = MagicMock()
        mock_fig.show = MagicMock()
        mock_fig.write_html = MagicMock()

        # Patch the _create_visualization method to return the mock figure
        monkeypatch.setattr(AgentProcessor, "_create_visualization", lambda *args, **kwargs: mock_fig)

        # Create processor with mock
        processor = AgentProcessor(db=mock_db)

        # Call the method
        results = processor.analyze_activity(output_file="test_output.html", show_plot=True)

        # Verify the result
        assert results is not None
        assert "actor_counts" in results
        assert "group_setter_counts" in results
        assert "action_count" in results
        assert "unique_actors" in results

        # Verify the figure was shown and saved
        mock_fig.show.assert_called_once()
        mock_fig.write_html.assert_called_once_with("test_output.html")

    def test_analyze_activity_no_data(self, monkeypatch):
        """Test analyzing agent activity with no data."""
        # Create a mock database with no data
        mock_db = MagicMock()
        mock_db.find_items.return_value = []

        # Create processor with mock
        processor = AgentProcessor(db=mock_db)

        # Call the method
        results = processor.analyze_activity(show_plot=False)

        # Verify the result
        assert results is None

    def test_create_visualization(self, mock_db):
        """Test creating a visualization."""
        # Create processor with mock
        processor = AgentProcessor(db=mock_db)

        # Create test data
        sorted_actors = {"Agent 1": 10, "Agent 2": 5}
        sorted_group_setters = {"Agent 1": 3, "Agent 2": 2}
        action_times = [
            (datetime(2023, 1, 1, 10, 0, 0), "Agent 1"),
            (datetime(2023, 1, 1, 11, 0, 0), "Agent 2"),
            (datetime(2023, 1, 1, 12, 0, 0), "Agent 1")
        ]

        # Call the method
        fig = processor._create_visualization(sorted_actors, sorted_group_setters, action_times)

        # Verify the result
        assert fig is not None
        assert len(fig.data) == 3  # Three traces (overall activity, group setting, action timing)

    @patch('freshconnect.api.freshservice.FreshServiceClient')
    def test_fetch_agents_and_groups(self, mock_client_class):
        """Test fetching agents and their groups."""
        # Create a mock database
        mock_db = MagicMock()

        # Setup mock client
        mock_client = MagicMock()
        mock_client_class.return_value = mock_client

        # Mock the get_all_pages method to return test data
        mock_client.get_all_pages.side_effect = [
            # First call - agents
            [
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "active": True
                },
                {
                    "id": 2,
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "email": "<EMAIL>",
                    "active": True
                }
            ],
            # Second call - groups
            [
                {"id": 101, "name": "Support"},
                {"id": 102, "name": "IT"}
            ],
            # Third call - members of group 101
            [
                {"agent_id": 1},
                {"agent_id": 2}
            ],
            # Fourth call - members of group 102
            [
                {"agent_id": 1}
            ]
        ]

        # Create processor with mock
        processor = AgentProcessor(db=mock_db)

        # Call the method
        result = processor.fetch_agents_and_groups()

        # Verify the client was called correctly
        mock_client_class.assert_called_once()
        assert mock_client.get_all_pages.call_count == 4
        mock_client.get_all_pages.assert_any_call('agents')
        mock_client.get_all_pages.assert_any_call('groups')
        mock_client.get_all_pages.assert_any_call('groups/101/members')
        mock_client.get_all_pages.assert_any_call('groups/102/members')

        # Verify the result structure
        assert result is not None
        assert len(result) == 2

        # Check first agent
        assert result[0]["id"] == 1
        assert result[0]["name"] == "John Doe"
        assert result[0]["email"] == "<EMAIL>"
        assert result[0]["active"] == True
        assert len(result[0]["groups"]) == 2
        assert {"id": 101, "name": "Support"} in result[0]["groups"]
        assert {"id": 102, "name": "IT"} in result[0]["groups"]

        # Check second agent
        assert result[1]["id"] == 2
        assert result[1]["name"] == "Jane Smith"
        assert result[1]["email"] == "<EMAIL>"
        assert result[1]["active"] == True
        assert len(result[1]["groups"]) == 1
        assert {"id": 101, "name": "Support"} in result[1]["groups"]

        # Verify data was stored in the database
        mock_db.clear_and_insert_items.assert_called_once_with('agent_details', result)


@pytest.mark.unit
@pytest.mark.processors
class TestAgentFunctions:
    """Tests for the agent processor functions."""

    @patch('freshconnect.processors.agents.AgentProcessor')
    def test_analyze_agent_activity(self, mock_processor_class):
        """Test the analyze_agent_activity function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.analyze_activity.return_value = {"action_count": 10}
        mock_processor_class.return_value = mock_processor

        # Call the function
        results = analyze_agent_activity(output_file="test_output.html", show_plot=True)

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.analyze_activity.assert_called_once_with("test_output.html", True)
        assert results == {"action_count": 10}

    @patch('freshconnect.processors.agents.AgentProcessor')
    def test_generate_agent_report(self, mock_processor_class):
        """Test the generate_agent_report function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.analyze_activity.return_value = {
            "actor_counts": {"Agent 1": 10},
            "group_setter_counts": {"Agent 1": 3},
            "action_count": 10,
            "unique_actors": 1
        }
        mock_processor_class.return_value = mock_processor

        # Call the function
        report = generate_agent_report(output_file="test_output.html")

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.analyze_activity.assert_called_once_with("test_output.html", show_plot=False)

        # Verify the report structure
        assert report is not None
        assert "timestamp" in report
        assert "metrics" in report
        assert "top_agents" in report
        assert "top_group_setters" in report
        assert report["metrics"]["action_count"] == 10

    @patch('freshconnect.processors.agents.AgentProcessor')
    def test_fetch_agent_details(self, mock_processor_class):
        """Test the fetch_agent_details function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.fetch_agents_and_groups.return_value = [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>",
                "active": True,
                "groups": [
                    {"id": 101, "name": "Support"},
                    {"id": 102, "name": "IT"}
                ]
            },
            {
                "id": 2,
                "name": "Jane Smith",
                "email": "<EMAIL>",
                "active": True,
                "groups": [
                    {"id": 101, "name": "Support"}
                ]
            }
        ]
        mock_processor_class.return_value = mock_processor

        # Call the function
        agents = fetch_agent_details()

        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.fetch_agents_and_groups.assert_called_once()

        # Verify the result structure
        assert agents is not None
        assert len(agents) == 2
        assert agents[0]["id"] == 1
        assert agents[0]["name"] == "John Doe"
        assert len(agents[0]["groups"]) == 2
        assert agents[1]["id"] == 2
        assert agents[1]["name"] == "Jane Smith"
        assert len(agents[1]["groups"]) == 1
