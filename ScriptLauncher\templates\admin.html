{% extends "base.html" %}

{% block title %}Script Launcher - Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-cog me-2"></i>Script Administration</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('add_script_route') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i> Add New Script
        </a>
    </div>
</div>

{% if scripts %}
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Category</th>
                    <th>Type</th>
                    <th>Path</th>
                    <th>Parameters</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for script in scripts %}
                    <tr>
                        <td>{{ script.id }}</td>
                        <td>{{ script.name }}</td>
                        <td><span class="badge bg-primary">{{ script.category }}</span></td>
                        <td><span class="badge bg-secondary">{{ script.type }}</span></td>
                        <td><code>{{ script.path }}</code></td>
                        <td>{{ script.parameters|length }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('script_form', script_id=script.id) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-play"></i>
                                </a>
                                <a href="{{ url_for('edit_script', script_id=script.id) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        data-bs-toggle="modal" data-bs-target="#deleteModal" 
                                        data-script-id="{{ script.id }}" data-script-name="{{ script.name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> No scripts found. Add a new script to get started.
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the script "<span id="deleteScriptName"></span>"?
                This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#deleteModal').on('show.bs.modal', function(event) {
            const button = $(event.relatedTarget);
            const scriptId = button.data('script-id');
            const scriptName = button.data('script-name');
            
            $('#deleteScriptName').text(scriptName);
            $('#deleteForm').attr('action', '/admin/delete/' + scriptId);
        });
    });
</script>
{% endblock %}
