import requests
from time import sleep

from requests.models import HTTPBasicAuth, Response

APIkey = "JISmsOnV8bDfoIO9W8M2"   #needs to be stored and encrypted somewhere else
pwd = "trash"
urlPre = "https://kaservicedesk.freshservice.com/api/v2"
dtSince = "2024-06-30T19:18:30Z"  ## need a persistant place to store when the last report was run 


##Function definitions

def GETUpdatedTickets(dtSince):
    ##Returns list of tickets that have had any update since dtSince in "YYY-MM-DDTHH:mm:ssZ" format
    
    url = urlPre + '/tickets?updated_since=' + str(dtSince)
    
    dictJson = {}
    pageNum = 1
    while(True):
        response = requests.get(url= url, auth=HTTPBasicAuth(APIkey, pwd))
        dictHolder = response.json()
        dictJson["page"+str(pageNum)] = dictHolder
        
        ## load the headers to check for a next page link
        checkHead = response.headers

        ## if the next link exists set url and page number. If exception the next link is not there and can return the results already gathered      
        try:
            url = checkHead['Link'][1:-13]
            pageNum += 1

        except:
            ## If there is not a link to the next page of results, return the results of the ticket query

            lstChangedTicketIds = []
            for page in dictJson:
                tickets = dictJson[page]['tickets']
                for ticket in tickets:
                    lstChangedTicketIds.append(ticket['id'])

            return lstChangedTicketIds

        ## flow control, if less than 60 iterations remaining pause for a second to get rate limit back up
        if int(checkHead['X-Ratelimit-Remaining']) < 60:
            print("Pausing for flow control - Updated Tickets")
            sleep(1)

def GETTicketHistory(ticketID):
    ## GETs the entire ticket history for a specific ticket by ID, returns a dictionary
    url = urlPre + '/tickets/'+str(ticketID) +'/activities'

    dictJson = {}
    pageNum = 1
    while(True):
        response = requests.get(url= url, auth=HTTPBasicAuth(APIkey, pwd))
        dictHolder = response.json()
        dictJson["page"+str(pageNum)] = dictHolder
        
        ## load the headers to check for a next page link
        checkHead = response.headers
        print(checkHead['X-Ratelimit-Remaining'])

        ## flow control, if less than 60 iterations remaining pause for a second to get rate limit back up
        if int(checkHead['X-Ratelimit-Remaining']) < 60:
            print("Pausing for flow control - Updated Tickets: transactions remaining")
            sleep(1)

        ## if the next link exists set url and page number. If exception the next link is not there and can return the results already gathered      
        try:
            url = checkHead['Link'][1:-13]
            pageNum += 1
            

        except:
            ## If there is not a link to the next page of results, return the results of the ticket query
            return dictJson




## Start program

lstChangedTicketIDs = [GETUpdatedTickets(dtSince)]  ## GET a list of updated ticket numbers

