{"tickets": {"1": {"ticket_number": 107892, "subject": "Request for <PERSON> : Web Camera", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:41:11Z"}, "2": {"ticket_number": 107891, "subject": "[<PERSON><PERSON>lla] Access request to dropbox.com", "status": 5, "priority": 1, "updated_at": "2024-10-31T18:38:34Z"}, "3": {"ticket_number": 107890, "subject": "Request for <PERSON> : Tv Display adapter - IT Parts & Accessories", "status": 4, "priority": 2, "updated_at": "2024-10-31T18:31:24Z"}, "4": {"ticket_number": 107889, "subject": "Request for Czerny So : Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:38:54Z"}, "5": {"ticket_number": 107888, "subject": "Calculations for Partial PN in BoM", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:31:53Z"}, "6": {"ticket_number": 107887, "subject": "reset password", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:10:23Z"}, "7": {"ticket_number": 107886, "subject": "Request for <PERSON> : Salesforce CRM", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:10:55Z"}, "8": {"ticket_number": 107885, "subject": "<PERSON><PERSON><PERSON> ", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:12:12Z"}, "9": {"ticket_number": 107884, "subject": "[<PERSON><PERSON>] ", "status": 2, "priority": 3, "updated_at": "2024-10-31T18:01:30Z"}, "10": {"ticket_number": 107883, "subject": "Request for <PERSON> : Email Feature Requests", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:55:56Z"}, "11": {"ticket_number": 107882, "subject": "AD account locked out", "status": 4, "priority": 2, "updated_at": "2024-10-31T17:55:24Z"}, "12": {"ticket_number": 107881, "subject": "TAF-JES-IDF2-UPS: APC UPS Health (SNMP Custom Advanced): No response (check: firewalls, routing, SNMP settings, IP addresses, SNMP version, community, passwords, etc) (SNMP error # -2003)", "status": 2, "priority": 4, "updated_at": "2024-10-31T18:28:20Z"}, "13": {"ticket_number": 107880, "subject": "New ticket logged Call Reference - T20241031.1282 Fwd: Ticket [#INC-107879] Updated with Reply: MFA is blocked", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:40:38Z"}, "14": {"ticket_number": 107879, "subject": "MFA is blocked", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:36:10Z"}, "15": {"ticket_number": 107878, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-31T18:35:48Z"}, "16": {"ticket_number": 107877, "subject": "Help", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:31:58Z"}, "17": {"ticket_number": 107876, "subject": "Cant read PDF files", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:30:52Z"}, "18": {"ticket_number": 107875, "subject": "[Failed] VAC Daily Backup - PLC AUTOMATION SPC VANTAGE POINT (0 VMs)", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:29:42Z"}, "19": {"ticket_number": 107874, "subject": "Caledon restock Wireless Headset", "status": 4, "priority": 2, "updated_at": "2024-10-31T18:29:08Z"}, "20": {"ticket_number": 107873, "subject": "[Failed] MOD Daily Backup - DOMAIN CONTROLLER (0 VMs)", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:28:09Z"}, "21": {"ticket_number": 107872, "subject": "Replace iPhone KNA-M-G0NZVG29N72J, giving multiple problems .", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:19:15Z"}, "22": {"ticket_number": 107871, "subject": "<PERSON><PERSON>'s computer down", "status": 4, "priority": 1, "updated_at": "2024-10-31T18:38:06Z"}, "23": {"ticket_number": 107870, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-31T18:18:45Z"}, "24": {"ticket_number": 107869, "subject": "Docking Station Not Connecting to my Laptop", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:29:50Z"}, "25": {"ticket_number": 107868, "subject": "toner from old printer", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:53:50Z"}, "26": {"ticket_number": 107867, "subject": "Outlook not opening attachments", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:47:59Z"}, "27": {"ticket_number": 107866, "subject": "MS Project access", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:08:38Z"}, "28": {"ticket_number": 107865, "subject": "Add Paint Code - 438A1600 ", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:33:07Z"}, "29": {"ticket_number": 107864, "subject": "[<PERSON><PERSON>] <PERSON> shared \"DS Pipe & Steel FInal #2439029.pdf\" with you", "status": 2, "priority": 3, "updated_at": "2024-10-31T18:38:21Z"}, "30": {"ticket_number": 107863, "subject": "[<PERSON><PERSON>] Urgent Assistant Needed", "status": 4, "priority": 3, "updated_at": "2024-10-31T18:38:57Z"}, "31": {"ticket_number": 107862, "subject": "JES-IDF-7-2960: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-31T17:05:10Z"}, "32": {"ticket_number": 107861, "subject": "TAF-JES-BackOffice-IDF-UPS: <PERSON> (<PERSON>): Destination host unreachable (ICMP error # 11003)", "status": 2, "priority": 4, "updated_at": "2024-10-31T17:04:27Z"}, "33": {"ticket_number": 107860, "subject": "TAF-JES-IDF2-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-31T17:04:00Z"}, "34": {"ticket_number": 107859, "subject": "BU-04 - Monthly Backup Formal Reconciliation", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:00:48Z"}, "35": {"ticket_number": 107858, "subject": "Service Pro Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:07:03Z"}, "36": {"ticket_number": 107857, "subject": "FW: Your organization requires a Zoom upgrade", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:53:55Z"}, "37": {"ticket_number": 107856, "subject": "Request for Czerny So : Wireless Headset", "status": 4, "priority": 2, "updated_at": "2024-10-31T16:48:03Z"}, "38": {"ticket_number": 107855, "subject": "TAF-STP-IDF4-UPS: APC UPS Health (SNMP Custom Advanced): 4 m 51 s  (Run Time Remaining) is below the error limit of 5 m  in Run Time Remaining. Battery Low, Shutdown eminent", "status": 2, "priority": 4, "updated_at": "2024-10-31T16:45:23Z"}, "39": {"ticket_number": 107854, "subject": "printer", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:22:10Z"}, "40": {"ticket_number": 107853, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:18:12Z"}, "41": {"ticket_number": 107852, "subject": "5046 Rona EDI Error/Warning  Vicwest invoice # 91729282 UCP code rejection ", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:07:17Z"}, "42": {"ticket_number": 107851, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-31T17:54:34Z"}, "43": {"ticket_number": 107850, "subject": "IT e mail. What I should to do?", "status": 4, "priority": 1, "updated_at": "2024-10-31T16:23:09Z"}, "44": {"ticket_number": 107849, "subject": "Acces to Folder", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:11:29Z"}, "45": {"ticket_number": 107848, "subject": "Backup/BRMS Daily Report", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:03:12Z"}, "46": {"ticket_number": 107847, "subject": "<PERSON> - Forward to <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:44:03Z"}, "47": {"ticket_number": 107846, "subject": "Fw: Your organization requires a Zoom upgrade", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:58:17Z"}, "48": {"ticket_number": 107845, "subject": "Off-Board <PERSON> / <PERSON> - Requested By <PERSON>: Request for <PERSON> by System:", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:01:33Z"}, "49": {"ticket_number": 107844, "subject": "Off-Board <PERSON> / <PERSON> - Requested By <PERSON>: AD Network Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:16:08Z"}, "50": {"ticket_number": 107843, "subject": "Off-Board <PERSON> / <PERSON> - Requested By <PERSON>: Finance and Fleet Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:01:24Z"}, "51": {"ticket_number": 107842, "subject": "Off-Board <PERSON> / <PERSON> - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:01:15Z"}, "52": {"ticket_number": 107841, "subject": "Off-Board <PERSON> / <PERSON> - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:00:30Z"}, "53": {"ticket_number": 107839, "subject": "SAP CAD030 Vicwest - Ledgerlink Upload", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:44:17Z"}, "54": {"ticket_number": 107838, "subject": "Request for <PERSON> : Laptop - Standard", "status": 3, "priority": 2, "updated_at": "2024-10-31T15:33:06Z"}, "55": {"ticket_number": 107837, "subject": "Vendor 11607 Appleone Blocked for posting 1035 ", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:21:34Z"}, "56": {"ticket_number": 107836, "subject": "Please Reset Vicwest SAP Password", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:27:50Z"}, "57": {"ticket_number": 107835, "subject": "Delivery address not showing ", "status": 4, "priority": 1, "updated_at": "2024-10-31T15:25:43Z"}, "58": {"ticket_number": 107834, "subject": "Request for <PERSON> : Deploy Fortinac | Deploy a Solution, Service or System", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:53:02Z"}, "59": {"ticket_number": 107833, "subject": "18543414, item, 00170 is blocked by requisitioner", "status": 4, "priority": 1, "updated_at": "2024-10-31T15:21:42Z"}, "60": {"ticket_number": 107832, "subject": "SAP Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:09:08Z"}, "61": {"ticket_number": 107831, "subject": "Request for <PERSON><PERSON><PERSON> : Mobile Phone - Change Plan/Features", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:27:32Z"}, "62": {"ticket_number": 107828, "subject": "FW: Lebanon VA property", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:27:33Z"}, "63": {"ticket_number": 107827, "subject": "Request for <PERSON> : TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-31T14:48:24Z"}, "64": {"ticket_number": 107826, "subject": "Need remote connection ", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:20:30Z"}, "65": {"ticket_number": 107825, "subject": "Request for <PERSON> : Non-Standard Application - No License Required", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:35:54Z"}, "66": {"ticket_number": 107823, "subject": "20613 Interstate Products, Inc. - extend to plant 1019", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:55:56Z"}, "67": {"ticket_number": 107822, "subject": "SAP SPU-SPZ", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:30:56Z"}, "68": {"ticket_number": 107821, "subject": "Request for <PERSON> : Monitor", "status": 4, "priority": 2, "updated_at": "2024-10-31T15:49:28Z"}, "69": {"ticket_number": 107820, "subject": "FW: Your organization requires a Zoom upgrade", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:37:43Z"}, "70": {"ticket_number": 107819, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Request for <PERSON> by System:", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:06Z"}, "71": {"ticket_number": 107818, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Salesforce CRM - Termination", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:34:18Z"}, "72": {"ticket_number": 107817, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:14:51Z"}, "73": {"ticket_number": 107816, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Finance and Fleet Termination", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:54:05Z"}, "74": {"ticket_number": 107815, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:05Z"}, "75": {"ticket_number": 107814, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:28:09Z"}, "76": {"ticket_number": 107813, "subject": "(Merged to  107821) Request for Ali Sheikh : Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T14:40:55Z"}, "77": {"ticket_number": 107812, "subject": "<PERSON><PERSON> laptop", "status": 4, "priority": 2, "updated_at": "2024-10-31T14:19:09Z"}, "78": {"ticket_number": 107811, "subject": "SAP CNP1 C: Drive filling up", "status": 5, "priority": 2, "updated_at": "2024-10-31T18:27:33Z"}, "79": {"ticket_number": 107810, "subject": "KNA-L-13607", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:14:20Z"}, "80": {"ticket_number": 107809, "subject": "Password reset ", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:22:52Z"}, "81": {"ticket_number": 107808, "subject": "SAP Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:06:04Z"}, "82": {"ticket_number": 107806, "subject": "iPhone Setup Error/help", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:04:29Z"}, "83": {"ticket_number": 107805, "subject": "Request for <PERSON> : Network Shared Files and Folders - Permissions and Access", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:59:34Z"}, "84": {"ticket_number": 107804, "subject": "Joes Monitor", "status": 4, "priority": 1, "updated_at": "2024-10-31T15:05:25Z"}, "85": {"ticket_number": 107803, "subject": "Emails Going To Spam from One of Our Major Store Chain", "status": 4, "priority": 2, "updated_at": "2024-10-31T17:18:26Z"}, "86": {"ticket_number": 107802, "subject": "MARD", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:05:06Z"}, "87": {"ticket_number": 107801, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-31T13:15:23Z"}, "88": {"ticket_number": 107800, "subject": "Laptop Cannot Connect To Internet", "status": 4, "priority": 2, "updated_at": "2024-10-31T13:05:34Z"}, "89": {"ticket_number": 107799, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-31T13:01:07Z"}, "90": {"ticket_number": 107798, "subject": "Scan to SharePoint stopped working", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:17:49Z"}, "91": {"ticket_number": 107797, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-31T12:32:06Z"}, "92": {"ticket_number": 107795, "subject": "Shopfloor", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:40:26Z"}, "93": {"ticket_number": 107794, "subject": "KNA-KIP-Caledon IT Meeting Room: Ping (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 1, "updated_at": "2024-10-31T13:40:13Z"}, "94": {"ticket_number": 107793, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-31T12:37:29Z"}, "95": {"ticket_number": 107792, "subject": "[Failed] DEL Daily Backup - KNADEL-FSNS1 NAS Users Files (4 VMs) 2 failed", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:15:21Z"}, "96": {"ticket_number": 107789, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:28:13Z"}, "97": {"ticket_number": 107790, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-31T04:07:30Z"}, "98": {"ticket_number": 107791, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: XA / AS400 - Access and Permissions", "status": 3, "priority": 2, "updated_at": "2024-10-31T15:36:08Z"}, "99": {"ticket_number": 107787, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:26:03Z"}, "100": {"ticket_number": 107788, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:25:45Z"}, "101": {"ticket_number": 107786, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: Desktop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:37:44Z"}, "102": {"ticket_number": 107785, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: XA / AS400 - Access and Permissions", "status": 3, "priority": 2, "updated_at": "2024-10-31T15:19:01Z"}, "103": {"ticket_number": 107783, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-31T04:26:39Z"}, "104": {"ticket_number": 107784, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion: Zoom - Office Phone", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:26:37Z"}, "105": {"ticket_number": 107782, "subject": "On-board <PERSON> (Sun, 2024, Nov 3) - Full-Time Employee KNA - Red Lion", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:27:12Z"}, "106": {"ticket_number": 107781, "subject": "Requesting Concur Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:01:57Z"}, "107": {"ticket_number": 107780, "subject": "kingspanamericas | CYDERES-1835907 | Attacker methodology: Input Capture MSEDGE.EXE Prevention | S2 (Medium)", "status": 4, "priority": 2, "updated_at": "2024-10-31T17:42:25Z"}, "108": {"ticket_number": 107779, "subject": "Request for <PERSON>l : Non-Standard Application - No License Required", "status": 4, "priority": 2, "updated_at": "2024-10-31T01:12:49Z"}, "109": {"ticket_number": 107778, "subject": "Issue with Short Sheets Allowance on Order", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:34:09Z"}, "110": {"ticket_number": 107777, "subject": "Word froze on me", "status": 5, "priority": 1, "updated_at": "2024-10-31T00:49:42Z"}, "111": {"ticket_number": 107775, "subject": "Lansweeper- 0f004v623363fb- rename and asset tag", "status": 4, "priority": 1, "updated_at": "2024-10-31T13:58:36Z"}, "112": {"ticket_number": 107774, "subject": "Lansweeper- 5CD3245S0S- needs rename and asset tag", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:02:20Z"}, "113": {"ticket_number": 107773, "subject": "Lansweeper- Printer 10.114.10.59 - asset tag and rename", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:03:44Z"}, "114": {"ticket_number": 107772, "subject": "Lansweeper- Printer 10.119.10.52 - asset tag and rename", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:04:15Z"}, "115": {"ticket_number": 107771, "subject": "SN022198621657  -unidentifiable device scanned", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:21:51Z"}, "116": {"ticket_number": 107770, "subject": "Fw: Ticket [INC-K7756] Your device is non-compliant ", "status": 5, "priority": 1, "updated_at": "2024-10-30T23:59:59Z"}, "117": {"ticket_number": 107769, "subject": "Lansweeper- kna-l-14166- asset tag and rename", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:14:02Z"}, "118": {"ticket_number": 107768, "subject": "Lansweeper- KNA-GT78SW3- needs asset tag and renamed", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:20:59Z"}, "119": {"ticket_number": 107767, "subject": "Unblock vendor 17808 cc1093 - SAP", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:13:02Z"}, "120": {"ticket_number": 107766, "subject": "Lansweeper- 5CG4376NN0 - needs asset tag and rename", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:34:12Z"}, "121": {"ticket_number": 107765, "subject": "kna-d-7660 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:31:42Z"}, "122": {"ticket_number": 107764, "subject": "kna-l-14182 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:30:25Z"}, "123": {"ticket_number": 107763, "subject": "kna-l-12215 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:29:06Z"}, "124": {"ticket_number": 107762, "subject": "kna-d-7682 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:27:52Z"}, "125": {"ticket_number": 107761, "subject": "kna-d-12798 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:26:31Z"}, "126": {"ticket_number": 107760, "subject": "kna-l-15188 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:25:03Z"}, "127": {"ticket_number": 107759, "subject": "kna-l-15188 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:28:19Z"}, "128": {"ticket_number": 107758, "subject": "kna-d-7490 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:21:16Z"}, "129": {"ticket_number": 107757, "subject": "Lansweeper- kna-l-8633- inactive asset enabled", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:16:39Z"}, "130": {"ticket_number": 107756, "subject": "SPAM Concern", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:17:03Z"}, "131": {"ticket_number": 107755, "subject": "Update Vendor address:  International Accreditation Service #19522", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:27:35Z"}, "132": {"ticket_number": 107754, "subject": "Clearance / Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:31:23Z"}, "133": {"ticket_number": 107753, "subject": "VENDOR SPELLED WRONG", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:47:43Z"}, "134": {"ticket_number": 107752, "subject": "Job# 1000101311 Hornings Market cooler Request to change shipping point", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:42:55Z"}, "135": {"ticket_number": 107751, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T21:39:20Z"}, "136": {"ticket_number": 107750, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T21:38:24Z"}, "137": {"ticket_number": 107749, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>: SAP S/4 HANA - Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:09:05Z"}, "138": {"ticket_number": 107748, "subject": "Off-Board Tan<PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-30T23:09:34Z"}, "139": {"ticket_number": 107747, "subject": "Off-Board Tan<PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:09:05Z"}, "140": {"ticket_number": 107746, "subject": "Off-Board Tan<PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:09:11Z"}, "141": {"ticket_number": 107745, "subject": "Adobe Sign/Pro", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:23:12Z"}, "142": {"ticket_number": 107744, "subject": "KA FInance DL addition", "status": 5, "priority": 1, "updated_at": "2024-10-30T21:26:34Z"}, "143": {"ticket_number": 107743, "subject": "material created/extension - accessories", "status": 3, "priority": 1, "updated_at": "2024-10-31T17:35:50Z"}, "144": {"ticket_number": 107742, "subject": "Robot Report CS475 - please map new PLC's", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:28:32Z"}, "145": {"ticket_number": 107741, "subject": "FW: AWIP Power BI Reports", "status": 4, "priority": 1, "updated_at": "2024-10-30T23:23:59Z"}, "146": {"ticket_number": 107740, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:22:09Z"}, "147": {"ticket_number": 107739, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:22:25Z"}, "148": {"ticket_number": 107738, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:22:42Z"}, "149": {"ticket_number": 107737, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:23:00Z"}, "150": {"ticket_number": 107734, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:19:49Z"}, "151": {"ticket_number": 107736, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:23:20Z"}, "152": {"ticket_number": 107735, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Laptop - Engineering/Drafting", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:23:42Z"}, "153": {"ticket_number": 107731, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:08:55Z"}, "154": {"ticket_number": 107733, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: XA / AS400 - Access and Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:24:01Z"}, "155": {"ticket_number": 107732, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Workstation Setup", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:24:18Z"}, "156": {"ticket_number": 107730, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:27:24Z"}, "157": {"ticket_number": 107729, "subject": "Morin - Bristol - Quarterly Site Visit", "status": 2, "priority": 2, "updated_at": "2024-10-30T21:10:04Z"}, "158": {"ticket_number": 107728, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:18:42Z"}, "159": {"ticket_number": 107727, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:19:08Z"}, "160": {"ticket_number": 107726, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:19:29Z"}, "161": {"ticket_number": 107725, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:19:47Z"}, "162": {"ticket_number": 107724, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:20:15Z"}, "163": {"ticket_number": 107722, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:17:28Z"}, "164": {"ticket_number": 107723, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Laptop - Engineering/Drafting", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:20:38Z"}, "165": {"ticket_number": 107719, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:22:06Z"}, "166": {"ticket_number": 107721, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: XA / AS400 - Access and Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:20:55Z"}, "167": {"ticket_number": 107720, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Workstation Setup", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:21:10Z"}, "168": {"ticket_number": 107718, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:18:21Z"}, "169": {"ticket_number": 107717, "subject": "FW: RELEASE - WSO NationWide Self-Storage & Carwash #1-92407 LINE 130 ", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:32:03Z"}, "170": {"ticket_number": 107716, "subject": "Download ", "status": 4, "priority": 2, "updated_at": "2024-10-30T20:57:53Z"}, "171": {"ticket_number": 107715, "subject": "Request for <PERSON><PERSON> : Bluebeam Complete - BlueBeam Annual Subscription Plans", "status": 4, "priority": 2, "updated_at": "2024-10-30T20:56:07Z"}, "172": {"ticket_number": 107714, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-31T04:42:59Z"}, "173": {"ticket_number": 107713, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:09:52Z"}, "174": {"ticket_number": 107712, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:09:56Z"}, "175": {"ticket_number": 107711, "subject": "FW: Your device is non-compliant", "status": 4, "priority": 1, "updated_at": "2024-10-30T20:51:30Z"}, "176": {"ticket_number": 107710, "subject": "GETTING ERROR MESSAGE WHEN SEARCHING IN SHAREPOINT/KNOWLEDGE CENTER", "status": 4, "priority": 1, "updated_at": "2024-10-30T21:49:23Z"}, "177": {"ticket_number": 107709, "subject": "ADD KS AZTECO OPTION TO KSSS SINGLE SKIN CONFIGURATION", "status": 5, "priority": 1, "updated_at": "2024-10-31T13:27:28Z"}, "178": {"ticket_number": 107708, "subject": "<NAME_EMAIL> to KNA Shift Reports", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:47:23Z"}, "179": {"ticket_number": 107706, "subject": "Request for <PERSON><PERSON><PERSON> : Microsoft Office 365 Platform License", "status": 5, "priority": 2, "updated_at": "2024-10-31T01:44:03Z"}, "180": {"ticket_number": 107705, "subject": "NEW VENDOR - Summit Fire & Security ", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:25:55Z"}, "181": {"ticket_number": 107704, "subject": "CANNOT REVERSE PO# **********", "status": 4, "priority": 1, "updated_at": "2024-10-31T16:31:23Z"}, "182": {"ticket_number": 107703, "subject": "FW: Your device is non-compliant", "status": 4, "priority": 1, "updated_at": "2024-10-31T13:47:32Z"}, "183": {"ticket_number": 107702, "subject": "<NAME_EMAIL> to KNA shift group", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:35:31Z"}, "184": {"ticket_number": 107701, "subject": "Disable <PERSON> Accounts Temporarily", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:15:15Z"}, "185": {"ticket_number": 107700, "subject": "Unable to receive", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:06:07Z"}, "186": {"ticket_number": 107699, "subject": "Whse 18 Shipping/Receiving Set up", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:04:05Z"}, "187": {"ticket_number": 107698, "subject": "Email signature update ", "status": 4, "priority": 1, "updated_at": "2024-10-30T22:04:57Z"}, "188": {"ticket_number": 107697, "subject": "CAMBIO DE CONTRASEÑA-URGENTE", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:16:39Z"}, "189": {"ticket_number": 107695, "subject": "<NAME_EMAIL> to KNA shift reports", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:58:20Z"}, "190": {"ticket_number": 107694, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:03Z"}, "191": {"ticket_number": 107693, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:48Z"}, "192": {"ticket_number": 107692, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:32Z"}, "193": {"ticket_number": 107691, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:38:18Z"}, "194": {"ticket_number": 107689, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-30T19:01:20Z"}, "195": {"ticket_number": 107690, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: Laptop - Engineering/Drafting", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:39:11Z"}, "196": {"ticket_number": 107687, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:41:40Z"}, "197": {"ticket_number": 107686, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:39:25Z"}, "198": {"ticket_number": 107688, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee TAF - Jessup: XA / AS400 - Access and Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:03:49Z"}, "199": {"ticket_number": 107685, "subject": "Purchase Order Items 2024-10-30.ods", "status": 2, "priority": 1, "updated_at": "2024-10-30T19:51:59Z"}, "200": {"ticket_number": 107684, "subject": "PRINTER NOT WORKING", "status": 5, "priority": 1, "updated_at": "2024-10-30T21:57:46Z"}, "201": {"ticket_number": 107683, "subject": "Change Currency", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:57:50Z"}, "202": {"ticket_number": 107682, "subject": "KNA-AWP-Vacaville Meetroom1 and Meetroom2 showing Organizer name (should be Meeting name)", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:59:11Z"}, "203": {"ticket_number": 107680, "subject": "Unable to access Stratford production calendar", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:02:35Z"}, "204": {"ticket_number": 107679, "subject": "Power BI Access", "status": 4, "priority": 1, "updated_at": "2024-10-31T15:14:00Z"}, "205": {"ticket_number": 107678, "subject": "FW: Forklift Training System Vendor", "status": 3, "priority": 1, "updated_at": "2024-10-31T14:56:16Z"}, "206": {"ticket_number": 107677, "subject": "GL Wand Issues", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:07:19Z"}, "207": {"ticket_number": 107676, "subject": "Unable to export reports from QuickBooks", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:47:29Z"}, "208": {"ticket_number": 107675, "subject": "Add <EMAIL> to KNA shift reports group", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:22:31Z"}, "209": {"ticket_number": 107674, "subject": "FW: Your device is non-compliant", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:56:45Z"}, "210": {"ticket_number": 107672, "subject": "SAP - Customer statement credit balance", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:13:22Z"}, "211": {"ticket_number": 107671, "subject": "Website Access Request", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:28:40Z"}, "212": {"ticket_number": 107670, "subject": "Request for <PERSON> : FortiNAC - Licenses and Professional service - Professional Services", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "213": {"ticket_number": 107669, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "214": {"ticket_number": 107668, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "215": {"ticket_number": 107667, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "216": {"ticket_number": 107666, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "217": {"ticket_number": 107665, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "218": {"ticket_number": 107664, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:20Z"}, "219": {"ticket_number": 107663, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore: AD Network Account Creation - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:19Z"}, "220": {"ticket_number": 107662, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Contract KNA - Baltimore", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:58:19Z"}, "221": {"ticket_number": 107661, "subject": "Director approval request in SF", "status": 4, "priority": 1, "updated_at": "2024-10-30T18:55:40Z"}, "222": {"ticket_number": 107660, "subject": "FW: Message from Zoro: \"Zoro Order\" -  CASE4881562", "status": 3, "priority": 1, "updated_at": "2024-10-30T19:01:13Z"}, "223": {"ticket_number": 107659, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> shared \"Invoice from DDA Global\" with you", "status": 5, "priority": 3, "updated_at": "2024-10-30T21:14:03Z"}, "224": {"ticket_number": 107658, "subject": "Morin - Bristol - Quarterly Site Visit", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:43:13Z"}, "225": {"ticket_number": 107657, "subject": "FW: Power BI Access ", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:28:25Z"}, "226": {"ticket_number": 107655, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> Bullen by <PERSON><PERSON><PERSON><PERSON> Bullen: Employee Change Board - User Data", "status": 4, "priority": 2, "updated_at": "2024-10-30T21:19:31Z"}, "227": {"ticket_number": 107654, "subject": "SAP logon", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:21:24Z"}, "228": {"ticket_number": 107653, "subject": "HDMI on laptop not working", "status": 4, "priority": 1, "updated_at": "2024-10-30T21:30:48Z"}, "229": {"ticket_number": 107652, "subject": "Add <EMAIL> to KNA shift reports group", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:20:35Z"}, "230": {"ticket_number": 107651, "subject": "Outlook-  Mailbox Access", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:23:43Z"}, "231": {"ticket_number": 107650, "subject": " Add <EMAIL> to KNA-KIP-LNG-ShiftReports", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:07:43Z"}, "232": {"ticket_number": 107649, "subject": "Fw: TriMech Renewal Invoice #I-24-308947", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:53:27Z"}, "233": {"ticket_number": 107648, "subject": "Vicwest Hub Search option not working. ", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:43:20Z"}, "234": {"ticket_number": 107647, "subject": "New Vendor Request INEEL", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:31:42Z"}, "235": {"ticket_number": 107646, "subject": "Lansweeper - Unknown Asset - *************", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:43:19Z"}, "236": {"ticket_number": 107645, "subject": "Lansweeper - Unknown Asset - *************", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:28:26Z"}, "237": {"ticket_number": 107644, "subject": "Request for <PERSON> : Internet - Access Level Request (Cisco Umbrella SIG)", "status": 4, "priority": 2, "updated_at": "2024-10-30T18:13:25Z"}, "238": {"ticket_number": 107643, "subject": "FW: Your organization requires a Zoom upgrade", "status": 4, "priority": 3, "updated_at": "2024-10-30T17:18:47Z"}, "239": {"ticket_number": 107642, "subject": "MSC Vending Machine for Plant Supplies", "status": 5, "priority": 1, "updated_at": "2024-10-31T16:19:23Z"}, "240": {"ticket_number": 107641, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 4, "priority": 3, "updated_at": "2024-10-30T17:07:01Z"}, "241": {"ticket_number": 107640, "subject": "Internal Morin users emails and calendar invites sometimes not being delivered at all", "status": 4, "priority": 1, "updated_at": "2024-10-30T20:28:49Z"}, "242": {"ticket_number": 107639, "subject": "QO36 location needed ", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:21:39Z"}, "243": {"ticket_number": 107637, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 4, "priority": 3, "updated_at": "2024-10-31T12:25:06Z"}, "244": {"ticket_number": 107636, "subject": "Request for <PERSON> : <PERSON><PERSON> - Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:27Z"}, "245": {"ticket_number": 107634, "subject": "Request for Krista Dietz : Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:36:34Z"}, "246": {"ticket_number": 107635, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:36:35Z"}, "247": {"ticket_number": 107633, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: SAP S/4 HANA - Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:31:33Z"}, "248": {"ticket_number": 107632, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Request for <PERSON> by System:", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:26Z"}, "249": {"ticket_number": 107630, "subject": "Request for <PERSON> : Big Tin Can License Account - Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:39:05Z"}, "250": {"ticket_number": 107631, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Salesforce CRM - Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:45:03Z"}, "251": {"ticket_number": 107627, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:26Z"}, "252": {"ticket_number": 107628, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Finance and Fleet Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:26Z"}, "253": {"ticket_number": 107629, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:26Z"}, "254": {"ticket_number": 107626, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:43:25Z"}, "255": {"ticket_number": 107625, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-31T02:51:48Z"}, "256": {"ticket_number": 107624, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland", "status": 2, "priority": 2, "updated_at": "2024-10-31T02:51:26Z"}, "257": {"ticket_number": 107623, "subject": "Please delete", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:59:27Z"}, "258": {"ticket_number": 107622, "subject": "KNA-Columbus Back Office IDF: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T23:51:33Z"}, "259": {"ticket_number": 107621, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: SAP - AWIP - Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:30:50Z"}, "260": {"ticket_number": 107620, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 4, "priority": 2, "updated_at": "2024-10-30T22:12:24Z"}, "261": {"ticket_number": 107618, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 4, "priority": 2, "updated_at": "2024-10-31T15:41:09Z"}, "262": {"ticket_number": 107619, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 4, "priority": 2, "updated_at": "2024-10-30T15:56:23Z"}, "263": {"ticket_number": 107617, "subject": "Export MDM Records for Power BI", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:28:58Z"}, "264": {"ticket_number": 107616, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "265": {"ticket_number": 107613, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "266": {"ticket_number": 107614, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "267": {"ticket_number": 107615, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "268": {"ticket_number": 107611, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "269": {"ticket_number": 107612, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:26Z"}, "270": {"ticket_number": 107608, "subject": "Request for <PERSON>.henry : Big Tin Can - License Account", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:11:52Z"}, "271": {"ticket_number": 107609, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: KIP - Kingspan Insulated Panels - Concur New Account", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:25Z"}, "272": {"ticket_number": 107610, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: Credit Card - <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:13:25Z"}, "273": {"ticket_number": 107607, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland: AD Network Account Creation - <PERSON>.henry", "status": 5, "priority": 2, "updated_at": "2024-10-31T02:30:49Z"}, "274": {"ticket_number": 107606, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Deland", "status": 2, "priority": 1, "updated_at": "2024-10-31T02:30:24Z"}, "275": {"ticket_number": 107605, "subject": "SOLIDWORKS Activation Failure", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:16:44Z"}, "276": {"ticket_number": 107604, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:01:21Z"}, "277": {"ticket_number": 107603, "subject": "Install two UPS for Kensington", "status": 2, "priority": 1, "updated_at": "2024-10-30T22:58:09Z"}, "278": {"ticket_number": 107602, "subject": "[<PERSON><PERSON>] RESOLVED: UKG Timekeeping is down", "status": 4, "priority": 3, "updated_at": "2024-10-30T15:49:11Z"}, "279": {"ticket_number": 107601, "subject": "Setup Starlink for Kensington", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:58:19Z"}, "280": {"ticket_number": 107600, "subject": "PC Running Slow/Apps Freezing ", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:38:48Z"}, "281": {"ticket_number": 107599, "subject": "Setup SSO with Stampli for AWIP", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:25:30Z"}, "282": {"ticket_number": 107598, "subject": "[<PERSON><PERSON>] Security update from Felix | Shared accounts", "status": 4, "priority": 3, "updated_at": "2024-10-30T16:45:52Z"}, "283": {"ticket_number": 107597, "subject": "[<PERSON><PERSON>] Important update | Enabling Multi-Factor Identification", "status": 4, "priority": 3, "updated_at": "2024-10-30T16:42:22Z"}, "284": {"ticket_number": 107596, "subject": "Printer in Testing and Certs office (above deland shipping and receiving)", "status": 2, "priority": 1, "updated_at": "2024-10-31T11:55:29Z"}, "285": {"ticket_number": 107595, "subject": "Access to Printers", "status": 5, "priority": 1, "updated_at": "2024-10-31T11:54:42Z"}, "286": {"ticket_number": 107594, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 4, "priority": 3, "updated_at": "2024-10-30T15:09:33Z"}, "287": {"ticket_number": 107593, "subject": "Reset Password", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:08:29Z"}, "288": {"ticket_number": 107592, "subject": "<PERSON>: Portals Access | IT Systems and Portals - Access and Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:13:19Z"}, "289": {"ticket_number": 107591, "subject": "Disable from AS400.  ", "status": 5, "priority": 1, "updated_at": "2024-10-30T14:56:25Z"}, "290": {"ticket_number": 107590, "subject": "FW: Kingspan Airline Quote (Lift and More)", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:58:33Z"}, "291": {"ticket_number": 107589, "subject": "Fw: Password Expiration: Scan barcode to keep old password", "status": 4, "priority": 1, "updated_at": "2024-10-30T16:17:04Z"}, "292": {"ticket_number": 107587, "subject": "MOR-DEL  Laptop Stock Replenishment", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:58:31Z"}, "293": {"ticket_number": 107586, "subject": "RISA application graphics very slow", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:43:02Z"}, "294": {"ticket_number": 107585, "subject": "<PERSON> Can't log in", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:42:43Z"}, "295": {"ticket_number": 107584, "subject": "VBP-KEN-MDF-UPS2: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T17:53:03Z"}, "296": {"ticket_number": 107583, "subject": "Email received re-non-compliant // unsure if spam", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:37:58Z"}, "297": {"ticket_number": 107582, "subject": "Purchase Request for Network Cable Install : <PERSON>rin Bristol | Mon, 2024, Nov 4", "status": 2, "priority": 2, "updated_at": "2024-10-31T16:51:44Z"}, "298": {"ticket_number": 107581, "subject": "Fw: PWP copy machine", "status": 2, "priority": 1, "updated_at": "2024-10-30T15:25:43Z"}, "299": {"ticket_number": 107580, "subject": "Access to Google Workspace", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:28:31Z"}, "300": {"ticket_number": 107579, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 4, "priority": 3, "updated_at": "2024-10-30T14:07:31Z"}, "301": {"ticket_number": 107578, "subject": "[<PERSON><PERSON>] Re: Kingspan en Colombia logística digital", "status": 4, "priority": 3, "updated_at": "2024-10-30T21:15:59Z"}, "302": {"ticket_number": 107576, "subject": "KNA_VW_Saskatoon-B (10.150.200.196): <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-31T13:06:37Z"}, "303": {"ticket_number": 107575, "subject": "VBP-SAS-MDF-UPS2: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-31T13:06:37Z"}, "304": {"ticket_number": 107574, "subject": "<PERSON><PERSON>", "status": 4, "priority": 3, "updated_at": "2024-10-30T13:50:38Z"}, "305": {"ticket_number": 107572, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:17:48Z"}, "306": {"ticket_number": 107573, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:58:16Z"}, "307": {"ticket_number": 107571, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:58:15Z"}, "308": {"ticket_number": 107570, "subject": "[<PERSON><PERSON>] ", "status": 3, "priority": 3, "updated_at": "2024-10-30T21:34:46Z"}, "309": {"ticket_number": 107569, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Caledon: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T22:07:49Z"}, "310": {"ticket_number": 107568, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Caledon", "status": 2, "priority": 2, "updated_at": "2024-10-30T22:06:19Z"}, "311": {"ticket_number": 107567, "subject": "Account <PERSON><PERSON>", "status": 4, "priority": 2, "updated_at": "2024-10-30T14:17:28Z"}, "312": {"ticket_number": 107566, "subject": "Saving to X Drive", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:23:16Z"}, "313": {"ticket_number": 107565, "subject": "5046 Rona EDI Error/Warning  Vicwest invoice ********** UCP Code rejection ", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:34:38Z"}, "314": {"ticket_number": 107564, "subject": "5046 Rona EDI Error/Warning Vicwest Invoice ********** - UCP code rejection ", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:34:29Z"}, "315": {"ticket_number": 107563, "subject": "SAP Year end Tasks", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:28:04Z"}, "316": {"ticket_number": 107562, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:15:23Z"}, "317": {"ticket_number": 107561, "subject": "Laptops", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:13:18Z"}, "318": {"ticket_number": 107560, "subject": "Cannot start the computer ", "status": 4, "priority": 2, "updated_at": "2024-10-31T14:16:19Z"}, "319": {"ticket_number": 107559, "subject": "[<PERSON><PERSON>] Adjust", "status": 4, "priority": 3, "updated_at": "2024-10-30T13:05:54Z"}, "320": {"ticket_number": 107558, "subject": "SAP RELEASE ISSUES", "status": 4, "priority": 1, "updated_at": "2024-10-30T13:03:36Z"}, "321": {"ticket_number": 107557, "subject": "16168607 blocked by requisitioner", "status": 4, "priority": 1, "updated_at": "2024-10-30T13:01:27Z"}, "322": {"ticket_number": 107556, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-30T13:01:02Z"}, "323": {"ticket_number": 107555, "subject": "HP USB - C/A Universal Dock G2 not working properly", "status": 5, "priority": 1, "updated_at": "2024-10-31T11:09:20Z"}, "324": {"ticket_number": 107554, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:46:42Z"}, "325": {"ticket_number": 107553, "subject": "Request for <PERSON> : Adobe - Acrobat DC Pro", "status": 4, "priority": 2, "updated_at": "2024-10-31T14:47:34Z"}, "326": {"ticket_number": 107552, "subject": "Request for <PERSON> : Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:13:16Z"}, "327": {"ticket_number": 107551, "subject": "RE: SW Models for APAC", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:33:12Z"}, "328": {"ticket_number": 107550, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:32:12Z"}, "329": {"ticket_number": 107548, "subject": "*********.0446 - Deletion of M365 Group ", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:13:07Z"}, "330": {"ticket_number": 107547, "subject": "[<PERSON><PERSON>] UKG Timekeeping is down", "status": 4, "priority": 3, "updated_at": "2024-10-30T11:55:06Z"}, "331": {"ticket_number": 107546, "subject": "OLE ACtion", "status": 5, "priority": 1, "updated_at": "2024-10-30T11:21:22Z"}, "332": {"ticket_number": 107545, "subject": "KNA-AWP-Little Rock Boardroom: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T05:51:26Z"}, "333": {"ticket_number": 107544, "subject": "KNA-AWP-Little Rock Meetroom1: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T05:51:16Z"}, "334": {"ticket_number": 107543, "subject": "KNA-LittleRock-TrimShop-West: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T05:51:16Z"}, "335": {"ticket_number": 107542, "subject": "KNA-LittleRock-AP9-Maintenance Area: Ping (Ping): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T05:51:16Z"}, "336": {"ticket_number": 107541, "subject": "KNA-LittleRock-AP4-Plant Rear: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-30T05:51:12Z"}, "337": {"ticket_number": 107540, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:24:01Z"}, "338": {"ticket_number": 107539, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-31T00:13:38Z"}, "339": {"ticket_number": 107538, "subject": "Update KNA-L-12169 to Windows 11", "status": 4, "priority": 1, "updated_at": "2024-10-31T16:32:41Z"}, "340": {"ticket_number": 107537, "subject": "Update Needed for Twinmotion Application", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:43:14Z"}, "341": {"ticket_number": 107536, "subject": "Return old laptop KNA-L-8852, please ship to Modesto office, new laptop KNA-L-12820 is deployed to Julie.", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:26:15Z"}, "342": {"ticket_number": 107535, "subject": "as4oo and printer", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:25:10Z"}, "343": {"ticket_number": 107534, "subject": "Modesto Bldg 1 CS Printer Software Install Request", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:14:36Z"}, "344": {"ticket_number": 107533, "subject": "FW: AWIP Quote # 20386954 - Gretna Volleyball Complex", "status": 4, "priority": 1, "updated_at": "2024-10-30T12:21:38Z"}, "345": {"ticket_number": 107532, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-29T22:46:16Z"}, "346": {"ticket_number": 107531, "subject": "Tanium Patching -  Over 90 Days", "status": 2, "priority": 1, "updated_at": "2024-10-29T22:37:51Z"}, "347": {"ticket_number": 107530, "subject": "Email chain adding", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:53:00Z"}, "348": {"ticket_number": 107528, "subject": "[Phi<PERSON>]  Oportunidad emocionante: Rol de Gerente de Marketing Digital en Meta para la Región de las Américas ", "status": 5, "priority": 3, "updated_at": "2024-10-30T16:16:09Z"}, "349": {"ticket_number": 107527, "subject": "Tanium Scanning Issues -  KNAMON-ADMINPC1", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:42:19Z"}, "350": {"ticket_number": 107526, "subject": "Tanium Patching -  KNA-L-9620", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:13:20Z"}, "351": {"ticket_number": 107525, "subject": "Tanium Patching -  KNA-L-8338", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:18:53Z"}, "352": {"ticket_number": 107524, "subject": "Printer not working", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:00:07Z"}, "353": {"ticket_number": 107523, "subject": "Tanium Patching -  KNA-L-15014", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:19:40Z"}, "354": {"ticket_number": 107522, "subject": "Tanium Patching -  KNA-L-14818", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:13:20Z"}, "355": {"ticket_number": 107521, "subject": "[<PERSON><PERSON>] You've been enrolled in training | Vous avez été inscrit à une formation", "status": 4, "priority": 3, "updated_at": "2024-10-29T21:59:55Z"}, "356": {"ticket_number": 107520, "subject": "Tanium Patching -  KNA-L-14504", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:58:09Z"}, "357": {"ticket_number": 107519, "subject": "Tanium Patching -  KNA-L-14503", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:53:00Z"}, "358": {"ticket_number": 107518, "subject": "Engineering Drive access Ryan <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:44:10Z"}, "359": {"ticket_number": 107517, "subject": "Tanium Scanning Issues - KNA-L-13117", "status": 2, "priority": 1, "updated_at": "2024-10-30T16:58:19Z"}, "360": {"ticket_number": 107515, "subject": "Tanium Patching -  KNA-D-6888", "status": 4, "priority": 1, "updated_at": "2024-10-31T14:16:07Z"}, "361": {"ticket_number": 107514, "subject": "elecosoft - po # **********", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:50:36Z"}, "362": {"ticket_number": 107513, "subject": "FW: Your organization requires a Zoom upgrade", "status": 4, "priority": 3, "updated_at": "2024-10-30T12:16:18Z"}, "363": {"ticket_number": 107512, "subject": "Clearence", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:01:03Z"}, "364": {"ticket_number": 107511, "subject": "Blocked Account", "status": 4, "priority": 2, "updated_at": "2024-10-29T21:36:52Z"}, "365": {"ticket_number": 107510, "subject": "Add Sysadmins/Engineers to KNA-IT-Local Admin Workstations", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:25:01Z"}, "366": {"ticket_number": 107509, "subject": "Colonne dans le dashboard pour ajouter le courriel enregistré au compte du client? SAP", "status": 2, "priority": 1, "updated_at": "2024-10-30T13:38:44Z"}, "367": {"ticket_number": 107508, "subject": "[<PERSON><PERSON>] UKG Timekeeping is down", "status": 4, "priority": 3, "updated_at": "2024-10-29T21:18:45Z"}, "368": {"ticket_number": 107507, "subject": "RE: 1-101517 Lime slaker", "status": 4, "priority": 1, "updated_at": "2024-10-29T21:13:38Z"}, "369": {"ticket_number": 107506, "subject": "Fw: Your device is non-compliant", "status": 4, "priority": 3, "updated_at": "2024-10-29T21:12:54Z"}, "370": {"ticket_number": 107505, "subject": "[<PERSON><PERSON>] UKG Timekeeping is down", "status": 5, "priority": 3, "updated_at": "2024-10-29T21:11:24Z"}, "371": {"ticket_number": 107504, "subject": "FW: Laptop Issues", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:39:21Z"}, "372": {"ticket_number": 107503, "subject": "FW: Your device is non-compliant", "status": 4, "priority": 3, "updated_at": "2024-10-29T20:57:38Z"}, "373": {"ticket_number": 107502, "subject": "Request for <PERSON><PERSON> Bo<PERSON> : Add IP (***************) to Qualys IT Device and Data Maintenance", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:24:59Z"}, "374": {"ticket_number": 107501, "subject": "[<PERSON><PERSON>] 71731", "status": 5, "priority": 3, "updated_at": "2024-10-29T21:17:57Z"}, "375": {"ticket_number": 107500, "subject": "Restore Active Directory from Backup", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:58:04Z"}, "376": {"ticket_number": 107499, "subject": "[<PERSON><PERSON>] Follow-up!!", "status": 4, "priority": 3, "updated_at": "2024-10-29T20:42:10Z"}, "377": {"ticket_number": 107498, "subject": "UKG Down?", "status": 4, "priority": 1, "updated_at": "2024-10-30T13:35:29Z"}, "378": {"ticket_number": 107497, "subject": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-29T20:32:19Z"}, "379": {"ticket_number": 107496, "subject": "VBP-KEN-UPS01: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-29T20:31:42Z"}, "380": {"ticket_number": 107495, "subject": "Bluebeam PDF Editor", "status": 3, "priority": 1, "updated_at": "2024-10-31T16:16:20Z"}, "381": {"ticket_number": 107494, "subject": "KNA_VW_Moncton-B (**************): <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 4, "updated_at": "2024-10-29T20:28:15Z"}, "382": {"ticket_number": 107493, "subject": "Bluebeam install and RISA Update", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:40:58Z"}, "383": {"ticket_number": 107492, "subject": "UKG LOGIN ERROR", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:23:15Z"}, "384": {"ticket_number": 107491, "subject": "AWIP paint code - 436RZ1348", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:34:37Z"}, "385": {"ticket_number": 107490, "subject": "Power BI Report Builder Install", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:40:47Z"}, "386": {"ticket_number": 107489, "subject": "RE: Past-due PO Maintenance - 241028", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:51:36Z"}, "387": {"ticket_number": 107488, "subject": "AWIP Request to Grant access to Shared Mailbox", "status": 5, "priority": 1, "updated_at": "2024-10-29T21:42:48Z"}, "388": {"ticket_number": 107487, "subject": "[<PERSON><PERSON>] Changes In Payroll Information", "status": 5, "priority": 3, "updated_at": "2024-10-30T16:17:15Z"}, "389": {"ticket_number": 107486, "subject": "ACCESS REQUEST:  <EMAIL>", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:36:16Z"}, "390": {"ticket_number": 107485, "subject": "SEC-UAC-01-AD - Review & Disable AD Inactive Accounts - 30 Days Inactive", "status": 2, "priority": 3, "updated_at": "2024-10-30T06:58:12Z"}, "391": {"ticket_number": 107484, "subject": "message d'erreur", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:00:17Z"}, "392": {"ticket_number": 107483, "subject": "Request for Rudran Pillay : Headset charging cable - IT Parts & Accessories", "status": 4, "priority": 2, "updated_at": "2024-10-29T19:52:05Z"}, "393": {"ticket_number": 107482, "subject": "billing issue so#1508883 ", "status": 5, "priority": 1, "updated_at": "2024-10-29T19:45:58Z"}, "394": {"ticket_number": 107481, "subject": "FW: Password Expiration: Scan barcode to keep old password", "status": 4, "priority": 3, "updated_at": "2024-10-31T16:54:06Z"}, "395": {"ticket_number": 107480, "subject": "[<PERSON><PERSON>] Vicwest", "status": 4, "priority": 3, "updated_at": "2024-10-29T19:45:09Z"}, "396": {"ticket_number": 107479, "subject": "New computer issue / Unable to email excel file from SAP", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:25:53Z"}, "397": {"ticket_number": 107478, "subject": "Laptop not connecting to WIFI or Internet when is not on desk", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:17:06Z"}, "398": {"ticket_number": 107477, "subject": "FW: IT - Email Issue", "status": 4, "priority": 1, "updated_at": "2024-10-30T18:42:46Z"}, "399": {"ticket_number": 107476, "subject": "Fw: Salesforce loggin issues", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:49:24Z"}, "400": {"ticket_number": 107475, "subject": "FW: Powerbi Access", "status": 4, "priority": 1, "updated_at": "2024-10-30T18:07:33Z"}, "401": {"ticket_number": 107474, "subject": "unblock websites", "status": 4, "priority": 1, "updated_at": "2024-10-29T20:21:35Z"}, "402": {"ticket_number": 107473, "subject": "[<PERSON><PERSON>] ATTENTION REQUIRED Jessica", "status": 5, "priority": 3, "updated_at": "2024-10-31T18:37:58Z"}, "403": {"ticket_number": 107471, "subject": "URGENT ORDER LOCKED IN SAP 1000097127", "status": 5, "priority": 3, "updated_at": "2024-10-30T17:29:26Z"}, "404": {"ticket_number": 107470, "subject": "Potential Phishing incidence", "status": 5, "priority": 3, "updated_at": "2024-10-31T18:37:57Z"}, "405": {"ticket_number": 107469, "subject": "Outgoing phone calls", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:19:48Z"}, "406": {"ticket_number": 107468, "subject": "Request for <PERSON> : Microsoft Visio - Flowchart Maker and Diagramming Software", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:34:58Z"}, "407": {"ticket_number": 107467, "subject": "SAP, unable to PGI -  101043 SVC coil order release", "status": 4, "priority": 1, "updated_at": "2024-10-29T19:06:56Z"}, "408": {"ticket_number": 107466, "subject": "Shire Email", "status": 5, "priority": 1, "updated_at": "2024-10-29T18:42:19Z"}, "409": {"ticket_number": 107465, "subject": "Signature Change", "status": 4, "priority": 1, "updated_at": "2024-10-30T17:31:19Z"}, "410": {"ticket_number": 107464, "subject": "Telisha <PERSON> -SharePoint Access", "status": 5, "priority": 1, "updated_at": "2024-10-31T18:37:56Z"}, "411": {"ticket_number": 107463, "subject": "Monitors went White", "status": 4, "priority": 1, "updated_at": "2024-10-29T18:54:58Z"}, "412": {"ticket_number": 107462, "subject": "<PERSON><PERSON> is running slowly", "status": 2, "priority": 1, "updated_at": "2024-10-29T22:32:56Z"}, "413": {"ticket_number": 107461, "subject": "AWIP: Update Zoom Call Flow during <PERSON>'s vacation", "status": 4, "priority": 1, "updated_at": "2024-10-29T23:39:05Z"}, "414": {"ticket_number": 107460, "subject": "Unable to Access R or J Drives at the Red Lion Annex Location", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:23:47Z"}, "415": {"ticket_number": 107459, "subject": "Setup Desk phone", "status": 5, "priority": 1, "updated_at": "2024-10-29T18:23:14Z"}, "416": {"ticket_number": 107458, "subject": "Home Hardware Stores LTD - [Invoice] Discrepancies - Reference # ********** V# 7378 PO# <PERSON> sending", "status": 5, "priority": 1, "updated_at": "2024-10-29T18:09:55Z"}, "417": {"ticket_number": 107457, "subject": "Request for <PERSON> : Stormwind Studios - Software License Renewal", "status": 4, "priority": 2, "updated_at": "2024-10-29T20:34:37Z"}, "418": {"ticket_number": 107456, "subject": "Fw: Please Unlock File Share Outside of Organization in OneDrive", "status": 4, "priority": 2, "updated_at": "2024-10-31T16:59:34Z"}, "419": {"ticket_number": 107455, "subject": "Need password reset", "status": 5, "priority": 2, "updated_at": "2024-10-29T17:36:40Z"}, "420": {"ticket_number": 107454, "subject": "Computer Supplies", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:02:47Z"}, "421": {"ticket_number": 107453, "subject": "Add AWIP color code 431R3142D Snow White", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:25:27Z"}, "422": {"ticket_number": 107452, "subject": "ASM Computer", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:52:03Z"}, "423": {"ticket_number": 107451, "subject": "You can now download your file", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:20:07Z"}, "424": {"ticket_number": 107450, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 4, "priority": 2, "updated_at": "2024-10-30T18:42:01Z"}, "425": {"ticket_number": 107449, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 4, "priority": 2, "updated_at": "2024-10-30T18:41:26Z"}, "426": {"ticket_number": 107448, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 4, "priority": 2, "updated_at": "2024-10-30T18:40:34Z"}, "427": {"ticket_number": 107447, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 4, "priority": 2, "updated_at": "2024-10-30T18:39:50Z"}, "428": {"ticket_number": 107446, "subject": "extend to plant 1019 for procurement - 1000003602 POLYFILM E6Y-120CWX Yellow 45.5” x 3000’", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:00:23Z"}, "429": {"ticket_number": 107445, "subject": "KNA-M-13668 transferring to KAF", "status": 5, "priority": 2, "updated_at": "2024-10-31T16:37:45Z"}, "430": {"ticket_number": 107444, "subject": "Request for Revit 2024 Installation", "status": 4, "priority": 1, "updated_at": "2024-10-29T21:20:46Z"}, "431": {"ticket_number": 107443, "subject": "Hackers", "status": 2, "priority": 3, "updated_at": "2024-10-31T18:22:16Z"}, "432": {"ticket_number": 107442, "subject": "FW: Milcon Construction Corp", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:14:50Z"}, "433": {"ticket_number": 107441, "subject": "SolidWorks license update", "status": 2, "priority": 1, "updated_at": "2024-10-30T12:07:27Z"}, "434": {"ticket_number": 107440, "subject": "FW: Your device is non-compliant", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:36:18Z"}, "435": {"ticket_number": 107439, "subject": "Shipping printer - Modesto CA ", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:47:31Z"}, "436": {"ticket_number": 107438, "subject": "Request for <PERSON>: access to Stormwinds | IT Systems and Portals - Access and Permissions | Requested by: <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:13:05Z"}, "437": {"ticket_number": 107437, "subject": "Request for <PERSON>: Access to Qualys | IT Systems and Portals - Access and Permissions | Requested by: <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:52:09Z"}, "438": {"ticket_number": 107436, "subject": "*********.0445 - Deletion of M365 Group ", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:32:56Z"}, "439": {"ticket_number": 107435, "subject": "Auto Refresh Plus - VAC", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:42:28Z"}, "440": {"ticket_number": 107434, "subject": "Auto Refresh Plus - LIT", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:53:57Z"}, "441": {"ticket_number": 107433, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-31T15:37:42Z"}, "442": {"ticket_number": 107432, "subject": "FW: Access for Part Number Creation - AS400/Net-Link", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:04:10Z"}, "443": {"ticket_number": 107431, "subject": "Request for <PERSON> : Video Conferencing System - Teams Room for Boardrooms - VBP - Burlington", "status": 2, "priority": 2, "updated_at": "2024-10-29T22:19:43Z"}, "444": {"ticket_number": 107430, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:02:44Z"}, "445": {"ticket_number": 107429, "subject": "WeTransfer Site Blocked ", "status": 4, "priority": 1, "updated_at": "2024-10-30T16:26:41Z"}, "446": {"ticket_number": 107428, "subject": "KNAMON-ADMINPC1", "status": 4, "priority": 1, "updated_at": "2024-10-30T16:51:14Z"}, "447": {"ticket_number": 107427, "subject": "ORG Chart - Teams / Outlook", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:25:07Z"}, "448": {"ticket_number": 107426, "subject": "De<PERSON> not pulling in MRP", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:28:31Z"}, "449": {"ticket_number": 107425, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T01:50:25Z"}, "450": {"ticket_number": 107424, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:26:09Z"}, "451": {"ticket_number": 107423, "subject": "SEC-UAC-05: AD - Review and Disable AD Active & HR Terminated Accounts", "status": 2, "priority": 4, "updated_at": "2024-10-30T14:46:28Z"}, "452": {"ticket_number": 107422, "subject": "Tate Jessup Calendars", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:18:45Z"}, "453": {"ticket_number": 107421, "subject": "Jabra headset not working", "status": 4, "priority": 1, "updated_at": "2024-10-29T19:20:20Z"}, "454": {"ticket_number": 107420, "subject": "Password reset", "status": 5, "priority": 2, "updated_at": "2024-10-31T17:38:00Z"}, "455": {"ticket_number": 107419, "subject": "SAPB1 License/Login", "status": 2, "priority": 1, "updated_at": "2024-10-29T19:11:22Z"}, "456": {"ticket_number": 107418, "subject": "Software (C-More EA9 V 6.78)", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:37:59Z"}, "457": {"ticket_number": 107417, "subject": "Agregar propietarios al grupo de TEAMS KA-KCO-Comite-Reclamos", "status": 5, "priority": 1, "updated_at": "2024-10-29T15:19:22Z"}, "458": {"ticket_number": 107416, "subject": "Request for <PERSON><PERSON> : KNA-L-7851 - Entra Joined. IT Device and Data Maintenance", "status": 5, "priority": 2, "updated_at": "2024-10-31T14:38:30Z"}, "459": {"ticket_number": 107415, "subject": "Replacing SNMP Version 1/2c on Factory devices", "status": 2, "priority": 1, "updated_at": "2024-10-29T22:28:08Z"}, "460": {"ticket_number": 107414, "subject": "Replacing SNMP Version 1/2c on network devices", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:10:28Z"}, "461": {"ticket_number": 107413, "subject": "Replacing SNMP Version 1/2c on Printer devices.", "status": 2, "priority": 1, "updated_at": "2024-10-29T17:49:00Z"}, "462": {"ticket_number": 107412, "subject": "kingspanamericas | CYDERES-1831272 | Change DHCP Relay to DHCP Server | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 2, "priority": 2, "updated_at": "2024-10-31T05:28:07Z"}, "463": {"ticket_number": 107411, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Printer", "status": 2, "priority": 2, "updated_at": "2024-10-30T13:43:07Z"}, "464": {"ticket_number": 107410, "subject": "FW: 3-7959 NLK Project", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:19:31Z"}, "465": {"ticket_number": 107409, "subject": "Forwarding Emails", "status": 4, "priority": 1, "updated_at": "2024-10-29T23:42:05Z"}, "466": {"ticket_number": 107408, "subject": "5046 Rona EDI Error/Warning  Vicwest invoice 91704816 UCP rejection ", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:25:11Z"}, "467": {"ticket_number": 107407, "subject": "<PERSON> Message Forwarding", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:14:42Z"}, "468": {"ticket_number": 107405, "subject": "Stratford Phone Directory", "status": 5, "priority": 2, "updated_at": "2024-10-29T14:22:19Z"}, "469": {"ticket_number": 107404, "subject": "Desbloqueo de cuenta", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:58:46Z"}, "470": {"ticket_number": 107403, "subject": "Viewing attachments in SAP", "status": 2, "priority": 1, "updated_at": "2024-10-29T21:13:06Z"}, "471": {"ticket_number": 107401, "subject": "cellulaire ", "status": 5, "priority": 2, "updated_at": "2024-10-29T14:24:00Z"}, "472": {"ticket_number": 107400, "subject": "Request for <PERSON> : Power BI - Licensing for User and Workspaces", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:11:59Z"}, "473": {"ticket_number": 107399, "subject": "Request by <PERSON><PERSON><PERSON> for <PERSON> Cook : Credit Card - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T19:57:37Z"}, "474": {"ticket_number": 107398, "subject": "Request for <PERSON><PERSON> : KNA-L-8156 - Entra Joined. IT Device and Data Maintenance", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:37:47Z"}, "475": {"ticket_number": 107397, "subject": "Paint Code - ADD - 439TCZ7512M/439RZ11309D", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:42:48Z"}, "476": {"ticket_number": 107396, "subject": "Request for <PERSON> : Memory Upgrade", "status": 2, "priority": 2, "updated_at": "2024-10-30T13:27:58Z"}, "477": {"ticket_number": 107395, "subject": "[<PERSON><PERSON>] ", "status": 4, "priority": 3, "updated_at": "2024-10-29T20:19:02Z"}, "478": {"ticket_number": 107394, "subject": "SAP password reset", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:39:18Z"}, "479": {"ticket_number": 107393, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:45:24Z"}, "480": {"ticket_number": 107392, "subject": "EUS2-KNA-DRIQB: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 2 % (Free Space Q:) is below the error limit of 10 % in Free Space Q: - 14 % (Free Space C:) is below the warning limit of 25 % in Free Space C:", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:37:49Z"}, "481": {"ticket_number": 107391, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-29T13:00:56Z"}, "482": {"ticket_number": 107390, "subject": "Account expired", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:37:46Z"}, "483": {"ticket_number": 107389, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-29T18:55:26Z"}, "484": {"ticket_number": 107388, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-29T12:32:12Z"}, "485": {"ticket_number": 107387, "subject": "[<PERSON><PERSON>] Vicwest-Scheduled Benefit Payout Batch-8352 on October 28, 2024", "status": 4, "priority": 3, "updated_at": "2024-10-29T21:03:15Z"}, "486": {"ticket_number": 107385, "subject": "KNA-Columbus Back Office IDF: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-31T15:37:41Z"}, "487": {"ticket_number": 107384, "subject": "KNA-Columbus Plant IDF: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-31T15:37:40Z"}, "488": {"ticket_number": 107383, "subject": "KNA-DEL-FAC-SHIPPING: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-31T08:39:13Z"}, "489": {"ticket_number": 107382, "subject": "KNA-DEL-FAC-SHIPPING YARD: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-31T08:39:12Z"}, "490": {"ticket_number": 107381, "subject": "KIP-DEL-PlantShippingOffice-IDF-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-31T08:39:12Z"}, "491": {"ticket_number": 107380, "subject": "Operator  Access", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:37:38Z"}, "492": {"ticket_number": 107379, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:13:34Z"}, "493": {"ticket_number": 107378, "subject": "Desbloqueo de cuenta siesa", "status": 5, "priority": 1, "updated_at": "2024-10-29T04:48:23Z"}, "494": {"ticket_number": 107377, "subject": "Update To Commission Report", "status": 5, "priority": 1, "updated_at": "2024-10-31T05:37:01Z"}, "495": {"ticket_number": 107376, "subject": "Lansweeper-kna-l-14492- Active device not assigned to a user", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:24:28Z"}, "496": {"ticket_number": 107375, "subject": "Lansweeper- \t KNA-5CG9251N2D- unknown Device scanned", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:10:26Z"}, "497": {"ticket_number": 107374, "subject": "Lansweeper- KILWW21698NB- unknow device scanned", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:46:25Z"}, "498": {"ticket_number": 107373, "subject": "Power BI Cannot be exported -Month End ", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:01:41Z"}, "499": {"ticket_number": 107372, "subject": "User lost Ipad, lock devices and line suspended so no can use it", "status": 2, "priority": 1, "updated_at": "2024-10-28T23:34:18Z"}, "500": {"ticket_number": 107371, "subject": "Qualys Level 3 Internal Vulnerability - *************** - 48169 ~ Remote Management Service Accepting Unencrypted Credentials Detected (FTP)", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:26:52Z"}, "501": {"ticket_number": 107370, "subject": "Possible Virus", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:24:08Z"}, "502": {"ticket_number": 107369, "subject": "Qualys Level4 Internal Vulnerability - ************ - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 2, "priority": 1, "updated_at": "2024-10-29T22:13:16Z"}, "503": {"ticket_number": 107368, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : AutoDesk Software Install", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:38:58Z"}, "504": {"ticket_number": 107367, "subject": "Qualys Level 4 Internal Vulnerability - ************** - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:42:12Z"}, "505": {"ticket_number": 107366, "subject": "SAP Address Missing from SO 1-100535 Simplot PWT Building Roof", "status": 5, "priority": 1, "updated_at": "2024-10-30T14:56:12Z"}, "506": {"ticket_number": 107364, "subject": "Qualys Level 5 Internal Vulnerability - ************* - VMware vCenter Server Multiple Security Vulnerabilities (VMSA-2024-0019)", "status": 2, "priority": 1, "updated_at": "2024-10-29T21:43:24Z"}, "507": {"ticket_number": 107363, "subject": "Request for Nancy Garza : Non-Standard Application - No License Required", "status": 4, "priority": 2, "updated_at": "2024-10-30T21:12:36Z"}, "508": {"ticket_number": 107362, "subject": "[<PERSON><PERSON>] ", "status": 5, "priority": 3, "updated_at": "2024-10-29T18:59:19Z"}, "509": {"ticket_number": 107361, "subject": "Add domain to Mimecast Safe Senders Whitelist - ddisurveys.com", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:37:07Z"}, "510": {"ticket_number": 107360, "subject": "********** - LMSF Project - New Skins", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:38:29Z"}, "511": {"ticket_number": 107359, "subject": "Fw: Please Unlock File Share Outside of Organization in OneDrive", "status": 4, "priority": 2, "updated_at": "2024-10-30T20:13:55Z"}, "512": {"ticket_number": 107358, "subject": "FW: Bluebeam", "status": 2, "priority": 1, "updated_at": "2024-10-29T15:42:45Z"}, "513": {"ticket_number": 107357, "subject": "[<PERSON><PERSON>] Vicwest-Scheduled Benefit Payout Batch-8352 on October 28, 2024", "status": 5, "priority": 3, "updated_at": "2024-10-31T13:37:45Z"}, "514": {"ticket_number": 107356, "subject": "Can't print from customer service printer.. seeing this message", "status": 5, "priority": 1, "updated_at": "2024-10-28T21:22:40Z"}, "515": {"ticket_number": 107355, "subject": "Request for <PERSON><PERSON> : Multifactor Authentication", "status": 5, "priority": 2, "updated_at": "2024-10-28T21:19:19Z"}, "516": {"ticket_number": 107354, "subject": "Unable to access \\\\knamod-fsns1\\Modesto\\", "status": 5, "priority": 1, "updated_at": "2024-10-31T12:37:48Z"}, "517": {"ticket_number": 107353, "subject": "Vicwest tech items for Burlington", "status": 2, "priority": 1, "updated_at": "2024-10-31T15:28:20Z"}, "518": {"ticket_number": 107352, "subject": "[<PERSON><PERSON>] Anixter Inquiry on Tate Inc. Product Portfolio", "status": 5, "priority": 3, "updated_at": "2024-10-28T21:29:32Z"}, "519": {"ticket_number": 107350, "subject": "Accounting printer install", "status": 5, "priority": 1, "updated_at": "2024-10-28T21:03:56Z"}, "520": {"ticket_number": 107349, "subject": "RE: VB", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:17:25Z"}, "521": {"ticket_number": 107348, "subject": "Can't login to new laptop", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:59:34Z"}, "522": {"ticket_number": 107347, "subject": "PO Release 40", "status": 5, "priority": 1, "updated_at": "2024-10-29T12:08:43Z"}, "523": {"ticket_number": 107346, "subject": "Request for Ram Balasubramanian : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-28T21:02:18Z"}, "524": {"ticket_number": 107345, "subject": "**********- Invoice not showing properly", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:37:32Z"}, "525": {"ticket_number": 107344, "subject": "EFIN down?", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:16:58Z"}, "526": {"ticket_number": 107343, "subject": "NET LINK - Login Issue", "status": 5, "priority": 3, "updated_at": "2024-10-28T20:16:26Z"}, "527": {"ticket_number": 107342, "subject": "Adobe convert PDF to Word", "status": 2, "priority": 1, "updated_at": "2024-10-30T19:28:13Z"}, "528": {"ticket_number": 107341, "subject": "Home Hardware Stores LTD - [Invoice] Discrepancies - Reference # ********** V# 7378 PO# <PERSON> sending", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:25:24Z"}, "529": {"ticket_number": 107340, "subject": "PRINTER NOT SCANNING ", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:48:19Z"}, "530": {"ticket_number": 107339, "subject": "Printer", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:39:18Z"}, "531": {"ticket_number": 107338, "subject": "Unlock <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:37:32Z"}, "532": {"ticket_number": 107337, "subject": "Lost iPad 12628", "status": 2, "priority": 1, "updated_at": "2024-10-28T19:51:00Z"}, "533": {"ticket_number": 107336, "subject": "Request for <PERSON> : Internet - Access Level Request (Cisco Umbrella SIG)", "status": 5, "priority": 2, "updated_at": "2024-10-29T19:55:11Z"}, "534": {"ticket_number": 107335, "subject": "Receiving not allowed", "status": 5, "priority": 1, "updated_at": "2024-10-28T19:50:14Z"}, "535": {"ticket_number": 107334, "subject": "[<PERSON><PERSON>] Vicwest-Scheduled Benefit Payout Batch-8352 on October 28, 2024", "status": 5, "priority": 3, "updated_at": "2024-10-28T19:49:56Z"}, "536": {"ticket_number": 107333, "subject": "Transfer of cell phone number ", "status": 2, "priority": 1, "updated_at": "2024-10-30T15:03:44Z"}, "537": {"ticket_number": 107332, "subject": "CHANGE VENDOR - IRI Consultants (17771)", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:24:23Z"}, "538": {"ticket_number": 107331, "subject": "Request for <PERSON> : External Sharing - OneDrive / SharePoint", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:37:45Z"}, "539": {"ticket_number": 107330, "subject": "Grant access to <PERSON> OneDrive files", "status": 5, "priority": 2, "updated_at": "2024-10-31T00:39:24Z"}, "540": {"ticket_number": 107328, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:55:19Z"}, "541": {"ticket_number": 107329, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON> Paul: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T03:40:02Z"}, "542": {"ticket_number": 107326, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:54:51Z"}, "543": {"ticket_number": 107327, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON> Paul: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T04:24:44Z"}, "544": {"ticket_number": 107324, "subject": "New Vendor:  Hyatt House Vacaville", "status": 5, "priority": 1, "updated_at": "2024-10-28T18:35:13Z"}, "545": {"ticket_number": 107323, "subject": "AS400", "status": 5, "priority": 1, "updated_at": "2024-10-28T19:50:35Z"}, "546": {"ticket_number": 107322, "subject": "New Vendor:  <PERSON><PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-28T18:21:43Z"}, "547": {"ticket_number": 107321, "subject": "Autodesk low usage users", "status": 4, "priority": 1, "updated_at": "2024-10-29T23:18:31Z"}, "548": {"ticket_number": 107320, "subject": "Adobe Acrobat not detecting printer", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:39:18Z"}, "549": {"ticket_number": 107319, "subject": "CHANGE VENDOR - Westbound Solar 3, LLC (20383)", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:46:12Z"}, "550": {"ticket_number": 107318, "subject": "<PERSON><PERSON><PERSON> (Langley) windows log on reset", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:44Z"}, "551": {"ticket_number": 107317, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-28T19:42:52Z"}, "552": {"ticket_number": 107316, "subject": "CAD030 Vicwest Inc - Schedule SAP batch jobs for month-end production order settlement", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:30:16Z"}, "553": {"ticket_number": 107315, "subject": "Request for <PERSON><PERSON> Boudhina : Requesting 1 Velocloud Edge for Tate ASP BUL (3400 Landmark Road Burlington) | Site Circuit ADD - Internet Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:43:19Z"}, "554": {"ticket_number": 107314, "subject": "(Merged to  107685) Cancel POs off AS400", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:21:48Z"}, "555": {"ticket_number": 107313, "subject": "Request for <PERSON> : SketchUp", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:22:26Z"}, "556": {"ticket_number": 107312, "subject": "Habilitación usuario Siesa Aprendiz ", "status": 5, "priority": 1, "updated_at": "2024-10-30T04:07:19Z"}, "557": {"ticket_number": 107311, "subject": "KNA-KIP-Caledon IT Meeting Room: Ping (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-30T16:39:52Z"}, "558": {"ticket_number": 107310, "subject": "5046 Rona EDI Error/Warning  Vicwest invoice ******** UCP code rejection", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:34:21Z"}, "559": {"ticket_number": 107309, "subject": "Change old to new number", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:14:59Z"}, "560": {"ticket_number": 107308, "subject": "Locked out of PC / Password Reset", "status": 5, "priority": 2, "updated_at": "2024-10-28T17:12:48Z"}, "561": {"ticket_number": 107307, "subject": "Monitors not working", "status": 2, "priority": 1, "updated_at": "2024-10-29T16:57:00Z"}, "562": {"ticket_number": 107306, "subject": "Request for <PERSON><PERSON> : Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:42:05Z"}, "563": {"ticket_number": 107305, "subject": "[<PERSON><PERSON>] SHEET METAL", "status": 5, "priority": 3, "updated_at": "2024-10-30T19:39:17Z"}, "564": {"ticket_number": 107304, "subject": "[<PERSON><PERSON>] All Weather Insulated Panels", "status": 5, "priority": 3, "updated_at": "2024-10-30T16:39:51Z"}, "565": {"ticket_number": 107303, "subject": "NEED ACCESS TO THERM", "status": 5, "priority": 1, "updated_at": "2024-10-31T13:00:12Z"}, "566": {"ticket_number": 107302, "subject": "<PERSON> Account locked", "status": 5, "priority": 2, "updated_at": "2024-10-28T19:56:02Z"}, "567": {"ticket_number": 107301, "subject": "Phone Replacement", "status": 2, "priority": 1, "updated_at": "2024-10-30T15:24:37Z"}, "568": {"ticket_number": 107298, "subject": "AWIP finish code - 436TCR3652", "status": 5, "priority": 1, "updated_at": "2024-10-28T16:47:28Z"}, "569": {"ticket_number": 107297, "subject": "PO date entry ", "status": 2, "priority": 1, "updated_at": "2024-10-28T18:26:35Z"}, "570": {"ticket_number": 107296, "subject": "Export MDM Records for Power BI", "status": 5, "priority": 1, "updated_at": "2024-10-28T23:31:15Z"}, "571": {"ticket_number": 107294, "subject": "Off-Board Armani Colon / - Requested By <PERSON>: Salesforce CRM - Termination", "status": 5, "priority": 2, "updated_at": "2024-10-28T15:17:38Z"}, "572": {"ticket_number": 107295, "subject": "Off-Board Armani Colon / - Requested By <PERSON>: SAP - AWIP - Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-28T16:45:33Z"}, "573": {"ticket_number": 107292, "subject": "Off-Board Armani Colon / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:43Z"}, "574": {"ticket_number": 107291, "subject": "Off-Board Armani Colon / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:43Z"}, "575": {"ticket_number": 107293, "subject": "Off-Board Armani Colon / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:37:47Z"}, "576": {"ticket_number": 107290, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-28T16:02:37Z"}, "577": {"ticket_number": 107289, "subject": "[<PERSON><PERSON>] b<PERSON><PERSON><PERSON>@grindertaber.com shared \"GRINDER TABER & GRINDER INC  MLGW 2025 PROJECTS\" with you", "status": 5, "priority": 3, "updated_at": "2024-10-29T19:01:00Z"}, "578": {"ticket_number": 107288, "subject": "[<PERSON><PERSON>] Thanks Receipt customization Orders-Complete with Docusign: Invoice-xt2d3468vr57hgt54sf66750.pdf", "status": 5, "priority": 3, "updated_at": "2024-10-29T19:33:30Z"}, "579": {"ticket_number": 107287, "subject": "Outlook", "status": 2, "priority": 1, "updated_at": "2024-10-29T20:20:11Z"}, "580": {"ticket_number": 107286, "subject": "Unable to print to printer at Red Lion Annex", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:37:46Z"}, "581": {"ticket_number": 107285, "subject": "Re: Mattoon Phone Directory", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:28:31Z"}, "582": {"ticket_number": 107284, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: SAP ECC - Vicwest Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-29T17:25:40Z"}, "583": {"ticket_number": 107283, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:18:24Z"}, "584": {"ticket_number": 107282, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:18:05Z"}, "585": {"ticket_number": 107281, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:18:23Z"}, "586": {"ticket_number": 107280, "subject": "FW: Enable external sharing ", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:39:51Z"}, "587": {"ticket_number": 107279, "subject": "[<PERSON><PERSON>] FW: Columbus Airport - Damaged Wall Panels", "status": 4, "priority": 3, "updated_at": "2024-10-29T21:28:45Z"}, "588": {"ticket_number": 107278, "subject": "Download ", "status": 5, "priority": 1, "updated_at": "2024-10-28T19:31:28Z"}, "589": {"ticket_number": 107277, "subject": "Account locked out", "status": 2, "priority": 1, "updated_at": "2024-10-29T00:24:13Z"}, "590": {"ticket_number": 107276, "subject": "AP Mailbox Signature ", "status": 4, "priority": 1, "updated_at": "2024-10-30T23:22:22Z"}, "591": {"ticket_number": 107275, "subject": "Calendar Issue ", "status": 5, "priority": 1, "updated_at": "2024-10-29T21:51:33Z"}, "592": {"ticket_number": 107274, "subject": "Request for <PERSON> : SolidWorks - License and Subscription", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:13:24Z"}, "593": {"ticket_number": 107273, "subject": "<PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:37:46Z"}, "594": {"ticket_number": 107272, "subject": "Deland Stock - Headsets & Chargers", "status": 2, "priority": 2, "updated_at": "2024-10-28T21:10:39Z"}, "595": {"ticket_number": 107271, "subject": "NEW VENDOR - TRANE US INC", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:32:10Z"}, "596": {"ticket_number": 107270, "subject": "Locked Out - SAP", "status": 5, "priority": 1, "updated_at": "2024-10-28T15:45:08Z"}, "597": {"ticket_number": 107269, "subject": "Fw: KINGSPAN_SFTP - ", "status": 5, "priority": 1, "updated_at": "2024-10-28T16:53:26Z"}, "598": {"ticket_number": 107267, "subject": "Vendor LT column for Shop Floor+ \"BOM All Items Inquiry\" view", "status": 2, "priority": 1, "updated_at": "2024-10-28T20:32:00Z"}, "599": {"ticket_number": 107266, "subject": "Copier in B1 Office not printing", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:48:48Z"}, "600": {"ticket_number": 107264, "subject": "Request for <PERSON> : Email Feature Requests", "status": 5, "priority": 2, "updated_at": "2024-10-28T15:03:08Z"}, "601": {"ticket_number": 107263, "subject": "HAWA In-Stocks not working", "status": 2, "priority": 1, "updated_at": "2024-10-30T13:51:31Z"}, "602": {"ticket_number": 107262, "subject": "SAP locked out ", "status": 5, "priority": 1, "updated_at": "2024-10-28T15:01:27Z"}, "603": {"ticket_number": 107261, "subject": "<PERSON><PERSON><PERSON> - <PERSON><PERSON> - SAP B1 RPD Access ", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:43Z"}, "604": {"ticket_number": 107260, "subject": "SEC-VMNSP-06 Weekly Assessments of Internal Vulnerabilities", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:39:30Z"}, "605": {"ticket_number": 107259, "subject": "SEC-VMNSP-05 Weekly Assessments of External vulnerabilities", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:21:05Z"}, "606": {"ticket_number": 107258, "subject": "SolidWorks Install Request", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:39:17Z"}, "607": {"ticket_number": 107257, "subject": "Share Point Access", "status": 2, "priority": 1, "updated_at": "2024-10-28T18:08:50Z"}, "608": {"ticket_number": 107256, "subject": "Title change", "status": 2, "priority": 1, "updated_at": "2024-10-28T15:24:52Z"}, "609": {"ticket_number": 107255, "subject": "Create/Change PO Access reinstate", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:42:34Z"}, "610": {"ticket_number": 107254, "subject": "Blue Screen", "status": 4, "priority": 1, "updated_at": "2024-10-29T20:37:11Z"}, "611": {"ticket_number": 107253, "subject": "Azure Cosmos DB | Shipping Photo App", "status": 2, "priority": 1, "updated_at": "2024-10-28T21:43:11Z"}, "612": {"ticket_number": 107251, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Burlington: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:24Z"}, "613": {"ticket_number": 107252, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Burlington: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T04:54:00Z"}, "614": {"ticket_number": 107250, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Burlington: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:24Z"}, "615": {"ticket_number": 107249, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Burlington", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:00:16Z"}, "616": {"ticket_number": 107248, "subject": "[<PERSON><PERSON>] All Weather Insulated Panels", "status": 5, "priority": 3, "updated_at": "2024-10-30T14:37:58Z"}, "617": {"ticket_number": 107247, "subject": "FW: 5046 Rona EDI Error/Warning  Vicwest invoice ******** UCP code rejection ", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:35:52Z"}, "618": {"ticket_number": 107246, "subject": "<PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:21:55Z"}, "619": {"ticket_number": 107245, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:24Z"}, "620": {"ticket_number": 107244, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:24Z"}, "621": {"ticket_number": 107243, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:23Z"}, "622": {"ticket_number": 107242, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: SAP ECC - Vicwest Access & Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:23Z"}, "623": {"ticket_number": 107240, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T03:03:15Z"}, "624": {"ticket_number": 107241, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Zoom - Office Phone", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:23Z"}, "625": {"ticket_number": 107239, "subject": "On-board <PERSON><PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville", "status": 2, "priority": 2, "updated_at": "2024-10-30T03:02:27Z"}, "626": {"ticket_number": 107238, "subject": "SAP Concur-Password Change Request", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:37:46Z"}, "627": {"ticket_number": 107237, "subject": "[<PERSON><PERSON>] All Weather Insulated Panels", "status": 5, "priority": 3, "updated_at": "2024-10-30T14:37:57Z"}, "628": {"ticket_number": 107236, "subject": "[<PERSON><PERSON>] All Weather Insulated Panels", "status": 5, "priority": 3, "updated_at": "2024-10-30T14:39:00Z"}, "629": {"ticket_number": 107235, "subject": "[<PERSON><PERSON>] All Weather Insulated Panels", "status": 5, "priority": 3, "updated_at": "2024-10-30T14:37:55Z"}, "630": {"ticket_number": 107234, "subject": "RE: KINGSPAN_SFTP - Deleting files from Ryder SFTP Server.", "status": 5, "priority": 3, "updated_at": "2024-10-28T16:55:42Z"}, "631": {"ticket_number": 107233, "subject": "delete all record", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:24:23Z"}, "632": {"ticket_number": 107232, "subject": "I’m unable to open any AutoCAD files.", "status": 5, "priority": 2, "updated_at": "2024-10-30T14:37:54Z"}, "633": {"ticket_number": 107231, "subject": "SAP Gui 8.0 Higher patch upgrade", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:16:47Z"}, "634": {"ticket_number": 107230, "subject": "<PERSON><PERSON>", "status": 3, "priority": 1, "updated_at": "2024-10-29T14:10:04Z"}, "635": {"ticket_number": 107229, "subject": "Please disable <PERSON>' email", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:39:50Z"}, "636": {"ticket_number": 107228, "subject": "Printer drivers", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:37:50Z"}, "637": {"ticket_number": 107227, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Rocket (ASG-RD old name) - Software License Renewal", "status": 2, "priority": 2, "updated_at": "2024-10-30T18:12:41Z"}, "638": {"ticket_number": 107225, "subject": "sap", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:00:56Z"}, "639": {"ticket_number": 107224, "subject": "Cell Phone", "status": 2, "priority": 1, "updated_at": "2024-10-29T20:19:33Z"}, "640": {"ticket_number": 107223, "subject": "TM Addition to AWIP C&I Team", "status": 5, "priority": 2, "updated_at": "2024-10-29T15:19:02Z"}, "641": {"ticket_number": 107222, "subject": "Vendor 17466", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:58:02Z"}, "642": {"ticket_number": 107221, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St<PERSON> Paul: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:23:38Z"}, "643": {"ticket_number": 107220, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St<PERSON> Paul: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:29:31Z"}, "644": {"ticket_number": 107219, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St<PERSON> Paul: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-30T17:44:33Z"}, "645": {"ticket_number": 107217, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St. Paul: XA / AS400 - Access and Permissions", "status": 3, "priority": 2, "updated_at": "2024-10-31T14:01:35Z"}, "646": {"ticket_number": 107216, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St<PERSON> <PERSON>: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T02:47:57Z"}, "647": {"ticket_number": 107218, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - St. Paul: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:46:07Z"}, "648": {"ticket_number": 107215, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T02:47:35Z"}, "649": {"ticket_number": 107214, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> shared \"Invoice From M2 Roofing\" with you", "status": 5, "priority": 3, "updated_at": "2024-10-28T18:50:47Z"}, "650": {"ticket_number": 107213, "subject": "Request for <PERSON> : Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-29T17:00:57Z"}, "651": {"ticket_number": 107212, "subject": "Request for Breanna May : Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-30T12:37:29Z"}, "652": {"ticket_number": 107211, "subject": "Printer Down", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:23:09Z"}, "653": {"ticket_number": 107210, "subject": "Quality Lab Printer not Working", "status": 5, "priority": 1, "updated_at": "2024-10-30T14:37:53Z"}, "654": {"ticket_number": 107209, "subject": "New mouse", "status": 5, "priority": 1, "updated_at": "2024-10-28T15:35:18Z"}, "655": {"ticket_number": 107208, "subject": "Password Reset", "status": 4, "priority": 1, "updated_at": "2024-10-31T17:46:29Z"}, "656": {"ticket_number": 107207, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:15:28Z"}, "657": {"ticket_number": 107206, "subject": "Access to Sales Force", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:37:49Z"}, "658": {"ticket_number": 107205, "subject": "Weekly Patch Management Review", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:34:27Z"}, "659": {"ticket_number": 107204, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-28T13:01:06Z"}, "660": {"ticket_number": 107203, "subject": "Printer issues", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:37:48Z"}, "661": {"ticket_number": 107202, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:34:08Z"}, "662": {"ticket_number": 107201, "subject": "<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T14:37:51Z"}, "663": {"ticket_number": 107200, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:42:07Z"}, "664": {"ticket_number": 107199, "subject": "Infor account locked out ", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:06:13Z"}, "665": {"ticket_number": 107198, "subject": "Disabled", "status": 5, "priority": 1, "updated_at": "2024-10-28T11:44:56Z"}, "666": {"ticket_number": 107197, "subject": "Re: SPC Access Request", "status": 4, "priority": 1, "updated_at": "2024-10-31T17:10:35Z"}, "667": {"ticket_number": 107196, "subject": "AS400 & Siwa", "status": 5, "priority": 1, "updated_at": "2024-10-28T11:24:10Z"}, "668": {"ticket_number": 107195, "subject": "EUS2-VBP-MEP: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 4.24 GB (Free Bytes S:) is below the error limit of 5 GB in Free Bytes S:", "status": 5, "priority": 2, "updated_at": "2024-10-30T04:37:30Z"}, "669": {"ticket_number": 107194, "subject": "kingspanamericas | CYDERES-1811493 | Change list of IPs for the KNA-KIP-Symplistech VPN group | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-31T14:38:29Z"}, "670": {"ticket_number": 107193, "subject": "[<PERSON><PERSON>] Urgent Assistance Needed", "status": 5, "priority": 3, "updated_at": "2024-10-30T13:37:47Z"}, "671": {"ticket_number": 107192, "subject": "EUS2-VBP-MHP: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-29T21:37:09Z"}, "672": {"ticket_number": 107191, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:41:19Z"}, "673": {"ticket_number": 107190, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-27T16:14:07Z"}, "674": {"ticket_number": 107189, "subject": "[Failed] BRI Daily Backup - KNABRI-FSNS1 NAS Users Files2 (2 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-30T12:37:28Z"}, "675": {"ticket_number": 107188, "subject": "KNA-AWP-Little Rock Boardroom: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-29T07:39:29Z"}, "676": {"ticket_number": 107187, "subject": "KNA-LittleRock-TrimShop-West: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-29T07:39:29Z"}, "677": {"ticket_number": 107186, "subject": "KNA-LittleRock-AP9-Maintenance Area: Ping (Ping): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-29T07:39:28Z"}, "678": {"ticket_number": 107184, "subject": "[Failed] DEL Daily Backup - KNADEL-FSNS1 NAS Users Files (4 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:37:46Z"}, "679": {"ticket_number": 107183, "subject": "KNA-KMX-Monterrey Sala Produccion: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-29T02:37:07Z"}, "680": {"ticket_number": 107182, "subject": "Backup y desactivación de correo electronico <PERSON> ", "status": 2, "priority": 1, "updated_at": "2024-10-28T20:13:18Z"}, "681": {"ticket_number": 107181, "subject": "Siesa - License Removal & Permits - | <EMAIL> : Request for <PERSON> by <PERSON>:", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:18:40Z"}, "682": {"ticket_number": 107180, "subject": "Reset MFA", "status": 5, "priority": 1, "updated_at": "2024-10-26T18:10:43Z"}, "683": {"ticket_number": 107179, "subject": "Request for <PERSON> : Email Feature Requests", "status": 5, "priority": 2, "updated_at": "2024-10-28T01:12:53Z"}, "684": {"ticket_number": 107178, "subject": "kingspanamericas | CYDERES-1827393 | Malicious IP space | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-31T14:38:28Z"}, "685": {"ticket_number": 107177, "subject": "[<PERSON><PERSON>] TEST #Quick action needed: Renew your Premium-#696698", "status": 5, "priority": 3, "updated_at": "2024-10-30T13:37:46Z"}, "686": {"ticket_number": 107176, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:41:41Z"}, "687": {"ticket_number": 107175, "subject": "KIP-DEL-PlantMaintOffice-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-28T15:37:41Z"}, "688": {"ticket_number": 107174, "subject": "KIP-DEL-IDF2-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-28T15:37:41Z"}, "689": {"ticket_number": 107173, "subject": "KIP-DEL-Line1CoolingRack-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-28T15:37:41Z"}, "690": {"ticket_number": 107172, "subject": "KNA-DEL-FAC-LINE1-DECOILER: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-28T15:37:39Z"}, "691": {"ticket_number": 107170, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-26T14:30:35Z"}, "692": {"ticket_number": 107169, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-26T14:30:02Z"}, "693": {"ticket_number": 107168, "subject": "[Failed] MOD Daily Backup - KNAMOD-FSNS1 NAS Users Files (4 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:37:44Z"}, "694": {"ticket_number": 107166, "subject": "EUS2-VBP-S4AD: Service: SAPVD1_01 (WMI Service): The Windows service you want to monitor is not running (Stopped). (code: PE011)", "status": 5, "priority": 2, "updated_at": "2024-10-28T04:39:13Z"}, "695": {"ticket_number": 107167, "subject": "EUS2-VBP-S4AD: Service: SAPVD1_00 (WMI Service): The Windows service you want to monitor is not running (Stopped). (code: PE011)", "status": 5, "priority": 2, "updated_at": "2024-10-28T04:39:14Z"}, "696": {"ticket_number": 107165, "subject": "kingspanamericas | CYDERES-1827276 | IdentityProtection - unfamiliarFeatures | S1 (High)", "status": 4, "priority": 2, "updated_at": "2024-10-30T17:35:49Z"}, "697": {"ticket_number": 107164, "subject": "Request for <PERSON> : Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-27T23:36:59Z"}, "698": {"ticket_number": 107163, "subject": "FW: Paymode-X Activation File -- Pre-Insulated Metal Technologies", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:59:53Z"}, "699": {"ticket_number": 107162, "subject": "Windows 11 Upgrade on KNA-L-8809", "status": 5, "priority": 1, "updated_at": "2024-10-27T21:39:50Z"}, "700": {"ticket_number": 107161, "subject": "Request for <PERSON> : MFA", "status": 5, "priority": 2, "updated_at": "2024-10-27T21:39:49Z"}, "701": {"ticket_number": 107160, "subject": "Laptop Keyboard not Workin", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:53:53Z"}, "702": {"ticket_number": 107159, "subject": "Request for Sinead Lalor : SharePoint - New Site", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:04:27Z"}, "703": {"ticket_number": 107158, "subject": "Paperwork showing both KG and LB in SAP", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:43:23Z"}, "704": {"ticket_number": 107157, "subject": "Blocked email?  ", "status": 5, "priority": 2, "updated_at": "2024-10-28T18:49:00Z"}, "705": {"ticket_number": 107156, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Baltimore: XA / AS400 - Access and Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:16:06Z"}, "706": {"ticket_number": 107155, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Baltimore: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T16:49:26Z"}, "707": {"ticket_number": 107154, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Baltimore", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:42:35Z"}, "708": {"ticket_number": 107153, "subject": "Re:[## 8484 ##] [#PRB-270] - RE: PDM Issues at the Baltimore site", "status": 5, "priority": 1, "updated_at": "2024-10-25T21:28:43Z"}, "709": {"ticket_number": 107150, "subject": "SAP Issue", "status": 5, "priority": 1, "updated_at": "2024-10-27T20:37:07Z"}, "710": {"ticket_number": 107149, "subject": "BAL - Shipping SA/IA issues (Extend Wi-Fi Coverage) ", "status": 2, "priority": 1, "updated_at": "2024-10-28T15:04:34Z"}, "711": {"ticket_number": 107148, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:07:29Z"}, "712": {"ticket_number": 107147, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:06:56Z"}, "713": {"ticket_number": 107146, "subject": "Request for <PERSON> by <PERSON><PERSON><PERSON><PERSON> : Stormwind Studios - License for Online IT Training", "status": 4, "priority": 2, "updated_at": "2024-10-29T18:52:25Z"}, "714": {"ticket_number": 107143, "subject": "Request for <PERSON> : Password Forgotten Account Password Requests | Requested by <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-27T19:37:22Z"}, "715": {"ticket_number": 107141, "subject": "Printer paper tray", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:34:26Z"}, "716": {"ticket_number": 107140, "subject": "Password reset needed ", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:18:44Z"}, "717": {"ticket_number": 107139, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-30T00:14:24Z"}, "718": {"ticket_number": 107138, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:43:25Z"}, "719": {"ticket_number": 107136, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: Desktop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:15:40Z"}, "720": {"ticket_number": 107137, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-30T00:14:22Z"}, "721": {"ticket_number": 107134, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T16:39:49Z"}, "722": {"ticket_number": 107135, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore: XA / AS400 - Access and Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:16:34Z"}, "723": {"ticket_number": 107133, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 7) - Full-Time Employee KNA - Baltimore", "status": 2, "priority": 2, "updated_at": "2024-10-30T04:13:05Z"}, "724": {"ticket_number": 107131, "subject": "Password reset", "status": 5, "priority": 1, "updated_at": "2024-10-27T19:37:21Z"}, "725": {"ticket_number": 107130, "subject": "Share Drive ", "status": 5, "priority": 1, "updated_at": "2024-10-27T19:37:21Z"}, "726": {"ticket_number": 107129, "subject": "Microphone not working", "status": 5, "priority": 1, "updated_at": "2024-10-27T19:37:20Z"}, "727": {"ticket_number": 107128, "subject": "Request for <PERSON><PERSON> Lalor : Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-25T21:30:54Z"}, "728": {"ticket_number": 107127, "subject": "SAP", "status": 2, "priority": 1, "updated_at": "2024-10-28T12:37:58Z"}, "729": {"ticket_number": 107126, "subject": "(Merged to  106261) Request for <PERSON> : Network Access - Allow Device Access to the Network", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:01:18Z"}, "730": {"ticket_number": 107125, "subject": "Supplier extension to Monterrey Plant (SAP)", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:02:00Z"}, "731": {"ticket_number": 107124, "subject": "CHANGE VENDOR - <PERSON><PERSON> (20840) ", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:10:46Z"}, "732": {"ticket_number": 107123, "subject": "#14840 computer ", "status": 5, "priority": 2, "updated_at": "2024-10-28T07:14:50Z"}, "733": {"ticket_number": 107122, "subject": "Caledon Shipping Schedule (Under KA-KIP / Plant Schedules)", "status": 5, "priority": 1, "updated_at": "2024-10-27T19:37:19Z"}, "734": {"ticket_number": 107121, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton: Salesforce CRM", "status": 2, "priority": 2, "updated_at": "2024-10-28T17:28:11Z"}, "735": {"ticket_number": 107120, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T13:20:15Z"}, "736": {"ticket_number": 107119, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:01:10Z"}, "737": {"ticket_number": 107117, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:36:35Z"}, "738": {"ticket_number": 107118, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:01:00Z"}, "739": {"ticket_number": 107116, "subject": "On-board <PERSON> (Tue, 2024, Nov 12) - Full-Time Employee VBP - Moncton", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:00:09Z"}, "740": {"ticket_number": 107115, "subject": "[<PERSON><PERSON>] Confirm Cell Details Charles", "status": 5, "priority": 3, "updated_at": "2024-10-27T17:37:10Z"}, "741": {"ticket_number": 107114, "subject": "FW: Acheson Water Readings link from old site", "status": 4, "priority": 1, "updated_at": "2024-10-30T21:48:38Z"}, "742": {"ticket_number": 107113, "subject": "Account unblocking", "status": 5, "priority": 1, "updated_at": "2024-10-27T17:37:08Z"}, "743": {"ticket_number": 107112, "subject": "NEW VENDOR ACCESS GAS SERVICES 1035", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:55:35Z"}, "744": {"ticket_number": 107111, "subject": "101634 Kent DB Flats ", "status": 5, "priority": 1, "updated_at": "2024-10-28T19:09:54Z"}, "745": {"ticket_number": 107110, "subject": "New vendor - <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-25T17:04:56Z"}, "746": {"ticket_number": 107108, "subject": "New Department Page", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:39:50Z"}, "747": {"ticket_number": 107107, "subject": "Jai -Screen", "status": 5, "priority": 1, "updated_at": "2024-10-25T19:35:32Z"}, "748": {"ticket_number": 107106, "subject": "Adobe", "status": 5, "priority": 1, "updated_at": "2024-10-27T20:37:05Z"}, "749": {"ticket_number": 107105, "subject": "FW: Vendor Block  Forklift Training Systems", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:44:47Z"}, "750": {"ticket_number": 107104, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Salesforce CRM", "status": 2, "priority": 2, "updated_at": "2024-10-28T15:43:14Z"}, "751": {"ticket_number": 107102, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:00:40Z"}, "752": {"ticket_number": 107101, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:08:17Z"}, "753": {"ticket_number": 107103, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Email - Mailbox Access Request for mailbox: <EMAIL>. Requested by System", "status": 5, "priority": 2, "updated_at": "2024-10-26T19:05:42Z"}, "754": {"ticket_number": 107098, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:00:35Z"}, "755": {"ticket_number": 107099, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:07:02Z"}, "756": {"ticket_number": 107100, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:07:32Z"}, "757": {"ticket_number": 107097, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: SAP ECC - Vicwest Access & Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-28T15:43:13Z"}, "758": {"ticket_number": 107095, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:05:25Z"}, "759": {"ticket_number": 107096, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee VBP - Victoriaville: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T19:03:52Z"}, "760": {"ticket_number": 107094, "subject": "Single ear headset", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:32:49Z"}, "761": {"ticket_number": 107093, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON>: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:06:50Z"}, "762": {"ticket_number": 107092, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON>: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:07:45Z"}, "763": {"ticket_number": 107091, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St. Paul: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-30T06:12:58Z"}, "764": {"ticket_number": 107090, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON>: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-30T06:12:58Z"}, "765": {"ticket_number": 107089, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON> Paul: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T11:42:48Z"}, "766": {"ticket_number": 107087, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St. Paul: XA / AS400 - Access and Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-28T17:51:25Z"}, "767": {"ticket_number": 107088, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON> Paul: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-25T15:44:18Z"}, "768": {"ticket_number": 107086, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - St<PERSON> Paul: Zoom - Office Phone", "status": 2, "priority": 2, "updated_at": "2024-10-30T06:12:57Z"}, "769": {"ticket_number": 107085, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON> <PERSON>: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T17:48:10Z"}, "770": {"ticket_number": 107084, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:10:33Z"}, "771": {"ticket_number": 107083, "subject": "Headset not connecting to computer", "status": 5, "priority": 1, "updated_at": "2024-10-27T16:39:05Z"}, "772": {"ticket_number": 107082, "subject": "RE: SF OD Issues ", "status": 5, "priority": 1, "updated_at": "2024-10-27T17:37:07Z"}, "773": {"ticket_number": 107081, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-27T16:39:05Z"}, "774": {"ticket_number": 107080, "subject": "FW: ", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:22:33Z"}, "775": {"ticket_number": 107079, "subject": "Request for <PERSON> : AD Network Account Reactivation", "status": 5, "priority": 2, "updated_at": "2024-10-25T16:17:31Z"}, "776": {"ticket_number": 107078, "subject": "[<PERSON><PERSON>] 4 Day PMP Certification Program", "status": 5, "priority": 3, "updated_at": "2024-10-27T19:37:19Z"}, "777": {"ticket_number": 107077, "subject": "Export MDM Records for Power BI", "status": 5, "priority": 1, "updated_at": "2024-10-28T23:35:03Z"}, "778": {"ticket_number": 107076, "subject": "Create Purchase Order Access", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:02:33Z"}, "779": {"ticket_number": 107075, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:02:15Z"}, "780": {"ticket_number": 107074, "subject": "FW: Inquiry -Project ******** Tri-County Mennonite Homes TCMH Nithview Comm LTC", "status": 5, "priority": 1, "updated_at": "2024-10-25T15:58:17Z"}, "781": {"ticket_number": 107073, "subject": "Request for Sinead Lalor : External Sharing - OneDrive / SharePoint", "status": 5, "priority": 2, "updated_at": "2024-10-30T19:54:28Z"}, "782": {"ticket_number": 107072, "subject": "Request for Visio app", "status": 5, "priority": 1, "updated_at": "2024-10-27T18:37:07Z"}, "783": {"ticket_number": 107071, "subject": "Shut down EUS2-VBP-SBX and EUS2-VBP-MHS.", "status": 5, "priority": 2, "updated_at": "2024-10-28T13:13:45Z"}, "784": {"ticket_number": 107070, "subject": "<PERSON><PERSON><PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-31T15:37:37Z"}, "785": {"ticket_number": 107069, "subject": "Update Revit 2022 to Revit 2025", "status": 5, "priority": 1, "updated_at": "2024-10-27T18:37:05Z"}, "786": {"ticket_number": 107068, "subject": "Re: Access - <PERSON><PERSON><PERSON><PERSON> ", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:05Z"}, "787": {"ticket_number": 107067, "subject": "Access to previous employee Edge", "status": 5, "priority": 1, "updated_at": "2024-10-28T21:57:24Z"}, "788": {"ticket_number": 107066, "subject": "[<PERSON><PERSON>] 941x review needed Laura", "status": 5, "priority": 3, "updated_at": "2024-10-27T16:39:04Z"}, "789": {"ticket_number": 107065, "subject": "Concur", "status": 5, "priority": 1, "updated_at": "2024-10-30T14:56:42Z"}, "790": {"ticket_number": 107063, "subject": "New User / Fontana Drive", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:20:21Z"}, "791": {"ticket_number": 107062, "subject": "Zoom Power Pack", "status": 5, "priority": 1, "updated_at": "2024-10-27T20:37:04Z"}, "792": {"ticket_number": 107061, "subject": "Transferring Calls on Ploycom CCX400", "status": 5, "priority": 1, "updated_at": "2024-10-27T14:39:23Z"}, "793": {"ticket_number": 107060, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:14:34Z"}, "794": {"ticket_number": 107059, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:41:55Z"}, "795": {"ticket_number": 107058, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:42:11Z"}, "796": {"ticket_number": 107055, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-25T14:15:14Z"}, "797": {"ticket_number": 107057, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:42:30Z"}, "798": {"ticket_number": 107056, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:45:34Z"}, "799": {"ticket_number": 107053, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:14:53Z"}, "800": {"ticket_number": 107054, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Workstation Setup", "status": 5, "priority": 2, "updated_at": "2024-10-25T14:33:10Z"}, "801": {"ticket_number": 107052, "subject": "On-board <PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:39:52Z"}, "802": {"ticket_number": 107051, "subject": "Zoom SSO access block", "status": 4, "priority": 1, "updated_at": "2024-10-31T16:32:24Z"}, "803": {"ticket_number": 107050, "subject": "Need permission to download files from google drive for REVIT course", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:00:51Z"}, "804": {"ticket_number": 107049, "subject": "NEW VENDOR - EVERON, LLC ", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:22:25Z"}, "805": {"ticket_number": 107048, "subject": "Presentation Cable in training room 1", "status": 2, "priority": 1, "updated_at": "2024-10-29T16:55:51Z"}, "806": {"ticket_number": 107046, "subject": "Request for Lawrence Warner : AutoDesk -AutoDesk Construction Cloud", "status": 5, "priority": 2, "updated_at": "2024-10-30T19:39:17Z"}, "807": {"ticket_number": 107045, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Disable Preservation Hold on KIP Deland SPO | Manage Data Retention Policy (Office 365) for a SharePoint Site (Enable managing Preservation Hold Library) | KA-KIP - NA DRAFTING DEPARTMENT", "status": 5, "priority": 2, "updated_at": "2024-10-28T13:48:37Z"}, "808": {"ticket_number": 107044, "subject": "Plaease delete", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:56:29Z"}, "809": {"ticket_number": 107043, "subject": "Request for <PERSON> : Adobe - Acrobat DC Pro", "status": 4, "priority": 2, "updated_at": "2024-10-29T19:23:36Z"}, "810": {"ticket_number": 107042, "subject": "LOCKED SAPB1", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:52:03Z"}, "811": {"ticket_number": 107041, "subject": "RE: 5046 Rona EDI Error/Warning  Vicwest invoices  ******** and invoice ********  UCP REJECTION ", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:51:58Z"}, "812": {"ticket_number": 107040, "subject": "Reset MFA", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:47:39Z"}, "813": {"ticket_number": 107039, "subject": "Please unlock <PERSON> network account.", "status": 5, "priority": 1, "updated_at": "2024-10-27T13:37:08Z"}, "814": {"ticket_number": 107038, "subject": "USUARIO BLOQUEADO", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:44:32Z"}, "815": {"ticket_number": 107037, "subject": "Missing folder", "status": 5, "priority": 2, "updated_at": "2024-10-29T17:48:54Z"}, "816": {"ticket_number": 107036, "subject": "low priority: Mimecast continues flagging sender I have permitted numerous times", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:37:31Z"}, "817": {"ticket_number": 107035, "subject": "Unable to get link [<PERSON><PERSON>] Statement of account invoice for Payment", "status": 5, "priority": 3, "updated_at": "2024-10-25T16:46:54Z"}, "818": {"ticket_number": 107033, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:07:50Z"}, "819": {"ticket_number": 107031, "subject": "PROD R ERRORS", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:13:29Z"}, "820": {"ticket_number": 107030, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-25T13:42:36Z"}, "821": {"ticket_number": 107029, "subject": "Fw: Printer", "status": 2, "priority": 2, "updated_at": "2024-10-29T03:57:57Z"}, "822": {"ticket_number": 107028, "subject": "Fw: WH 6 Printer Issue", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:50:51Z"}, "823": {"ticket_number": 107027, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:30:42Z"}, "824": {"ticket_number": 107026, "subject": "[<PERSON><PERSON>] ", "status": 5, "priority": 3, "updated_at": "2024-10-27T12:37:12Z"}, "825": {"ticket_number": 107025, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-26T14:43:07Z"}, "826": {"ticket_number": 107024, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:42:56Z"}, "827": {"ticket_number": 107023, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:42:56Z"}, "828": {"ticket_number": 107022, "subject": "Critical SharePoint Storage Alert", "status": 2, "priority": 3, "updated_at": "2024-10-29T20:44:18Z"}, "829": {"ticket_number": 107021, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:07:57Z"}, "830": {"ticket_number": 107020, "subject": "<PERSON>ptop <PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:24:28Z"}, "831": {"ticket_number": 107019, "subject": "Request for <PERSON><PERSON> : Mobile - MAC", "status": 5, "priority": 2, "updated_at": "2024-10-26T22:37:10Z"}, "832": {"ticket_number": 107018, "subject": "Request for <PERSON> by <PERSON>: Employee Change Board - User Data", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:37:45Z"}, "833": {"ticket_number": 107017, "subject": "TAF-STP-IDF4-UPS: APC UPS Health (SNMP Custom Advanced): 4 m 51 s  (Run Time Remaining) is below the error limit of 5 m  in Run Time Remaining. Battery Low, Shutdown eminent", "status": 5, "priority": 1, "updated_at": "2024-10-29T21:37:09Z"}, "834": {"ticket_number": 107016, "subject": "MII Login Credentials Needed", "status": 5, "priority": 1, "updated_at": "2024-10-29T22:39:05Z"}, "835": {"ticket_number": 107015, "subject": "Request for <PERSON> : Network Shared Files and Folders - Permissions and Access", "status": 4, "priority": 2, "updated_at": "2024-10-30T23:07:45Z"}, "836": {"ticket_number": 107014, "subject": "kingspanamericas | CYDERES-1825386 | Microsoft Defender XDR - User compromised through session cookie hijack | S1 (High) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-27T17:37:07Z"}, "837": {"ticket_number": 107013, "subject": "Request for <PERSON> : <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:39:10Z"}, "838": {"ticket_number": 107012, "subject": "Request for <PERSON> Pierce : <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:38:27Z"}, "839": {"ticket_number": 107011, "subject": "Password Reset", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:37:08Z"}, "840": {"ticket_number": 107010, "subject": "Access to VPN", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:11:11Z"}, "841": {"ticket_number": 107009, "subject": "Fw: <PERSON><PERSON><PERSON><PERSON> Bolsitas Halloween", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:14:44Z"}, "842": {"ticket_number": 107008, "subject": "Access to R drive", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:39:49Z"}, "843": {"ticket_number": 107007, "subject": "PRINTER CONFIGURATION", "status": 5, "priority": 1, "updated_at": "2024-10-26T23:37:06Z"}, "844": {"ticket_number": 107006, "subject": "as 400", "status": 3, "priority": 1, "updated_at": "2024-10-25T14:53:14Z"}, "845": {"ticket_number": 107005, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-29T23:13:20Z"}, "846": {"ticket_number": 107004, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:23:34Z"}, "847": {"ticket_number": 107003, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:39:28Z"}, "848": {"ticket_number": 107002, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:39:44Z"}, "849": {"ticket_number": 107001, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:40:08Z"}, "850": {"ticket_number": 106998, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: TAF - Tate Access Floors - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:08:23Z"}, "851": {"ticket_number": 107000, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:40:24Z"}, "852": {"ticket_number": 106999, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:41:06Z"}, "853": {"ticket_number": 106997, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - Jessup: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T01:45:01Z"}, "854": {"ticket_number": 106996, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee TAF - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T12:38:46Z"}, "855": {"ticket_number": 106995, "subject": "RE: New Starter - <PERSON><PERSON><PERSON><PERSON><PERSON> - 28th of October ", "status": 2, "priority": 1, "updated_at": "2024-10-25T12:52:54Z"}, "856": {"ticket_number": 106994, "subject": "kingspanamericas | CYDERES-1825310 | IdentityProtection - unfamiliarFeatures | S1 (High)", "status": 4, "priority": 2, "updated_at": "2024-10-30T17:34:23Z"}, "857": {"ticket_number": 106993, "subject": "Customer service Printer ", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:49:18Z"}, "858": {"ticket_number": 106992, "subject": "RE: TRIALS NOT RELEASED", "status": 5, "priority": 1, "updated_at": "2024-10-27T13:37:07Z"}, "859": {"ticket_number": 106991, "subject": "RE: Weird Netlink ", "status": 2, "priority": 1, "updated_at": "2024-10-25T16:58:08Z"}, "860": {"ticket_number": 106990, "subject": "Cell Phone", "status": 5, "priority": 1, "updated_at": "2024-10-31T17:08:22Z"}, "861": {"ticket_number": 106989, "subject": "Printer not choosing the proper tray", "status": 5, "priority": 1, "updated_at": "2024-10-25T20:22:08Z"}, "862": {"ticket_number": 106988, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>: SAP ECC - Vicwest Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-25T12:54:56Z"}, "863": {"ticket_number": 106986, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-26T04:05:32Z"}, "864": {"ticket_number": 106987, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>: Request for <PERSON><PERSON> by System:", "status": 5, "priority": 2, "updated_at": "2024-10-26T21:39:00Z"}, "865": {"ticket_number": 106985, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-30T12:40:23Z"}, "866": {"ticket_number": 106984, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-31T16:45:53Z"}, "867": {"ticket_number": 106983, "subject": "2-10132 part to be extended to Caledon", "status": 5, "priority": 1, "updated_at": "2024-10-24T21:37:38Z"}, "868": {"ticket_number": 106982, "subject": "FW: Inquiry -Project ******** Tri-County Mennonite Homes TCMH Nithview Comm LTC", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:04Z"}, "869": {"ticket_number": 106981, "subject": "Tanium Patching -  VBP-L-2489", "status": 5, "priority": 1, "updated_at": "2024-10-31T13:37:44Z"}, "870": {"ticket_number": 106980, "subject": "(Merged to  85503) Tanium Patching -  KNAMON-ADMINPC1", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:51:44Z"}, "871": {"ticket_number": 106979, "subject": "Tanium Patching -  KNAMAT-ADMIN2", "status": 5, "priority": 1, "updated_at": "2024-10-28T21:07:14Z"}, "872": {"ticket_number": 106978, "subject": "Tanium Patching -  KNA-L-2534", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:28:25Z"}, "873": {"ticket_number": 106977, "subject": "SAP - Item Pricing Condition Key Combination Sequence Restructure - Configuration Change ", "status": 2, "priority": 1, "updated_at": "2024-10-25T16:28:13Z"}, "874": {"ticket_number": 106976, "subject": "Tanium Patching -  KNA-L-14399", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:13:29Z"}, "875": {"ticket_number": 106975, "subject": "Solidworks License activation", "status": 5, "priority": 1, "updated_at": "2024-10-24T21:12:38Z"}, "876": {"ticket_number": 106973, "subject": "Tanium Scanning Issues -  KNA-L-14105", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:38:28Z"}, "877": {"ticket_number": 106972, "subject": "Tanium Patching -  KNA-L-14035", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:13:29Z"}, "878": {"ticket_number": 106971, "subject": "Tanium Patching -  KNA-L-13272", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:13:29Z"}, "879": {"ticket_number": 106970, "subject": "Tanium Patching -  KNA-L-13119", "status": 4, "priority": 1, "updated_at": "2024-10-29T23:00:53Z"}, "880": {"ticket_number": 106969, "subject": "Tanium Patching -  KNA-L-12923", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:37:45Z"}, "881": {"ticket_number": 106968, "subject": "Tanium Patching -  KNA-L-12221", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:38:27Z"}, "882": {"ticket_number": 106967, "subject": "Tanium Patching -  KNA-D-7718", "status": 4, "priority": 1, "updated_at": "2024-10-29T22:58:38Z"}, "883": {"ticket_number": 106966, "subject": "For disposal when it is returned KNA-M-8491", "status": 2, "priority": 1, "updated_at": "2024-10-25T20:13:28Z"}, "884": {"ticket_number": 106965, "subject": "Tanium Patching -  KNA-D-7671", "status": 5, "priority": 1, "updated_at": "2024-10-28T23:45:34Z"}, "885": {"ticket_number": 106964, "subject": "Email Signature phone numbers", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:22:17Z"}, "886": {"ticket_number": 106963, "subject": "Monitor is not working properly", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:57:20Z"}, "887": {"ticket_number": 106962, "subject": "Vicwest remote management credentials iPhone", "status": 5, "priority": 1, "updated_at": "2024-10-24T22:07:54Z"}, "888": {"ticket_number": 106961, "subject": "RE: West Co-op Pricing changes: Effective Start of Business November 4th ", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:42:04Z"}, "889": {"ticket_number": 106960, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:43:19Z"}, "890": {"ticket_number": 106959, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:43:19Z"}, "891": {"ticket_number": 106956, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:43:18Z"}, "892": {"ticket_number": 106955, "subject": "Not able to hear or speak on headphones during Zoom calls", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:27:48Z"}, "893": {"ticket_number": 106954, "subject": "New report request", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:19:26Z"}, "894": {"ticket_number": 106953, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> shared \"Invoice From M2 Roofing\" with you", "status": 5, "priority": 3, "updated_at": "2024-10-25T16:53:43Z"}, "895": {"ticket_number": 106952, "subject": "Time clock in building 3", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:18:43Z"}, "896": {"ticket_number": 106951, "subject": "Folder missing in outlook", "status": 2, "priority": 1, "updated_at": "2024-10-25T15:28:06Z"}, "897": {"ticket_number": 106950, "subject": "(Merged to  106704) FW: All Weather Insulated Panels Shift Report", "status": 5, "priority": 1, "updated_at": "2024-10-24T22:49:56Z"}, "898": {"ticket_number": 106949, "subject": "Printer <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-26T20:37:15Z"}, "899": {"ticket_number": 106948, "subject": "Sharepoint Access", "status": 4, "priority": 1, "updated_at": "2024-10-30T15:51:56Z"}, "900": {"ticket_number": 106946, "subject": "AWP-EAS-IDF1-UPS, does it exist and is it online?", "status": 2, "priority": 1, "updated_at": "2024-10-25T18:58:07Z"}, "901": {"ticket_number": 106944, "subject": "Receiving not allowed", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:45:10Z"}, "902": {"ticket_number": 106943, "subject": "PPV - other review", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:19:28Z"}, "903": {"ticket_number": 106942, "subject": "Qualys Level 3 Internal Vulnerability - 10.119.10.53 - 48168 ~ Remote Management Service Accepting Unencrypted Credentials Detected (Telnet)", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:13:16Z"}, "904": {"ticket_number": 106940, "subject": "Excel 64 bits", "status": 5, "priority": 1, "updated_at": "2024-10-26T19:37:06Z"}, "905": {"ticket_number": 106939, "subject": "Qualys Level 3 Internal Vulnerabilities - 48168 ~ Remote Management Service Accepting Unencrypted Credentials Detected (Telnet)", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:37:06Z"}, "906": {"ticket_number": 106938, "subject": "Qualys Level 3 Internal Vulnerabilities - 48168 ~ Remote Management Service Accepting Unencrypted Credentials Detected (Telnet)", "status": 5, "priority": 1, "updated_at": "2024-10-29T18:15:28Z"}, "907": {"ticket_number": 106937, "subject": "Missing Files in OD 3-1307", "status": 5, "priority": 1, "updated_at": "2024-10-26T19:37:06Z"}, "908": {"ticket_number": 106936, "subject": "Issues with the drafting portfolio file", "status": 5, "priority": 1, "updated_at": "2024-10-27T17:37:06Z"}, "909": {"ticket_number": 106935, "subject": "FW: BLMGROUP: New software release ready to download", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:37:27Z"}, "910": {"ticket_number": 106934, "subject": "FW: NES Vendor Application", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:10:18Z"}, "911": {"ticket_number": 106933, "subject": "FW: <PERSON><PERSON> Insulated Panel information request", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:08:04Z"}, "912": {"ticket_number": 106932, "subject": "FW: UNDELIVERABLE EMAIL", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:39:26Z"}, "913": {"ticket_number": 106931, "subject": "Edit Access", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:37:31Z"}, "914": {"ticket_number": 106930, "subject": "Deland Stock 2024-10-24 - Docking stations", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:35:49Z"}, "915": {"ticket_number": 106929, "subject": "Qualys Level 4 Internal Vulnerability- EUS2-KNA-DNP1 (10.248.177.25) - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:12:15Z"}, "916": {"ticket_number": 106928, "subject": "Qualys Level 4 Internal Vulnerability - 10.177.106.8 - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:11:28Z"}, "917": {"ticket_number": 106927, "subject": "Qualys Level 4 Internal Vulnerability - 192.168.149.12", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:28:21Z"}, "918": {"ticket_number": 106926, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:14:00Z"}, "919": {"ticket_number": 106925, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:16:42Z"}, "920": {"ticket_number": 106924, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:16:54Z"}, "921": {"ticket_number": 106922, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: KIP - Kingspan Insulated Panels - Concur New Account", "status": 2, "priority": 2, "updated_at": "2024-10-25T17:28:12Z"}, "922": {"ticket_number": 106923, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-29T14:56:09Z"}, "923": {"ticket_number": 106921, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T02:01:22Z"}, "924": {"ticket_number": 106920, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:17:41Z"}, "925": {"ticket_number": 106919, "subject": "1-99253 Line 210", "status": 5, "priority": 1, "updated_at": "2024-10-26T18:37:14Z"}, "926": {"ticket_number": 106917, "subject": "Merge Projects", "status": 5, "priority": 1, "updated_at": "2024-10-26T17:37:11Z"}, "927": {"ticket_number": 106916, "subject": "McAfee Virus Alert", "status": 5, "priority": 2, "updated_at": "2024-10-26T17:37:10Z"}, "928": {"ticket_number": 106914, "subject": "RE: <PERSON>WIP", "status": 5, "priority": 1, "updated_at": "2024-10-31T00:39:24Z"}, "929": {"ticket_number": 106913, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Salesforce CRM", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:20:25Z"}, "930": {"ticket_number": 106912, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:00:29Z"}, "931": {"ticket_number": 106911, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:01:42Z"}, "932": {"ticket_number": 106910, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:03:48Z"}, "933": {"ticket_number": 106909, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:31:37Z"}, "934": {"ticket_number": 106908, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:39:04Z"}, "935": {"ticket_number": 106905, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T12:37:03Z"}, "936": {"ticket_number": 106907, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: SAP B1 - Morin Access & Permissions", "status": 2, "priority": 2, "updated_at": "2024-10-25T16:13:09Z"}, "937": {"ticket_number": 106906, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana: Zoom - Office Phone", "status": 2, "priority": 2, "updated_at": "2024-10-31T18:10:39Z"}, "938": {"ticket_number": 106904, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Fontana", "status": 2, "priority": 2, "updated_at": "2024-10-25T12:36:31Z"}, "939": {"ticket_number": 106903, "subject": "Request for <PERSON> : SAP S/4 HANA - KIP/AWIP Access & Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:53:46Z"}, "940": {"ticket_number": 106902, "subject": "Slow computer", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:13:17Z"}, "941": {"ticket_number": 106901, "subject": "On-board Santiago Winter (Mon, 2023, Jan 2) - Contract KNA - Montevideo: Email - Mailbox Access Request for mailbox: <EMAIL>. Requested by System", "status": 2, "priority": 2, "updated_at": "2024-10-29T00:12:57Z"}, "942": {"ticket_number": 106900, "subject": "On-board Santiago Winter (Mon, 2023, Jan 2) - Contract KNA - Montevideo: AD Network Account Creation - <PERSON> Perez", "status": 2, "priority": 2, "updated_at": "2024-10-29T00:12:57Z"}, "943": {"ticket_number": 106899, "subject": "On-board Santiago Winter (Mon, 2023, Jan 2) - Contract KNA - Montevideo", "status": 2, "priority": 2, "updated_at": "2024-10-29T04:13:01Z"}, "944": {"ticket_number": 106898, "subject": "Modesto CS printer paper trays not set up", "status": 5, "priority": 2, "updated_at": "2024-10-31T15:23:35Z"}, "945": {"ticket_number": 106897, "subject": "[<PERSON><PERSON>] <PERSON>, Opening for an Advisory Board Seat at a Top Tier University [invitation only]", "status": 5, "priority": 3, "updated_at": "2024-10-26T17:37:09Z"}, "946": {"ticket_number": 106896, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-28T18:16:39Z"}, "947": {"ticket_number": 106895, "subject": "KNA-Mattoon-AP21: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T20:37:15Z"}, "948": {"ticket_number": 106894, "subject": "KNA-Mattoon-AP18: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:24Z"}, "949": {"ticket_number": 106893, "subject": "Zoom password", "status": 5, "priority": 1, "updated_at": "2024-10-26T17:37:08Z"}, "950": {"ticket_number": 106892, "subject": "Twinmotion / Epic Games Login Blocked", "status": 4, "priority": 2, "updated_at": "2024-10-30T15:42:44Z"}, "951": {"ticket_number": 106891, "subject": "kingspanamericas | CYDERES-1824484 | IdentityProtection - unfamiliarFeatures | S1 (High)", "status": 5, "priority": 2, "updated_at": "2024-10-26T15:37:24Z"}, "952": {"ticket_number": 106890, "subject": "VBP-SAS-MDF-UPS2: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:24Z"}, "953": {"ticket_number": 106889, "subject": "VBP-SAS-MDF-UPS1: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:23Z"}, "954": {"ticket_number": 106888, "subject": "Saskatoon-<PERSON>elo: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:23Z"}, "955": {"ticket_number": 106887, "subject": "KNA_VW_Saskatoon-A (10.150.200.195): <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:23Z"}, "956": {"ticket_number": 106886, "subject": "VBP-D-13226: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T15:37:22Z"}, "957": {"ticket_number": 106885, "subject": "Backup/BRMS Daily Report", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:04:23Z"}, "958": {"ticket_number": 106884, "subject": "FW: Paymode-X Vendor Deactivation --  for Kingspan Insulated Panels, Inc.", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:30:47Z"}, "959": {"ticket_number": 106883, "subject": "Fw: <PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-28T12:44:48Z"}, "960": {"ticket_number": 106882, "subject": "[<PERSON><PERSON>] TEST #Quick action needed: Renew your Premium-#718658", "status": 5, "priority": 3, "updated_at": "2024-10-26T15:37:22Z"}, "961": {"ticket_number": 106881, "subject": "New Voicemail from LEWISVILLE TX (469) 830-9319 to <PERSON><PERSON><PERSON> (Ext. 94000) on Thu Oct 24, 2024 10:36", "status": 5, "priority": 1, "updated_at": "2024-10-26T18:37:13Z"}, "962": {"ticket_number": 106880, "subject": "[<PERSON><PERSON>ert] RE: Important: Autodesk SSO Activation at 13:00 CET Today", "status": 5, "priority": 3, "updated_at": "2024-10-30T19:40:24Z"}, "963": {"ticket_number": 106879, "subject": "[<PERSON><PERSON>] - !ncoming rnessage", "status": 5, "priority": 3, "updated_at": "2024-10-24T20:39:20Z"}, "964": {"ticket_number": 106878, "subject": "[<PERSON><PERSON>] - !ncoming rnessage [1]", "status": 5, "priority": 3, "updated_at": "2024-10-26T19:37:05Z"}, "965": {"ticket_number": 106876, "subject": "[<PERSON><PERSON>] - !ncoming rnessage", "status": 5, "priority": 3, "updated_at": "2024-10-26T17:37:07Z"}, "966": {"ticket_number": 106875, "subject": "FW: Badland Batteries - Fargo, ND", "status": 5, "priority": 1, "updated_at": "2024-10-26T23:37:06Z"}, "967": {"ticket_number": 106874, "subject": "[<PERSON><PERSON>] - !ncoming rnessage [1]", "status": 5, "priority": 3, "updated_at": "2024-10-26T14:39:35Z"}, "968": {"ticket_number": 106873, "subject": "[<PERSON><PERSON>] - !ncoming rnessage [1]", "status": 5, "priority": 3, "updated_at": "2024-10-24T16:30:08Z"}, "969": {"ticket_number": 106872, "subject": "Bahrns Toyota Forklift", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:27:41Z"}, "970": {"ticket_number": 106871, "subject": "One Drive Problems", "status": 5, "priority": 1, "updated_at": "2024-10-27T14:39:23Z"}, "971": {"ticket_number": 106870, "subject": "Please delete", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:32:28Z"}, "972": {"ticket_number": 106869, "subject": "Fw: Improvements of Sales Order Rejection Report", "status": 2, "priority": 1, "updated_at": "2024-10-28T17:31:54Z"}, "973": {"ticket_number": 106868, "subject": "New Shared Email Inbox:  <EMAIL>", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:58:13Z"}, "974": {"ticket_number": 106867, "subject": "[<PERSON><PERSON>] Short Assignment!", "status": 5, "priority": 3, "updated_at": "2024-10-26T14:39:34Z"}, "975": {"ticket_number": 106866, "subject": "Global protect connection issue", "status": 2, "priority": 1, "updated_at": "2024-10-30T11:41:38Z"}, "976": {"ticket_number": 106865, "subject": "<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T22:37:10Z"}, "977": {"ticket_number": 106864, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-26T15:37:22Z"}, "978": {"ticket_number": 106863, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-28T23:49:48Z"}, "979": {"ticket_number": 106862, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T18:44:39Z"}, "980": {"ticket_number": 106861, "subject": "Request to correct spelling on color description - Egyptian White", "status": 5, "priority": 1, "updated_at": "2024-10-24T15:32:13Z"}, "981": {"ticket_number": 106860, "subject": "[<PERSON><PERSON>] Your iCloud storage is almost full.", "status": 5, "priority": 3, "updated_at": "2024-10-25T16:57:53Z"}, "982": {"ticket_number": 106859, "subject": "OOO <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T18:17:38Z"}, "983": {"ticket_number": 106858, "subject": "(Merged to  106503) Request for <PERSON> : Bluebeam Complete - BlueBeam Annual Subscription Plans", "status": 5, "priority": 2, "updated_at": "2024-10-24T18:16:55Z"}, "984": {"ticket_number": 106857, "subject": "FW: Zoom password reset confirmation", "status": 5, "priority": 1, "updated_at": "2024-10-26T13:38:02Z"}, "985": {"ticket_number": 106856, "subject": "Mouse/Keyboard issue", "status": 5, "priority": 2, "updated_at": "2024-10-24T13:24:11Z"}, "986": {"ticket_number": 106855, "subject": "Working remotely, unable to access any resources or VPN", "status": 5, "priority": 2, "updated_at": "2024-10-26T12:37:51Z"}, "987": {"ticket_number": 106854, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:08:41Z"}, "988": {"ticket_number": 106852, "subject": "[<PERSON><PERSON>] TEST #Quick action needed: Renew your Premium-#910705", "status": 5, "priority": 3, "updated_at": "2024-10-26T13:38:02Z"}, "989": {"ticket_number": 106850, "subject": "AS400 sessions won't open/authenticate", "status": 5, "priority": 1, "updated_at": "2024-10-26T12:37:46Z"}, "990": {"ticket_number": 106849, "subject": "Request for <PERSON><PERSON> by <PERSON><PERSON>: Employee Change Board - User Data", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:21:59Z"}, "991": {"ticket_number": 106848, "subject": "BU-02 Daily Backup Status Review", "status": 5, "priority": 3, "updated_at": "2024-10-24T13:00:52Z"}, "992": {"ticket_number": 106847, "subject": "Salesforce Onedrive ", "status": 5, "priority": 1, "updated_at": "2024-10-26T15:37:22Z"}, "993": {"ticket_number": 106846, "subject": "[Failed] DEL Daily Backup - KNADEL-FSNS1 NAS Users Files (4 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-26T12:37:45Z"}, "994": {"ticket_number": 106845, "subject": "KA IT - AZURE LOG ANALYTICS - DAILY BACKUP IaaSVM (RDL) - PDF", "status": 5, "priority": 1, "updated_at": "2024-10-24T12:31:46Z"}, "995": {"ticket_number": 106844, "subject": "Monitors not working through dock", "status": 5, "priority": 1, "updated_at": "2024-10-26T12:37:45Z"}, "996": {"ticket_number": 106843, "subject": "[<PERSON><PERSON>] TEST #Quick action needed: Renew your Premium-#438102", "status": 5, "priority": 3, "updated_at": "2024-10-26T15:37:21Z"}, "997": {"ticket_number": 106842, "subject": "[<PERSON><PERSON>] TEST #Quick action needed: Renew your Premium-#361424", "status": 5, "priority": 3, "updated_at": "2024-10-28T11:22:24Z"}, "998": {"ticket_number": 106841, "subject": "<PERSON>acker computer turning off by itself and feeling too hot ", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:59:31Z"}, "999": {"ticket_number": 106840, "subject": "KNA-KIP-Deland Optimo Boardroom: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T06:39:12Z"}, "1000": {"ticket_number": 106839, "subject": "KNA-Deland-DET: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T06:39:12Z"}, "1001": {"ticket_number": 106838, "subject": "KNA-KIP-Deland Hercules Room: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T06:39:12Z"}, "1002": {"ticket_number": 106837, "subject": "KNADEL-MDT01: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-26T06:39:11Z"}, "1003": {"ticket_number": 106836, "subject": "XA Journal Audit", "status": 5, "priority": 1, "updated_at": "2024-10-24T14:47:06Z"}, "1004": {"ticket_number": 106834, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-24T04:21:05Z"}, "1005": {"ticket_number": 106835, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-24T04:31:04Z"}, "1006": {"ticket_number": 106833, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T04:32:04Z"}, "1007": {"ticket_number": 106832, "subject": "[Failed] DEL Daily Backup - KNADEL-FSNS1 NAS Users Files (4 VMs) 3 failed", "status": 5, "priority": 2, "updated_at": "2024-10-26T12:37:44Z"}, "1008": {"ticket_number": 106831, "subject": "Document Processing Error: Peavey Industries LP 2024-10-24 02:17:54", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:56:58Z"}, "1009": {"ticket_number": 106830, "subject": "EUS2-KNA-ADMIN1: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 4.98 GB (Free Bytes C:) is below the error limit of 5 GB in Free Bytes C:", "status": 5, "priority": 2, "updated_at": "2024-10-26T21:38:59Z"}, "1010": {"ticket_number": 106829, "subject": "Global Protect Blocked User", "status": 5, "priority": 1, "updated_at": "2024-10-26T12:37:43Z"}, "1011": {"ticket_number": 106828, "subject": "Printer installation", "status": 5, "priority": 1, "updated_at": "2024-10-26T17:37:05Z"}, "1012": {"ticket_number": 106827, "subject": "Seeing this message when signing  in", "status": 5, "priority": 1, "updated_at": "2024-10-24T00:54:38Z"}, "1013": {"ticket_number": 106826, "subject": "Locked out of my account", "status": 5, "priority": 2, "updated_at": "2024-10-24T00:52:56Z"}, "1014": {"ticket_number": 106825, "subject": "EUS2-KNA-SAPBOR: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 1 % (Free Space C:) is below the error limit of 2 % in Free Space C:", "status": 5, "priority": 2, "updated_at": "2024-10-27T14:39:22Z"}, "1015": {"ticket_number": 106824, "subject": "SAP: Product Variation Addition", "status": 5, "priority": 1, "updated_at": "2024-10-28T13:11:11Z"}, "1016": {"ticket_number": 106823, "subject": "Twinmotion update", "status": 5, "priority": 1, "updated_at": "2024-10-24T00:06:11Z"}, "1017": {"ticket_number": 106821, "subject": "On-board <PERSON> (Wed, 2024, Oct 23) - Full-Time Employee VBP - Victoriaville", "status": 5, "priority": 2, "updated_at": "2024-10-25T23:37:05Z"}, "1018": {"ticket_number": 106822, "subject": "On-board <PERSON> (Wed, 2024, Oct 23) - Full-Time Employee VBP - Victoriaville: AD Network Account Creation - <PERSON><PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T23:37:06Z"}, "1019": {"ticket_number": 106819, "subject": "New Vendor:  Elesa USA Corporation", "status": 5, "priority": 1, "updated_at": "2024-10-24T19:00:29Z"}, "1020": {"ticket_number": 106818, "subject": "New Vendor:  Arkansas Door Service", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:57:20Z"}, "1021": {"ticket_number": 106816, "subject": "Vendor 20639 updated", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:44:08Z"}, "1022": {"ticket_number": 106815, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> - Kingspan Insulated Panels North America", "status": 5, "priority": 3, "updated_at": "2024-10-25T22:38:59Z"}, "1023": {"ticket_number": 106814, "subject": "BlueBeam access", "status": 4, "priority": 1, "updated_at": "2024-10-30T20:55:35Z"}, "1024": {"ticket_number": 106813, "subject": "Reset MFA", "status": 5, "priority": 1, "updated_at": "2024-10-24T02:10:00Z"}, "1025": {"ticket_number": 106812, "subject": "New Vendor:  I-80 Forklift ", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:47:29Z"}, "1026": {"ticket_number": 106811, "subject": "Desbloqueo de cuenta", "status": 5, "priority": 1, "updated_at": "2024-10-24T02:13:34Z"}, "1027": {"ticket_number": 106810, "subject": "sap delivery date ", "status": 2, "priority": 1, "updated_at": "2024-10-31T12:43:18Z"}, "1028": {"ticket_number": 106809, "subject": "CAD Files are not printing in black and white", "status": 5, "priority": 1, "updated_at": "2024-10-27T17:37:05Z"}, "1029": {"ticket_number": 106808, "subject": "Problema con Impresoras arrendadas", "status": 5, "priority": 1, "updated_at": "2024-10-26T22:37:10Z"}, "1030": {"ticket_number": 106806, "subject": "[<PERSON><PERSON>] Sundance Builders LLC Change Orders #47543", "status": 5, "priority": 3, "updated_at": "2024-10-25T17:00:04Z"}, "1031": {"ticket_number": 106805, "subject": "[Failed] BAL Daily Backup - KNAJESAX-FSNS1 NAS Users Files (2 VMs) 2 failed", "status": 5, "priority": 2, "updated_at": "2024-10-26T12:37:43Z"}, "1032": {"ticket_number": 106804, "subject": "Adobe Full Access", "status": 5, "priority": 1, "updated_at": "2024-10-26T18:37:09Z"}, "1033": {"ticket_number": 106800, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-25T20:37:27Z"}, "1034": {"ticket_number": 106799, "subject": "[Failed] BAL Daily Backup - DOMAIN CONTROLLER (0 VMs)", "status": 5, "priority": 2, "updated_at": "2024-10-26T11:37:01Z"}, "1035": {"ticket_number": 106797, "subject": "Adobe Sign In", "status": 5, "priority": 1, "updated_at": "2024-10-24T15:21:05Z"}, "1036": {"ticket_number": 106798, "subject": "Requesting High-Cloud Access", "status": 5, "priority": 2, "updated_at": "2024-10-30T22:37:06Z"}, "1037": {"ticket_number": 106795, "subject": "Desactivar corre<PERSON> ", "status": 5, "priority": 1, "updated_at": "2024-10-24T04:21:47Z"}, "1038": {"ticket_number": 106794, "subject": "4500188534  - 3000585427", "status": 5, "priority": 1, "updated_at": "2024-10-26T17:37:04Z"}, "1039": {"ticket_number": 106793, "subject": "KNA-D-7217 Colombus desktop replacement", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:43:23Z"}, "1040": {"ticket_number": 106792, "subject": "<PERSON> Error", "status": 5, "priority": 2, "updated_at": "2024-10-24T14:16:34Z"}, "1041": {"ticket_number": 106791, "subject": "New Vendor for 1035 <PERSON><PERSON>s ", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:34:14Z"}, "1042": {"ticket_number": 106790, "subject": "WIFI Boosters ", "status": 2, "priority": 1, "updated_at": "2024-10-30T19:28:13Z"}, "1043": {"ticket_number": 106789, "subject": "[<PERSON><PERSON>] !ncoming V-Message", "status": 5, "priority": 3, "updated_at": "2024-10-25T17:03:33Z"}, "1044": {"ticket_number": 106788, "subject": "KNAASH-BUVE1: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-25T19:39:12Z"}, "1045": {"ticket_number": 106787, "subject": "[Failed] BAL Daily Backup - KNAJESAX-FSNS1 NAS Files Azure (2 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-26T11:37:00Z"}, "1046": {"ticket_number": 106785, "subject": "For disposal KNA-D-7739", "status": 2, "priority": 1, "updated_at": "2024-10-30T19:13:23Z"}, "1047": {"ticket_number": 106783, "subject": "Phone is not connected to internet", "status": 5, "priority": 2, "updated_at": "2024-10-25T19:39:12Z"}, "1048": {"ticket_number": 106782, "subject": "hp update ", "status": 5, "priority": 1, "updated_at": "2024-10-26T12:37:42Z"}, "1049": {"ticket_number": 106780, "subject": "Connection to Morin Corporation", "status": 5, "priority": 2, "updated_at": "2024-10-25T19:39:12Z"}, "1050": {"ticket_number": 106779, "subject": "Request for <PERSON><PERSON> by <PERSON><PERSON>: Employee Change Board - User Data", "status": 5, "priority": 2, "updated_at": "2024-10-25T19:39:11Z"}, "1051": {"ticket_number": 106778, "subject": "[<PERSON><PERSON>] !ncoming V-Message", "status": 5, "priority": 3, "updated_at": "2024-10-24T16:32:35Z"}, "1052": {"ticket_number": 106777, "subject": "Customer <PERSON><PERSON> is blocked", "status": 5, "priority": 1, "updated_at": "2024-10-25T19:45:56Z"}, "1053": {"ticket_number": 106776, "subject": "RE: RISA Section", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:17:02Z"}, "1054": {"ticket_number": 106774, "subject": "Rebate Credits", "status": 2, "priority": 1, "updated_at": "2024-10-31T10:28:01Z"}, "1055": {"ticket_number": 106773, "subject": "Qualys Level 3 - 38657 ~ Birthday attacks against TLS ciphers with 64bit block size vulnerability (Sweet32)", "status": 5, "priority": 2, "updated_at": "2024-10-29T16:26:58Z"}, "1056": {"ticket_number": 106772, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-25T20:37:27Z"}, "1057": {"ticket_number": 106771, "subject": "********** part to be extended to Caledon", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:50:28Z"}, "1058": {"ticket_number": 106770, "subject": "Call forwarding from company phone", "status": 4, "priority": 1, "updated_at": "2024-10-31T17:04:20Z"}, "1059": {"ticket_number": 106769, "subject": "On-board <PERSON><PERSON><PERSON> (Wed, 2024, Oct 23) - Full-Time Employee KNA - Monterrey: AD Network Account Creation - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T03:12:04Z"}, "1060": {"ticket_number": 106768, "subject": "On-board <PERSON><PERSON><PERSON> (Wed, 2024, Oct 23) - Full-Time Employee KNA - Monterrey", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:16:04Z"}, "1061": {"ticket_number": 106767, "subject": "kingspanamericas | CYDERES-1811493 | Change list of IPs for the KNA-KIP-Symplistech VPN group | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-26T12:37:41Z"}, "1062": {"ticket_number": 106766, "subject": "Quadcore Price update", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:37:36Z"}, "1063": {"ticket_number": 106765, "subject": "Jabra Software Update", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:30:39Z"}, "1064": {"ticket_number": 106764, "subject": "[<PERSON><PERSON>] Remittance Advice   [#egvqmwexadaspp#]", "status": 5, "priority": 3, "updated_at": "2024-10-28T18:14:50Z"}, "1065": {"ticket_number": 106763, "subject": "Error Everytime AutoCAD is Launched", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:37:32Z"}, "1066": {"ticket_number": 106760, "subject": "VB Application ", "status": 5, "priority": 1, "updated_at": "2024-10-25T00:45:21Z"}, "1067": {"ticket_number": 106758, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-25T19:29:33Z"}, "1068": {"ticket_number": 106757, "subject": "[<PERSON><PERSON>] UPDATE MY PAY INFO", "status": 5, "priority": 3, "updated_at": "2024-10-24T22:38:14Z"}, "1069": {"ticket_number": 106756, "subject": "POS and Invoices from AS400 to PDF copy (email)", "status": 2, "priority": 1, "updated_at": "2024-10-29T19:29:03Z"}, "1070": {"ticket_number": 106752, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-25T18:37:28Z"}, "1071": {"ticket_number": 106751, "subject": "New Password required", "status": 5, "priority": 2, "updated_at": "2024-10-25T17:37:51Z"}, "1072": {"ticket_number": 106750, "subject": "Wiki links transfer to FreshService", "status": 2, "priority": 1, "updated_at": "2024-10-31T08:13:43Z"}, "1073": {"ticket_number": 106749, "subject": "INFOR XA - Mass Change to suspend items", "status": 2, "priority": 1, "updated_at": "2024-10-31T08:13:42Z"}, "1074": {"ticket_number": 106748, "subject": "Add printer ", "status": 5, "priority": 1, "updated_at": "2024-10-30T23:38:58Z"}, "1075": {"ticket_number": 106747, "subject": "FW: Access for Part Number Creation - AS400/Net-Link", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:10:17Z"}, "1076": {"ticket_number": 106744, "subject": "Deland Stock - Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-28T15:37:34Z"}, "1077": {"ticket_number": 106743, "subject": "Request for <PERSON> : Laptop - Engineering/Drafting restock", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:13:14Z"}, "1078": {"ticket_number": 106742, "subject": "Request for <PERSON> : Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:43Z"}, "1079": {"ticket_number": 106735, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Request for <PERSON> by System:", "status": 5, "priority": 2, "updated_at": "2024-10-28T23:55:52Z"}, "1080": {"ticket_number": 106736, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: SAP S/4 HANA - Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-28T23:56:28Z"}, "1081": {"ticket_number": 106734, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-29T23:48:47Z"}, "1082": {"ticket_number": 106733, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-28T23:58:05Z"}, "1083": {"ticket_number": 106732, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T23:48:46Z"}, "1084": {"ticket_number": 106729, "subject": "Off-Board <PERSON><PERSON><PERSON> / - Requested By <PERSON>y Espino: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:33:58Z"}, "1085": {"ticket_number": 106728, "subject": "Off-Board <PERSON><PERSON><PERSON> / - Requested By <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:34:03Z"}, "1086": {"ticket_number": 106727, "subject": "Export MDM Records for Power BI", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:18:23Z"}, "1087": {"ticket_number": 106724, "subject": "Fix Password not required <PERSON><PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-24T02:11:25Z"}, "1088": {"ticket_number": 106723, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-25T15:37:32Z"}, "1089": {"ticket_number": 106721, "subject": "Blocked Dropbox Message / Document Retrieval Request", "status": 5, "priority": 1, "updated_at": "2024-10-27T21:39:48Z"}, "1090": {"ticket_number": 106720, "subject": "Twinmotion / Epic Games Website Blocked", "status": 5, "priority": 2, "updated_at": "2024-10-27T19:37:14Z"}, "1091": {"ticket_number": 106719, "subject": "problems opening general word documents/pdf after update on laptop", "status": 5, "priority": 1, "updated_at": "2024-10-25T23:37:05Z"}, "1092": {"ticket_number": 106717, "subject": "Issues with Solidworks", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:23:16Z"}, "1093": {"ticket_number": 106714, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-25T14:46:41Z"}, "1094": {"ticket_number": 106712, "subject": "KNA-KMX-Monterrey Sala Produccion: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-25T14:39:34Z"}, "1095": {"ticket_number": 106711, "subject": "KNA-Monterrey_WAP3_ENG_Office: <PERSON> (Ping): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-25T14:39:33Z"}, "1096": {"ticket_number": 106710, "subject": "KMX-MON-IDF1-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-25T14:39:31Z"}, "1097": {"ticket_number": 106708, "subject": "Unable connect VPN after restart", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:37:49Z"}, "1098": {"ticket_number": 106705, "subject": "[<PERSON><PERSON>] <PERSON><PERSON><PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-25T13:37:47Z"}, "1099": {"ticket_number": 106704, "subject": "FW: All Weather Insulated Panels Shift Report", "status": 4, "priority": 1, "updated_at": "2024-10-30T22:06:11Z"}, "1100": {"ticket_number": 106702, "subject": "Request for <PERSON> : Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:58:34Z"}, "1101": {"ticket_number": 106701, "subject": "RE: H:Drive Access", "status": 5, "priority": 1, "updated_at": "2024-10-28T22:48:12Z"}, "1102": {"ticket_number": 106700, "subject": "SIG UnBlock KA IT - S3 Amazon AWS", "status": 4, "priority": 1, "updated_at": "2024-10-29T22:09:14Z"}, "1103": {"ticket_number": 106699, "subject": "Missing access to Stratford email", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:42:32Z"}, "1104": {"ticket_number": 106698, "subject": "Veeam Daily Backup Status Review", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:09:37Z"}, "1105": {"ticket_number": 106694, "subject": "Request for <PERSON>: Read-only access to Azure reservation and cost analysis", "status": 5, "priority": 2, "updated_at": "2024-10-28T17:06:19Z"}, "1106": {"ticket_number": 106690, "subject": "[Failed] BRI Daily Backup - KNABRI-FSNS1 NAS Users Files2 (2 VMs) 1 failed", "status": 5, "priority": 2, "updated_at": "2024-10-27T18:37:04Z"}, "1107": {"ticket_number": 106689, "subject": "Critical SharePoint Storage Alert", "status": 2, "priority": 1, "updated_at": "2024-10-31T03:47:33Z"}, "1108": {"ticket_number": 106688, "subject": "Critical SharePoint Storage Alert", "status": 5, "priority": 1, "updated_at": "2024-10-25T20:37:26Z"}, "1109": {"ticket_number": 106685, "subject": "FW: Hot check fits", "status": 5, "priority": 1, "updated_at": "2024-10-26T16:39:22Z"}, "1110": {"ticket_number": 106684, "subject": "Bluebeam prompting update when launching.", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:27:33Z"}, "1111": {"ticket_number": 106683, "subject": "Customer's email getting held in Mimecast", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:38:01Z"}, "1112": {"ticket_number": 106682, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> sent a message", "status": 5, "priority": 3, "updated_at": "2024-10-25T00:37:10Z"}, "1113": {"ticket_number": 106681, "subject": "Install google chrome", "status": 5, "priority": 1, "updated_at": "2024-10-25T00:37:08Z"}, "1114": {"ticket_number": 106678, "subject": "Lansweeper- Printer  NPIF88E52 - asset tag, rename, and Ka Location", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:32:08Z"}, "1115": {"ticket_number": 106677, "subject": "Lansweeper- Printer TEC B-EX6T - asset tag, rename, and Ka Location", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:21:13Z"}, "1116": {"ticket_number": 106675, "subject": "SN015344414057  -unidentifiable device scanned", "status": 2, "priority": 1, "updated_at": "2024-10-29T23:28:13Z"}, "1117": {"ticket_number": 106674, "subject": "0F01ERC23063FB -unidentifiable device scanned", "status": 2, "priority": 1, "updated_at": "2024-10-29T23:13:09Z"}, "1118": {"ticket_number": 106673, "subject": "0f004v623363fb  -unidentifiable device scanned", "status": 2, "priority": 1, "updated_at": "2024-10-29T23:13:09Z"}, "1119": {"ticket_number": 106671, "subject": "KNA-L-2480 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-27T12:37:12Z"}, "1120": {"ticket_number": 106670, "subject": "kna-l-7835 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T22:32:50Z"}, "1121": {"ticket_number": 106669, "subject": "kna-l-8786 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-29T22:58:09Z"}, "1122": {"ticket_number": 106668, "subject": "kna-l-12229 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:39:16Z"}, "1123": {"ticket_number": 106666, "subject": "kna-l-7846 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:43:40Z"}, "1124": {"ticket_number": 106665, "subject": "kna-l-7992 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:03:49Z"}, "1125": {"ticket_number": 106664, "subject": "kna-l-7991 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:09:22Z"}, "1126": {"ticket_number": 106660, "subject": "FW: Qhs folder", "status": 5, "priority": 1, "updated_at": "2024-10-25T20:37:25Z"}, "1127": {"ticket_number": 106659, "subject": "DELIVERY ADDRESS NOT SAVING - 1-91764 ROSEWOOD CALISTOGA - ROOF", "status": 5, "priority": 1, "updated_at": "2024-10-25T12:37:21Z"}, "1128": {"ticket_number": 106658, "subject": "AWIP - ADD COLOR ABBREVIATION TO ACCESSORIES  (1-91764 ROSEWOOD CALISTOGA)", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:19:20Z"}, "1129": {"ticket_number": 106656, "subject": "Request for <PERSON><PERSON><PERSON> : Display - Cable/Adapter Modesto restock 10/22/2024", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:36:45Z"}, "1130": {"ticket_number": 106654, "subject": "Train on using phish alert button", "status": 5, "priority": 1, "updated_at": "2024-10-29T13:59:35Z"}, "1131": {"ticket_number": 106652, "subject": "[<PERSON><PERSON> Alert] RE: Upper Macungie Community Center You Tube link", "status": 5, "priority": 3, "updated_at": "2024-10-25T15:37:29Z"}, "1132": {"ticket_number": 106651, "subject": "Po creation for invoices", "status": 5, "priority": 1, "updated_at": "2024-10-24T21:37:31Z"}, "1133": {"ticket_number": 106648, "subject": "Help with Delivery Note- 80245123 ", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:31Z"}, "1134": {"ticket_number": 106647, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee TAF - Jessup: Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:43:49Z"}, "1135": {"ticket_number": 106642, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee TAF - Jessup: Laptop - Engineering/Drafting", "status": 5, "priority": 2, "updated_at": "2024-10-25T12:50:37Z"}, "1136": {"ticket_number": 106638, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee TAF - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:43:48Z"}, "1137": {"ticket_number": 106637, "subject": "Large file from client", "status": 5, "priority": 2, "updated_at": "2024-10-25T12:37:20Z"}, "1138": {"ticket_number": 106634, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-24T20:37:31Z"}, "1139": {"ticket_number": 106632, "subject": "Request for Sunita Bishnoi : AD User Group - Distribution & Security | |", "status": 5, "priority": 2, "updated_at": "2024-10-24T20:37:29Z"}, "1140": {"ticket_number": 106630, "subject": "10/22/2024 Rest<PERSON> Jessup", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:01:27Z"}, "1141": {"ticket_number": 106628, "subject": "Vicwest, Burlington restock 10/22/24: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-29T19:13:14Z"}, "1142": {"ticket_number": 106626, "subject": "Request for <PERSON>ef Boudhina : Cancel the Kensington Velocloud service with CBTS | Site Circuit REMOVE - Internet Access", "status": 2, "priority": 2, "updated_at": "2024-10-29T18:58:13Z"}, "1143": {"ticket_number": 106625, "subject": "Password Reset ", "status": 5, "priority": 2, "updated_at": "2024-10-28T19:57:02Z"}, "1144": {"ticket_number": 106624, "subject": "Request for <PERSON><PERSON> Boudhina : order Velocloud edges for Tate ASP BUL 3400 landmark Burlington | Site Circuit ADD - Internet Access", "status": 2, "priority": 2, "updated_at": "2024-10-29T18:58:12Z"}, "1145": {"ticket_number": 106623, "subject": "Graphics Driver update Needed", "status": 5, "priority": 1, "updated_at": "2024-10-26T23:37:05Z"}, "1146": {"ticket_number": 106621, "subject": "Request for <PERSON><PERSON> : Relocate the Meriplex circuit in Kensington Struss to Metal | Site Circuit CHANGE - Internet Access", "status": 2, "priority": 2, "updated_at": "2024-10-29T18:43:06Z"}, "1147": {"ticket_number": 106620, "subject": "Request for <PERSON><PERSON> Boudhina : Cancel / Terminate the BT internet circuit in Kensington | Site Circuit REMOVE - Internet Access", "status": 2, "priority": 2, "updated_at": "2024-10-29T18:43:06Z"}, "1148": {"ticket_number": 106619, "subject": "Salesforce Upload", "status": 2, "priority": 1, "updated_at": "2024-10-30T10:28:22Z"}, "1149": {"ticket_number": 106617, "subject": "Request for <PERSON><PERSON> by <PERSON><PERSON>: Employee Change Board - User Data", "status": 5, "priority": 2, "updated_at": "2024-10-28T14:50:34Z"}, "1150": {"ticket_number": 106615, "subject": "[<PERSON><PERSON>] <PERSON><PERSON> ", "status": 5, "priority": 3, "updated_at": "2024-10-25T20:37:25Z"}, "1151": {"ticket_number": 106614, "subject": "[<PERSON><PERSON>] (!ncoming M essage)", "status": 5, "priority": 3, "updated_at": "2024-10-28T18:08:54Z"}, "1152": {"ticket_number": 106612, "subject": "Options for EDI transition of AWIP documents to Vicwest", "status": 2, "priority": 1, "updated_at": "2024-10-30T09:43:05Z"}, "1153": {"ticket_number": 106611, "subject": "Zoom SSO problem", "status": 5, "priority": 1, "updated_at": "2024-10-25T15:37:27Z"}, "1154": {"ticket_number": 106609, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:37:43Z"}, "1155": {"ticket_number": 106607, "subject": "FW: Error Message \"You are not authorized to use transaction  /cockpit/IV_Sched", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:58:17Z"}, "1156": {"ticket_number": 106606, "subject": "Printer ", "status": 5, "priority": 1, "updated_at": "2024-10-27T13:37:07Z"}, "1157": {"ticket_number": 106605, "subject": "[<PERSON><PERSON>ert] OFFICE PROJECT DETAILS", "status": 5, "priority": 3, "updated_at": "2024-10-24T17:37:42Z"}, "1158": {"ticket_number": 106603, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Monterrey: Standard Laptop", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:37:40Z"}, "1159": {"ticket_number": 106598, "subject": "[<PERSON><PERSON>] Please update the bank information.", "status": 5, "priority": 3, "updated_at": "2024-10-24T16:40:27Z"}, "1160": {"ticket_number": 106594, "subject": "Remote Access Setup", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:38:28Z"}, "1161": {"ticket_number": 106593, "subject": "[<PERSON><PERSON>] Form I-9 Reminder - Section 1 Due 10/21/2024", "status": 5, "priority": 3, "updated_at": "2024-10-24T16:38:17Z"}, "1162": {"ticket_number": 106592, "subject": "No Microphone Option in Teams", "status": 5, "priority": 1, "updated_at": "2024-10-24T19:37:36Z"}, "1163": {"ticket_number": 106590, "subject": "Download", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:37:23Z"}, "1164": {"ticket_number": 106589, "subject": "Cannot download from Sketchup Warehouse", "status": 5, "priority": 2, "updated_at": "2024-10-24T19:37:35Z"}, "1165": {"ticket_number": 106588, "subject": "Request for <PERSON><PERSON> : Email Access", "status": 4, "priority": 2, "updated_at": "2024-10-29T20:57:44Z"}, "1166": {"ticket_number": 106586, "subject": "Request for <PERSON> : Allow List - Allow a Domain - CBA practice test website", "status": 5, "priority": 2, "updated_at": "2024-10-24T19:38:14Z"}, "1167": {"ticket_number": 106585, "subject": "RE: Vacaville Release for Production <PERSON><PERSON> - Mirko Trim Repair - SOA#1000100802", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:37:38Z"}, "1168": {"ticket_number": 106584, "subject": "KNA-M-12390 Lansweeper - MAC", "status": 5, "priority": 2, "updated_at": "2024-10-24T15:37:27Z"}, "1169": {"ticket_number": 106583, "subject": "Gargled audio in Zoom", "status": 5, "priority": 2, "updated_at": "2024-10-24T15:37:26Z"}, "1170": {"ticket_number": 106580, "subject": "Dry Dock 4 Project - Open File?", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:40:26Z"}, "1171": {"ticket_number": 106579, "subject": "LSAgent scans not working since October 15, 2024", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:59:53Z"}, "1172": {"ticket_number": 106577, "subject": "SEC-UAC-05: AD - Review and Disable AD Active & HR Terminated Accounts", "status": 5, "priority": 4, "updated_at": "2024-10-26T18:53:12Z"}, "1173": {"ticket_number": 106576, "subject": "SIWA - create PO issue!", "status": 2, "priority": 1, "updated_at": "2024-10-30T06:28:52Z"}, "1174": {"ticket_number": 106574, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON> Paul: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-24T19:56:18Z"}, "1175": {"ticket_number": 106575, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON> Paul: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:05:59Z"}, "1176": {"ticket_number": 106572, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-24T20:48:10Z"}, "1177": {"ticket_number": 106570, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:04Z"}, "1178": {"ticket_number": 106571, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - <PERSON><PERSON>: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:05:33Z"}, "1179": {"ticket_number": 106573, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON>: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:06:22Z"}, "1180": {"ticket_number": 106564, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:03Z"}, "1181": {"ticket_number": 106566, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:03Z"}, "1182": {"ticket_number": 106568, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:04Z"}, "1183": {"ticket_number": 106567, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON>: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:06:46Z"}, "1184": {"ticket_number": 106565, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON> Paul: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:08:48Z"}, "1185": {"ticket_number": 106558, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St<PERSON> Paul: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T03:41:53Z"}, "1186": {"ticket_number": 106560, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: KIP - Kings<PERSON> Insulated Panels - Concur New Account", "status": 2, "priority": 2, "updated_at": "2024-10-24T19:21:42Z"}, "1187": {"ticket_number": 106561, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - St. Paul: XA / AS400 - Access and Permissions", "status": 3, "priority": 2, "updated_at": "2024-10-25T11:58:27Z"}, "1188": {"ticket_number": 106556, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-24T03:41:28Z"}, "1189": {"ticket_number": 106554, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg", "status": 2, "priority": 2, "updated_at": "2024-10-24T04:00:56Z"}, "1190": {"ticket_number": 106555, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T04:01:30Z"}, "1191": {"ticket_number": 106557, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - East Stroudsburg: Workstation Setup", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:47:21Z"}, "1192": {"ticket_number": 106551, "subject": "request for flatstock and trim out of Monterrey", "status": 2, "priority": 1, "updated_at": "2024-10-30T05:58:09Z"}, "1193": {"ticket_number": 106549, "subject": "Request for <PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-24T14:38:22Z"}, "1194": {"ticket_number": 106548, "subject": "Request for <PERSON><PERSON> : Cloud Shared File Access - Document Retrieval/Access", "status": 5, "priority": 2, "updated_at": "2024-10-24T14:38:21Z"}, "1195": {"ticket_number": 106547, "subject": "[<PERSON><PERSON>] Mark, Please Review Your Scheduled Project Details", "status": 5, "priority": 3, "updated_at": "2024-10-24T19:37:33Z"}, "1196": {"ticket_number": 106544, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-24T14:38:18Z"}, "1197": {"ticket_number": 106542, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: AD Network Account Termination", "status": 5, "priority": 2, "updated_at": "2024-10-25T23:37:05Z"}, "1198": {"ticket_number": 106541, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-29T21:21:29Z"}, "1199": {"ticket_number": 106540, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-29T13:14:45Z"}, "1200": {"ticket_number": 106539, "subject": "Locked out of Windows", "status": 5, "priority": 2, "updated_at": "2024-10-24T14:38:17Z"}, "1201": {"ticket_number": 106536, "subject": "FW: Duplicate Rejection Automation", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:58:28Z"}, "1202": {"ticket_number": 106535, "subject": "iPad Setup", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:30:43Z"}, "1203": {"ticket_number": 106534, "subject": "Driveworks Group Change", "status": 2, "priority": 1, "updated_at": "2024-10-29T12:58:06Z"}, "1204": {"ticket_number": 106533, "subject": "transit folder", "status": 5, "priority": 1, "updated_at": "2024-10-24T19:37:32Z"}, "1205": {"ticket_number": 106532, "subject": "[<PERSON><PERSON>] <PERSON>", "status": 5, "priority": 3, "updated_at": "2024-10-24T18:37:26Z"}, "1206": {"ticket_number": 106531, "subject": "[<PERSON><PERSON>] TEST #424125 - Quick action needed: Renew your Premium", "status": 5, "priority": 3, "updated_at": "2024-10-24T14:38:16Z"}, "1207": {"ticket_number": 106528, "subject": "Request for <PERSON> : Non-Standard Application - No License Required", "status": 5, "priority": 2, "updated_at": "2024-10-24T12:37:44Z"}, "1208": {"ticket_number": 106527, "subject": "My laptop will not connect to any monitors", "status": 5, "priority": 2, "updated_at": "2024-10-24T14:38:14Z"}, "1209": {"ticket_number": 106524, "subject": "Power BI Access to BMSM001 - BOMs Cross-Referenceing SMOI Inventory", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:47:36Z"}, "1210": {"ticket_number": 106523, "subject": "New Shipping Condition Needed in SAP", "status": 2, "priority": 1, "updated_at": "2024-10-30T03:58:03Z"}, "1211": {"ticket_number": 106519, "subject": "Internet down -  Kensington - Urgent ", "status": 5, "priority": 3, "updated_at": "2024-10-29T20:15:21Z"}, "1212": {"ticket_number": 106518, "subject": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-24T15:37:25Z"}, "1213": {"ticket_number": 106517, "subject": "kingspanamericas | CYDERES-1808510 | Kingspan - KNA - Automatic Generation of Test Alerts in CrowdStrike | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-24T12:37:43Z"}, "1214": {"ticket_number": 106516, "subject": "KNAASH-ITTL1: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 4.95 GB (Free Bytes F:) is below the error limit of 5 GB in Free Bytes F:", "status": 5, "priority": 2, "updated_at": "2024-10-26T21:38:59Z"}, "1215": {"ticket_number": 106515, "subject": "KNA_Mattoon_A: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-24T05:39:31Z"}, "1216": {"ticket_number": 106514, "subject": "KNA_Mattoon_B: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 5, "priority": 4, "updated_at": "2024-10-24T05:39:28Z"}, "1217": {"ticket_number": 106511, "subject": "Document Processing Error: Peavey Industries LP 2024-10-22 01:07:06", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:57:12Z"}, "1218": {"ticket_number": 106510, "subject": "Request for <PERSON> : Non-Standard Application - No License Required", "status": 5, "priority": 2, "updated_at": "2024-10-24T00:39:30Z"}, "1219": {"ticket_number": 106509, "subject": "Bluebeam Update Needed", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:26:38Z"}, "1220": {"ticket_number": 106508, "subject": "Access to Network Share/Installation of CFS", "status": 5, "priority": 1, "updated_at": "2024-10-24T00:39:30Z"}, "1221": {"ticket_number": 106503, "subject": "Request for <PERSON> : Bluebeam Complete - BlueBeam Annual Subscription Plans", "status": 5, "priority": 2, "updated_at": "2024-10-26T13:38:01Z"}, "1222": {"ticket_number": 106502, "subject": "Request for <PERSON> : <PERSON> - Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-28T15:27:26Z"}, "1223": {"ticket_number": 106501, "subject": "Request for <PERSON> : <PERSON> - Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-24T19:39:37Z"}, "1224": {"ticket_number": 106500, "subject": "Request for <PERSON> : <PERSON> - Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-28T15:22:45Z"}, "1225": {"ticket_number": 106498, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Fontana - User Access Controls (UAC) Review for System/Application - User Access Controls (UAC) Review for System/Application", "status": 5, "priority": 2, "updated_at": "2024-10-24T19:37:31Z"}, "1226": {"ticket_number": 106495, "subject": "ACCESO ", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:08:30Z"}, "1227": {"ticket_number": 106492, "subject": "SharePoint Training", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:18:11Z"}, "1228": {"ticket_number": 106490, "subject": "[<PERSON><PERSON>] Warranty Request - Fineline Steel", "status": 5, "priority": 3, "updated_at": "2024-10-25T20:37:23Z"}, "1229": {"ticket_number": 106489, "subject": "Request for <PERSON> : Update Department for all admin accounts", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:39:12Z"}, "1230": {"ticket_number": 106478, "subject": "Account blocked", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:08:58Z"}, "1231": {"ticket_number": 106474, "subject": " Project Plan 3 | Microsoft - Project & Planner Subscription", "status": 5, "priority": 2, "updated_at": "2024-10-25T15:05:15Z"}, "1232": {"ticket_number": 106469, "subject": "Install Bluebeam", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:37:45Z"}, "1233": {"ticket_number": 106466, "subject": "Concur not launching ", "status": 5, "priority": 1, "updated_at": "2024-10-24T15:37:23Z"}, "1234": {"ticket_number": 106465, "subject": "OneDrive error in Salesforce", "status": 5, "priority": 1, "updated_at": "2024-10-27T19:37:10Z"}, "1235": {"ticket_number": 106464, "subject": "RE: Job# 1-01311 Hornings Market Walls is ready to release", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:37:25Z"}, "1236": {"ticket_number": 106457, "subject": "[<PERSON><PERSON>] BRT Consulting Ltd.23749-24", "status": 5, "priority": 3, "updated_at": "2024-10-24T13:37:23Z"}, "1237": {"ticket_number": 106445, "subject": "Charger for Laptop", "status": 5, "priority": 1, "updated_at": "2024-10-26T22:37:10Z"}, "1238": {"ticket_number": 106440, "subject": "kingspanamericas | CYDERES-1811493 | Change list of IPs for the KNA-KIP-Symplistech VPN group | S3 (Low) - Status Changed to 'Waiting on Customer'", "status": 5, "priority": 2, "updated_at": "2024-10-25T17:37:47Z"}, "1239": {"ticket_number": 106436, "subject": "RE: Daniel Drive Access", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:34:31Z"}, "1240": {"ticket_number": 106434, "subject": "Fw: 3.15 HANA Password Policy - min. length 12", "status": 2, "priority": 1, "updated_at": "2024-10-29T07:43:09Z"}, "1241": {"ticket_number": 106432, "subject": "Renew the reservation for EUS2-KNA-HP1", "status": 2, "priority": 2, "updated_at": "2024-10-28T15:58:18Z"}, "1242": {"ticket_number": 106429, "subject": "RE: Wrong width assigned for KSSS 153055 / 97877 --  SAP chose wrong width steel for a 36\" wide skin.", "status": 2, "priority": 1, "updated_at": "2024-10-29T07:28:11Z"}, "1243": {"ticket_number": 106427, "subject": "I cannot download files from Teams", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:48:27Z"}, "1244": {"ticket_number": 106415, "subject": "New Route to be created - P410HD ", "status": 2, "priority": 1, "updated_at": "2024-10-29T06:58:03Z"}, "1245": {"ticket_number": 106413, "subject": "[<PERSON><PERSON>] TEST #507772 - Quick action needed: Renew your Premium", "status": 5, "priority": 3, "updated_at": "2024-10-24T14:46:23Z"}, "1246": {"ticket_number": 106404, "subject": "Laptop won't work with any dock", "status": 5, "priority": 2, "updated_at": "2024-10-29T16:09:30Z"}, "1247": {"ticket_number": 106389, "subject": "New ticket logged Call Reference - T20241021.0892 Re: No access to KNA resources from the internet without MFA", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:45:42Z"}, "1248": {"ticket_number": 106377, "subject": "KNA-D-7668 Evidence that CS is installed on endpoint. ", "status": 2, "priority": 2, "updated_at": "2024-10-28T13:13:18Z"}, "1249": {"ticket_number": 106373, "subject": "[<PERSON><PERSON>] CCN# 8249MANTOR09143", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:39:29Z"}, "1250": {"ticket_number": 106372, "subject": "Computer login", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:40:22Z"}, "1251": {"ticket_number": 106366, "subject": "Weekly Patch Management Review", "status": 5, "priority": 1, "updated_at": "2024-10-24T19:17:04Z"}, "1252": {"ticket_number": 106365, "subject": "Company Phone - <PERSON> ", "status": 2, "priority": 1, "updated_at": "2024-10-30T12:50:59Z"}, "1253": {"ticket_number": 106360, "subject": "Request for <PERSON> : Power BI Desktop - Windows App Data Modelling Software", "status": 5, "priority": 2, "updated_at": "2024-10-27T15:37:03Z"}, "1254": {"ticket_number": 106354, "subject": "Document Processing Error: Peavey Industries LP 2024-10-21 09:16:59", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:57:27Z"}, "1255": {"ticket_number": 106348, "subject": "EUS2-VBP-SAPDOX: Service: SAPMCP_02 (WMI Service): The Windows service you want to monitor is not running (Stopped). (code: PE011)", "status": 5, "priority": 2, "updated_at": "2024-10-29T17:52:28Z"}, "1256": {"ticket_number": 106345, "subject": "Printer access.", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:40:20Z"}, "1257": {"ticket_number": 106336, "subject": "Fw: Messages on <NAME_EMAIL>", "status": 5, "priority": 1, "updated_at": "2024-10-29T15:47:55Z"}, "1258": {"ticket_number": 106328, "subject": "Request for <PERSON><PERSON> : Bluebeam Basic - BlueBeam Annual Subscription Plans", "status": 5, "priority": 2, "updated_at": "2024-10-26T23:37:05Z"}, "1259": {"ticket_number": 106327, "subject": "user <EMAIL> locked out of Laptop: Asset#13247", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:24:59Z"}, "1260": {"ticket_number": 106323, "subject": "Off-Board <PERSON> / - Requested By <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T22:37:23Z"}, "1261": {"ticket_number": 106320, "subject": "VBP-ACH Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:13:24Z"}, "1262": {"ticket_number": 106318, "subject": "VBP-SAS Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:13:24Z"}, "1263": {"ticket_number": 106317, "subject": "VBP-MOC Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:13:24Z"}, "1264": {"ticket_number": 106316, "subject": "VBP-K<PERSON> Admin", "status": 2, "priority": 2, "updated_at": "2024-10-30T17:42:10Z"}, "1265": {"ticket_number": 106315, "subject": "VBP-STR ROMEO Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:13:24Z"}, "1266": {"ticket_number": 106314, "subject": "VBP-STR LORNE Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T20:13:24Z"}, "1267": {"ticket_number": 106309, "subject": "HR SharePoint-Limited Consultant Access", "status": 4, "priority": 1, "updated_at": "2024-10-30T23:12:17Z"}, "1268": {"ticket_number": 106308, "subject": "VBP-VIC Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:28:15Z"}, "1269": {"ticket_number": 106307, "subject": "VBP-BUR Admin", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:28:14Z"}, "1270": {"ticket_number": 106306, "subject": "MOR-FON Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:43:41Z"}, "1271": {"ticket_number": 106305, "subject": "MOR-BRI Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:45:58Z"}, "1272": {"ticket_number": 106304, "subject": "DRI-HOL Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-25T19:28:14Z"}, "1273": {"ticket_number": 106299, "subject": "Sender account compromised [<PERSON><PERSON>] #****(October AR Aging Report From Data Power Technology Group)", "status": 5, "priority": 3, "updated_at": "2024-10-28T18:05:56Z"}, "1274": {"ticket_number": 106296, "subject": "KIP-COL Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:16:11Z"}, "1275": {"ticket_number": 106295, "subject": "KIP-CAL Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:15:22Z"}, "1276": {"ticket_number": 106294, "subject": "KIP-LNG Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T22:07:53Z"}, "1277": {"ticket_number": 106293, "subject": "KIP-MOD Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:16:22Z"}, "1278": {"ticket_number": 106291, "subject": "KIP-DEL Admin PC", "status": 2, "priority": 2, "updated_at": "2024-10-25T18:58:06Z"}, "1279": {"ticket_number": 106290, "subject": "Request for <PERSON> : Service Account - Special Account Request - <PERSON><PERSON>, <PERSON><PERSON>, etc.", "status": 5, "priority": 2, "updated_at": "2024-10-24T20:36:02Z"}, "1280": {"ticket_number": 106272, "subject": "Finished Goods Project ", "status": 2, "priority": 1, "updated_at": "2024-10-28T09:13:04Z"}, "1281": {"ticket_number": 106270, "subject": "Request for Nancy Garza : Non-Standard Application - No License Required", "status": 2, "priority": 2, "updated_at": "2024-10-29T00:07:20Z"}, "1282": {"ticket_number": 106267, "subject": "Charging Issue", "status": 5, "priority": 2, "updated_at": "2024-10-29T19:33:18Z"}, "1283": {"ticket_number": 106265, "subject": "cannot print direct from SAP", "status": 5, "priority": 1, "updated_at": "2024-10-28T12:34:34Z"}, "1284": {"ticket_number": 106262, "subject": "Fix Password not required", "status": 5, "priority": 1, "updated_at": "2024-10-27T21:39:47Z"}, "1285": {"ticket_number": 106261, "subject": "Request for <PERSON> : RDP access to a server", "status": 2, "priority": 2, "updated_at": "2024-10-31T01:13:34Z"}, "1286": {"ticket_number": 106256, "subject": "<PERSON> - Holland P Drive Access", "status": 5, "priority": 1, "updated_at": "2024-10-26T17:37:02Z"}, "1287": {"ticket_number": 106255, "subject": "KIP-DEL Optimo Conf Room", "status": 2, "priority": 1, "updated_at": "2024-10-28T07:43:03Z"}, "1288": {"ticket_number": 106254, "subject": "KIP-DEL Hurcules Room Conf Room", "status": 5, "priority": 1, "updated_at": "2024-10-26T14:39:34Z"}, "1289": {"ticket_number": 106252, "subject": "Unable login to 2224", "status": 5, "priority": 1, "updated_at": "2024-10-25T12:37:18Z"}, "1290": {"ticket_number": 106250, "subject": "KMX-MON Maintenance office wi-fi issue", "status": 3, "priority": 1, "updated_at": "2024-10-28T19:27:57Z"}, "1291": {"ticket_number": 106246, "subject": "SIWA", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:05:10Z"}, "1292": {"ticket_number": 106245, "subject": "remoye management not working on my ipad ", "status": 2, "priority": 1, "updated_at": "2024-10-28T06:43:05Z"}, "1293": {"ticket_number": 106243, "subject": "A desk phone is required - <PERSON><PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:49:53Z"}, "1294": {"ticket_number": 106242, "subject": "Request for <PERSON> : SDWAN/Firewalls for Pocahontas - Network Equipment & Accessories", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:44:18Z"}, "1295": {"ticket_number": 106237, "subject": "PRT-B011 / Ko<PERSON> vs. BuroPro Citation", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:50:34Z"}, "1296": {"ticket_number": 106226, "subject": "Missing reader software for PLC", "status": 2, "priority": 1, "updated_at": "2024-10-28T05:13:00Z"}, "1297": {"ticket_number": 106206, "subject": "Terminated account - Immediate action required | Mark <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T06:22:35Z"}, "1298": {"ticket_number": 106199, "subject": "Request for <PERSON><PERSON><PERSON> : Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:36:06Z"}, "1299": {"ticket_number": 106198, "subject": "Request for <PERSON> : Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-28T16:33:13Z"}, "1300": {"ticket_number": 106195, "subject": "VBP-L-12455 bitlocker not enabled", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:05:04Z"}, "1301": {"ticket_number": 106193, "subject": "3 computers missing bitlocker", "status": 2, "priority": 1, "updated_at": "2024-10-28T13:59:35Z"}, "1302": {"ticket_number": 106192, "subject": "kna-d-7493 Red Lion desktop replacement", "status": 2, "priority": 2, "updated_at": "2024-10-26T03:57:58Z"}, "1303": {"ticket_number": 106180, "subject": "On-board <PERSON><PERSON> (Tue, 2024, Oct 22) - Full-Time Employee Burlington", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:15:38Z"}, "1304": {"ticket_number": 106161, "subject": "Ship-To Address SAP Glitch - order 1-98465 LAM Research Building G Roof", "status": 5, "priority": 1, "updated_at": "2024-10-30T14:57:19Z"}, "1305": {"ticket_number": 106155, "subject": "On-board <PERSON> (Fri, 2024, Oct 18) - Contract TAF - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T13:14:11Z"}, "1306": {"ticket_number": 106154, "subject": "MONITORS + DOCKING STATION NEEDED ", "status": 5, "priority": 1, "updated_at": "2024-10-29T19:57:42Z"}, "1307": {"ticket_number": 106148, "subject": "Bluebeam Awip renewal", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:11:28Z"}, "1308": {"ticket_number": 106147, "subject": "Access to Network Share in order to use CFS.", "status": 5, "priority": 1, "updated_at": "2024-10-24T00:39:30Z"}, "1309": {"ticket_number": 106142, "subject": "KNA-L-14166 KAF migration cleanup", "status": 5, "priority": 1, "updated_at": "2024-10-31T16:37:43Z"}, "1310": {"ticket_number": 106140, "subject": "Renewal for Bluebeam Kip <PERSON>pan Insulated Panels - Florida", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:17:46Z"}, "1311": {"ticket_number": 106138, "subject": "Delete VBPKEN-MITEK3 server from KNAMOD-VMHOST1.", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:03:01Z"}, "1312": {"ticket_number": 106133, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Jessup: AutoDesk - AutoCAD Standard - Subscription", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:14:40Z"}, "1313": {"ticket_number": 106134, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Jessup: AutoDesk - DWG TrueView", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:15:44Z"}, "1314": {"ticket_number": 106129, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Jessup: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:15:07Z"}, "1315": {"ticket_number": 106120, "subject": "Pushing dates out", "status": 2, "priority": 1, "updated_at": "2024-10-25T08:28:09Z"}, "1316": {"ticket_number": 106115, "subject": "Email redirect - leaver", "status": 5, "priority": 1, "updated_at": "2024-10-26T15:17:31Z"}, "1317": {"ticket_number": 106106, "subject": "Onboard <PERSON> (Mon, 2024, Oct 21) - Full-Time Employee Burlington", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:13:42Z"}, "1318": {"ticket_number": 106100, "subject": "cannot hook up to the internet ", "status": 5, "priority": 2, "updated_at": "2024-10-28T21:00:04Z"}, "1319": {"ticket_number": 106099, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-24T15:13:29Z"}, "1320": {"ticket_number": 106094, "subject": "<PERSON> needs OneDrive access to  <PERSON>'s  files", "status": 3, "priority": 2, "updated_at": "2024-10-28T16:05:46Z"}, "1321": {"ticket_number": 106092, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:05:00Z"}, "1322": {"ticket_number": 106090, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-28T13:38:08Z"}, "1323": {"ticket_number": 106089, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:03:58Z"}, "1324": {"ticket_number": 106088, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-28T13:38:22Z"}, "1325": {"ticket_number": 106087, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-28T13:40:46Z"}, "1326": {"ticket_number": 106084, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T03:30:13Z"}, "1327": {"ticket_number": 106085, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Jessup: XA / AS400 - Access and Permissions", "status": 3, "priority": 2, "updated_at": "2024-10-28T18:13:09Z"}, "1328": {"ticket_number": 106083, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-28T13:37:43Z"}, "1329": {"ticket_number": 106074, "subject": "Adding IPs to the VPN profile for Symplistech", "status": 5, "priority": 2, "updated_at": "2024-10-29T14:26:04Z"}, "1330": {"ticket_number": 106073, "subject": "Request by <PERSON> for <PERSON> : Yubikey Security Keys for Caledon Staff | Security Key / Token", "status": 2, "priority": 2, "updated_at": "2024-10-24T13:43:10Z"}, "1331": {"ticket_number": 106061, "subject": "Request - Red Lion Main; Upper Receiving Area", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:27:16Z"}, "1332": {"ticket_number": 106060, "subject": "Access to  BMSM001", "status": 2, "priority": 1, "updated_at": "2024-10-25T04:43:13Z"}, "1333": {"ticket_number": 106057, "subject": "Urgent - Two UPS needed for Kensington Metal", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:40:18Z"}, "1334": {"ticket_number": 106055, "subject": "On-board <PERSON> (<PERSON><PERSON>, 2024, Oct 29) - Contract KNA - Holland: Bluebeam Basic - BlueBeam Annual Subscription Plans", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:57:11Z"}, "1335": {"ticket_number": 106056, "subject": "On-board <PERSON> (Tue, 2024, Oct 29) - Contract KNA - Holland: AutoDesk - AutoCAD Standard - Subscription", "status": 5, "priority": 2, "updated_at": "2024-10-30T15:57:25Z"}, "1336": {"ticket_number": 106049, "subject": "On-board <PERSON> (<PERSON><PERSON>, 2024, Oct 29) - Contract KNA - Holland: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T19:37:22Z"}, "1337": {"ticket_number": 106051, "subject": "On-board <PERSON> (Tue, 2024, Oct 29) - Contract KNA - Holland: Laptop - Engineering/Drafting", "status": 5, "priority": 2, "updated_at": "2024-10-29T21:15:14Z"}, "1338": {"ticket_number": 106050, "subject": "On-board <PERSON> (<PERSON><PERSON>, 2024, Oct 29) - Contract KNA - Holland: Zoom - Office Phone", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:17:10Z"}, "1339": {"ticket_number": 106048, "subject": "On-board <PERSON> (Tue, 2024, Oct 29) - Contract KNA - Holland", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:17:37Z"}, "1340": {"ticket_number": 106033, "subject": "vendor master file annual reveiw", "status": 2, "priority": 1, "updated_at": "2024-10-25T15:06:09Z"}, "1341": {"ticket_number": 106017, "subject": "Lansweeper- TEC B-EX6T- rename, asset tag an Ka location", "status": 2, "priority": 1, "updated_at": "2024-10-31T11:42:14Z"}, "1342": {"ticket_number": 106013, "subject": "kna-l-14001 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-28T18:50:43Z"}, "1343": {"ticket_number": 106012, "subject": "kna-l-8633 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:39:28Z"}, "1344": {"ticket_number": 106009, "subject": "Lansweeper- kna-l-7865- Asset not seen for 30+ days", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:50:59Z"}, "1345": {"ticket_number": 105999, "subject": "Customer Getting an Undeliverable Message When Emailing Us: <EMAIL>", "status": 5, "priority": 1, "updated_at": "2024-10-24T23:39:05Z"}, "1346": {"ticket_number": 105992, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Mattoon: KIP - <PERSON><PERSON> Insulated Panels - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-29T15:27:04Z"}, "1347": {"ticket_number": 105990, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:49:23Z"}, "1348": {"ticket_number": 105988, "subject": "<PERSON> - Added to KIP Everyone Distro", "status": 5, "priority": 2, "updated_at": "2024-10-25T15:42:57Z"}, "1349": {"ticket_number": 105987, "subject": "FW: Associate Announcement - <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-24T12:20:02Z"}, "1350": {"ticket_number": 105963, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T06:12:45Z"}, "1351": {"ticket_number": 105955, "subject": "KIP-DEL MFA Exception Temp", "status": 5, "priority": 1, "updated_at": "2024-10-26T14:39:34Z"}, "1352": {"ticket_number": 105949, "subject": "Deploy SAP SSO certificates to Vicwest computers", "status": 2, "priority": 1, "updated_at": "2024-10-24T09:13:09Z"}, "1353": {"ticket_number": 105937, "subject": "Bellara orders 2 copies ", "status": 2, "priority": 1, "updated_at": "2024-10-24T08:12:39Z"}, "1354": {"ticket_number": 105935, "subject": "FW: 3-7959 NLK Project", "status": 5, "priority": 1, "updated_at": "2024-10-25T16:37:54Z"}, "1355": {"ticket_number": 105934, "subject": "Adobe - Creative Cloud - M<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T15:37:18Z"}, "1356": {"ticket_number": 105932, "subject": "Adobe - Creative Cloud - M<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-26T13:38:01Z"}, "1357": {"ticket_number": 105916, "subject": "FW: Meeting DW Auto Bundler ", "status": 2, "priority": 1, "updated_at": "2024-10-24T06:43:07Z"}, "1358": {"ticket_number": 105913, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:39:25Z"}, "1359": {"ticket_number": 105911, "subject": "Request for <PERSON> : Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:00:36Z"}, "1360": {"ticket_number": 105909, "subject": "reset passwords", "status": 5, "priority": 2, "updated_at": "2024-10-28T19:57:50Z"}, "1361": {"ticket_number": 105906, "subject": "Lansweeper - 10.177.106.17 - Unknown", "status": 2, "priority": 1, "updated_at": "2024-10-28T21:11:02Z"}, "1362": {"ticket_number": 105904, "subject": "Request for <PERSON> : Desktop - Engineering/Drafting", "status": 2, "priority": 2, "updated_at": "2024-10-24T21:20:24Z"}, "1363": {"ticket_number": 105903, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - Jessup: Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:27:47Z"}, "1364": {"ticket_number": 105901, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - Jessup: Laptop - Engineering/Drafting", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:26:41Z"}, "1365": {"ticket_number": 105900, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - Jessup: Workstation Setup", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:24:39Z"}, "1366": {"ticket_number": 105898, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T06:12:56Z"}, "1367": {"ticket_number": 105892, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Allow List - Allow a Domain - www.epicgames.com", "status": 5, "priority": 2, "updated_at": "2024-10-27T19:37:09Z"}, "1368": {"ticket_number": 105891, "subject": "Snag it", "status": 5, "priority": 1, "updated_at": "2024-10-26T18:37:09Z"}, "1369": {"ticket_number": 105890, "subject": "RE: FM Labeling of KSFL from 1020 (Modesto) 24x6\" (26/26) panels ", "status": 2, "priority": 1, "updated_at": "2024-10-24T05:13:03Z"}, "1370": {"ticket_number": 105886, "subject": "V060096, V060097 PURCH REQ ISSUES", "status": 2, "priority": 1, "updated_at": "2024-10-24T04:58:02Z"}, "1371": {"ticket_number": 105884, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:02Z"}, "1372": {"ticket_number": 105885, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-30T19:39:16Z"}, "1373": {"ticket_number": 105883, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-30T21:37:33Z"}, "1374": {"ticket_number": 105881, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: SAP S/4 HANA - KIP/AWIP Access & Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-25T16:35:57Z"}, "1375": {"ticket_number": 105880, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Zoom - Office Phone", "status": 5, "priority": 2, "updated_at": "2024-10-25T22:23:15Z"}, "1376": {"ticket_number": 105878, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg", "status": 5, "priority": 2, "updated_at": "2024-10-30T21:37:32Z"}, "1377": {"ticket_number": 105840, "subject": "Monthly Endpoint Review - Sensor Update", "status": 2, "priority": 1, "updated_at": "2024-10-29T17:22:04Z"}, "1378": {"ticket_number": 105832, "subject": "re-enable EAS-PRSV1 backups", "status": 5, "priority": 2, "updated_at": "2024-10-29T17:52:10Z"}, "1379": {"ticket_number": 105829, "subject": "<PERSON><PERSON> email signature", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:16:07Z"}, "1380": {"ticket_number": 105822, "subject": "Settlement posting missing", "status": 4, "priority": 1, "updated_at": "2024-10-31T15:09:48Z"}, "1381": {"ticket_number": 105810, "subject": "SEC-UAC-01-AD - Review & Disable AD Inactive Accounts - 30 Days Inactive", "status": 5, "priority": 3, "updated_at": "2024-10-28T20:22:08Z"}, "1382": {"ticket_number": 105808, "subject": "[<PERSON><PERSON>] You can now download your file", "status": 5, "priority": 3, "updated_at": "2024-10-24T16:37:51Z"}, "1383": {"ticket_number": 105800, "subject": "Solid works 2023", "status": 5, "priority": 1, "updated_at": "2024-10-26T22:37:10Z"}, "1384": {"ticket_number": 105791, "subject": "Tanium Patching -  KNA-L-12737", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:40:17Z"}, "1385": {"ticket_number": 105782, "subject": "Lansweeper- *********** - A scanned IP that can't be tracked", "status": 5, "priority": 1, "updated_at": "2024-10-24T22:37:21Z"}, "1386": {"ticket_number": 105776, "subject": "Qualys Level 4 Internal Vulnerability - ************* - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:30:56Z"}, "1387": {"ticket_number": 105768, "subject": "Adobe PDF Glitches - Customer Service Department", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:56:24Z"}, "1388": {"ticket_number": 105763, "subject": "Temps SW License Switch", "status": 5, "priority": 1, "updated_at": "2024-10-28T20:46:12Z"}, "1389": {"ticket_number": 105760, "subject": "Fw: SolidWorks Subscription Service - <PERSON><PERSON><PERSON>minder - P-24-62031 Due 12/21/2024", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:53:57Z"}, "1390": {"ticket_number": 105750, "subject": "On-board <PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Bristol: SAP B1 - Morin Access & Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-29T14:28:03Z"}, "1391": {"ticket_number": 105746, "subject": "Internet Connection Keeps Dropping Every Few Minutes (Wi-Fi)", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:37:22Z"}, "1392": {"ticket_number": 105745, "subject": "Hardware ", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:32:30Z"}, "1393": {"ticket_number": 105735, "subject": "Microsoft Account Adjustments", "status": 5, "priority": 1, "updated_at": "2024-10-28T19:56:30Z"}, "1394": {"ticket_number": 105725, "subject": "Request for <PERSON><PERSON> : Tablet Individually Owned iPad", "status": 5, "priority": 2, "updated_at": "2024-10-28T19:45:14Z"}, "1395": {"ticket_number": 105718, "subject": "Forgot mouse home", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:44:59Z"}, "1396": {"ticket_number": 105702, "subject": "Request for <PERSON> : Allow List - Allow a Domain - https://securefiles.oracle.com/", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:22:46Z"}, "1397": {"ticket_number": 105688, "subject": "Request for <PERSON> : Zoom - Office Phone", "status": 5, "priority": 1, "updated_at": "2024-10-24T12:37:43Z"}, "1398": {"ticket_number": 105679, "subject": "Vicwest - Moncton/Kensington - Quarterly Site Visit", "status": 2, "priority": 2, "updated_at": "2024-10-30T18:02:47Z"}, "1399": {"ticket_number": 105676, "subject": "KIP - Columbus - Quadrimester Site Visit", "status": 2, "priority": 2, "updated_at": "2024-10-25T17:32:00Z"}, "1400": {"ticket_number": 105660, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Monterrey: Zoom - Office Phone", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:37:09Z"}, "1401": {"ticket_number": 105658, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Monterrey", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:37:09Z"}, "1402": {"ticket_number": 105657, "subject": "kna-l-8345 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:14:44Z"}, "1403": {"ticket_number": 105655, "subject": "kna-l-12035 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:46:46Z"}, "1404": {"ticket_number": 105648, "subject": "Lansweeper- kna-l-14181- inactive device enabled", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:56:17Z"}, "1405": {"ticket_number": 105631, "subject": "Lansweeper Unknown Device - 10.177.68.100", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:30Z"}, "1406": {"ticket_number": 105626, "subject": "Vicwest Adobe renewal 2024", "status": 2, "priority": 1, "updated_at": "2024-10-29T16:08:17Z"}, "1407": {"ticket_number": 105622, "subject": "Blocked Site FW: Microsoft PO#: 476022916", "status": 5, "priority": 1, "updated_at": "2024-10-25T17:37:46Z"}, "1408": {"ticket_number": 105618, "subject": "Microsoft Access!", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:08:40Z"}, "1409": {"ticket_number": 105610, "subject": "New ticket logged Call Reference - *********.1397 Re: Sharepoint folder KA-KIP NA DRAFTING DEPARTMENT out of Space", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:13:33Z"}, "1410": {"ticket_number": 105599, "subject": "<PERSON><PERSON><PERSON> of Monitors", "status": 5, "priority": 2, "updated_at": "2024-10-31T12:56:03Z"}, "1411": {"ticket_number": 105587, "subject": "DeLand Stock Replenishment : Laptop - Standard (Non-Bundled)", "status": 2, "priority": 2, "updated_at": "2024-10-28T16:04:49Z"}, "1412": {"ticket_number": 105570, "subject": "Request for Jacob Grizzle : Desktop - Standard (Non-Boarding)", "status": 2, "priority": 2, "updated_at": "2024-10-29T22:33:34Z"}, "1413": {"ticket_number": 105546, "subject": "Mimecast Error Message", "status": 5, "priority": 1, "updated_at": "2024-10-24T21:37:30Z"}, "1414": {"ticket_number": 105518, "subject": "Terminated account - Immediate action required | <PERSON><PERSON><PERSON> Espinoza", "status": 5, "priority": 1, "updated_at": "2024-10-30T05:40:59Z"}, "1415": {"ticket_number": 105516, "subject": "Access to <PERSON> emails ", "status": 5, "priority": 1, "updated_at": "2024-10-28T22:44:55Z"}, "1416": {"ticket_number": 105470, "subject": "On-board <PERSON> (Mon, 2024, Sep 30) - Full-Time Employee VBP - Acheson: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-29T20:45:59Z"}, "1417": {"ticket_number": 105454, "subject": "LS to Tanium - Not Showing in Tanium", "status": 2, "priority": 1, "updated_at": "2024-10-29T20:24:05Z"}, "1418": {"ticket_number": 105449, "subject": "Material Request Form 304L 2B ANNEALED COIL", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:19:05Z"}, "1419": {"ticket_number": 105437, "subject": "AWIP: Request to setup Team Resources for MS Project Online", "status": 5, "priority": 1, "updated_at": "2024-10-28T22:44:07Z"}, "1420": {"ticket_number": 105397, "subject": "RISA Section app install", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:20:14Z"}, "1421": {"ticket_number": 105361, "subject": "Tanium Patching -  2024-09 Cumulative Update for Windows 11 Version 23H2 for x64-based Systems (KB5043076)", "status": 2, "priority": 1, "updated_at": "2024-10-29T20:42:00Z"}, "1422": {"ticket_number": 105341, "subject": "Request for <PERSON> Byrgesen : HP Elite x360 1040 14 inch G11 2-in-1 Notebook PC - Computer - Non-Standard Laptop or Desktop", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:19:02Z"}, "1423": {"ticket_number": 105321, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Adobe - Acrobat DC Pro", "status": 2, "priority": 2, "updated_at": "2024-10-28T14:14:18Z"}, "1424": {"ticket_number": 105320, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Mobile Phone - New, Reassign or Replace", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:23:54Z"}, "1425": {"ticket_number": 105317, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Docking Station", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:22:46Z"}, "1426": {"ticket_number": 105318, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Keyboard and Mouse", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:23:05Z"}, "1427": {"ticket_number": 105319, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Wireless Headset", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:23:18Z"}, "1428": {"ticket_number": 105315, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Laptop - Standard", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:21:50Z"}, "1429": {"ticket_number": 105316, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Monitor", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:22:06Z"}, "1430": {"ticket_number": 105313, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - Jessup: Workstation Setup", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:21:31Z"}, "1431": {"ticket_number": 105311, "subject": "On-board <PERSON><PERSON><PERSON> (Mon, 2024, Nov 11) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:21:15Z"}, "1432": {"ticket_number": 105298, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:31:55Z"}, "1433": {"ticket_number": 105297, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-31T15:31:51Z"}, "1434": {"ticket_number": 105238, "subject": "New Printer Request", "status": 2, "priority": 1, "updated_at": "2024-10-31T13:43:37Z"}, "1435": {"ticket_number": 105220, "subject": "Wireless mouse cursor jumping all over screen and very difficult to control.", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:19:18Z"}, "1436": {"ticket_number": 105213, "subject": "phone set moved", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:39:59Z"}, "1437": {"ticket_number": 105204, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:47:34Z"}, "1438": {"ticket_number": 105203, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:49:03Z"}, "1439": {"ticket_number": 105199, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:40:16Z"}, "1440": {"ticket_number": 105198, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:49:40Z"}, "1441": {"ticket_number": 105194, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:44:56Z"}, "1442": {"ticket_number": 105192, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:07:31Z"}, "1443": {"ticket_number": 105191, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:50:38Z"}, "1444": {"ticket_number": 105188, "subject": "SEC-UAC-05: Reviewing Contractor accounts in Active Directory", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:40:01Z"}, "1445": {"ticket_number": 105187, "subject": "Document Processing Error: Peavey Industries LP 2024-10-10 02:05:31", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:57:33Z"}, "1446": {"ticket_number": 105182, "subject": "Lansweeper- SN022198621657 - asset tag, rename", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:23:00Z"}, "1447": {"ticket_number": 105172, "subject": "<PERSON> return to work. Reactivate ID and enable PC.", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:39:23Z"}, "1448": {"ticket_number": 105168, "subject": "Request for <PERSON>: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T23:37:05Z"}, "1449": {"ticket_number": 105163, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>ough: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:37:37Z"}, "1450": {"ticket_number": 105162, "subject": "Off-Board <PERSON><PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T18:37:25Z"}, "1451": {"ticket_number": 105154, "subject": "Off-Board <PERSON> / - Requested By <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T20:37:20Z"}, "1452": {"ticket_number": 105155, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-25T20:37:22Z"}, "1453": {"ticket_number": 105149, "subject": "Request for <PERSON><PERSON> Bo<PERSON>hina : Need quote for 2 Palo Alto PA-440 hardware + license + Cyderes deployment fee - Network Equipment & Accessories", "status": 2, "priority": 2, "updated_at": "2024-10-25T13:47:07Z"}, "1454": {"ticket_number": 105119, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:02Z"}, "1455": {"ticket_number": 105120, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:54:20Z"}, "1456": {"ticket_number": 105121, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:39:48Z"}, "1457": {"ticket_number": 105118, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:02Z"}, "1458": {"ticket_number": 105116, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:01Z"}, "1459": {"ticket_number": 105117, "subject": "On-board <PERSON>ani <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-27T16:39:02Z"}, "1460": {"ticket_number": 105114, "subject": "Request for <PERSON> : SAP S/4 HANA - KIP/AWIP Access & Permissions", "status": 5, "priority": 2, "updated_at": "2024-10-28T14:05:17Z"}, "1461": {"ticket_number": 105115, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-30T16:39:47Z"}, "1462": {"ticket_number": 105113, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg: Zoom - Office Phone", "status": 5, "priority": 2, "updated_at": "2024-10-25T22:22:25Z"}, "1463": {"ticket_number": 105111, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - East Stroudsburg", "status": 4, "priority": 2, "updated_at": "2024-10-29T21:21:17Z"}, "1464": {"ticket_number": 105107, "subject": "Iphone 11 Replacement ", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:17:55Z"}, "1465": {"ticket_number": 105088, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:32:01Z"}, "1466": {"ticket_number": 105085, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Docking Station", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:32:32Z"}, "1467": {"ticket_number": 105086, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Keyboard and Mouse", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:32:45Z"}, "1468": {"ticket_number": 105087, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Wireless Headset", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:32:58Z"}, "1469": {"ticket_number": 105083, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:24:34Z"}, "1470": {"ticket_number": 105084, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Deland: Monitor", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:32:17Z"}, "1471": {"ticket_number": 105080, "subject": "On-board <PERSON> (Mon, 2024, Nov 4) - Full-Time Employee KNA - Del<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:33:25Z"}, "1472": {"ticket_number": 105076, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Deland: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-28T12:17:33Z"}, "1473": {"ticket_number": 105070, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Deland: KIP - Kingspan Insulated Panels - Concur New Account", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:34:14Z"}, "1474": {"ticket_number": 105068, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Full-Time Employee KNA - Del<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-29T13:16:27Z"}, "1475": {"ticket_number": 105053, "subject": "Terminated account - Immediate action required | Jose Gloria", "status": 5, "priority": 1, "updated_at": "2024-10-30T05:34:09Z"}, "1476": {"ticket_number": 105038, "subject": "Support Qualys", "status": 3, "priority": 2, "updated_at": "2024-10-24T16:38:42Z"}, "1477": {"ticket_number": 105023, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - Jessup: Adobe - Acrobat DC Pro", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:15:59Z"}, "1478": {"ticket_number": 105018, "subject": "On-board <PERSON> (Mon, 2024, Oct 28) - Contract TAF - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-31T13:16:22Z"}, "1479": {"ticket_number": 105004, "subject": "Request for <PERSON> : Recover Company Assets #15004", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:37:42Z"}, "1480": {"ticket_number": 104999, "subject": "<PERSON>up Guest Wifi run out of available IP addresses", "status": 5, "priority": 3, "updated_at": "2024-10-29T16:32:49Z"}, "1481": {"ticket_number": 104995, "subject": "Request to modify SPS Demand data dump", "status": 5, "priority": 1, "updated_at": "2024-10-31T18:37:54Z"}, "1482": {"ticket_number": 104975, "subject": "Tate ASP restock / stock level establishment", "status": 2, "priority": 1, "updated_at": "2024-10-28T16:16:35Z"}, "1483": {"ticket_number": 104959, "subject": "On-board <PERSON> (Tu<PERSON>, 2024, Oct 15) - Full-Time Employee Burlington: Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-30T14:37:50Z"}, "1484": {"ticket_number": 104953, "subject": "On-board <PERSON> (Tu<PERSON>, 2024, Oct 15) - Full-Time Employee Burlington", "status": 5, "priority": 2, "updated_at": "2024-10-30T14:37:49Z"}, "1485": {"ticket_number": 104884, "subject": "Lansweeper- KNA-L-15272- incomplete scan and info", "status": 5, "priority": 1, "updated_at": "2024-10-26T13:38:01Z"}, "1486": {"ticket_number": 104878, "subject": "kna-l-14606 - Not seen for 30 days", "status": 5, "priority": 1, "updated_at": "2024-10-24T23:39:04Z"}, "1487": {"ticket_number": 104876, "subject": "kna-l-8646 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:58:38Z"}, "1488": {"ticket_number": 104868, "subject": "Request for <PERSON><PERSON><PERSON> : Microsoft Visio - Flowchart Maker and Diagramming Software", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:58:17Z"}, "1489": {"ticket_number": 104863, "subject": "Lansweeper- KNA-Fontana-MDF SW1- duplicate serial number", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:53:30Z"}, "1490": {"ticket_number": 104833, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:10:20Z"}, "1491": {"ticket_number": 104829, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:10:46Z"}, "1492": {"ticket_number": 104826, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:11:10Z"}, "1493": {"ticket_number": 104815, "subject": "KNA-T-14156 - Endpoint Encryption", "status": 5, "priority": 2, "updated_at": "2024-10-25T16:37:52Z"}, "1494": {"ticket_number": 104781, "subject": "<PERSON><PERSON><PERSON><PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:16:46Z"}, "1495": {"ticket_number": 104780, "subject": "Request for <PERSON> : Stock/spare Laptop - Engineering/Drafting (Non-Bundled)", "status": 5, "priority": 2, "updated_at": "2024-10-31T00:39:24Z"}, "1496": {"ticket_number": 104762, "subject": "Printer not working", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:41:06Z"}, "1497": {"ticket_number": 104751, "subject": "On-board <PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T13:13:00Z"}, "1498": {"ticket_number": 104745, "subject": "Process runner & Transit Folder access", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:08:07Z"}, "1499": {"ticket_number": 104716, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Document Scanning to SharePoint", "status": 5, "priority": 2, "updated_at": "2024-10-31T11:54:55Z"}, "1500": {"ticket_number": 104658, "subject": "sonido del computador", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:17:28Z"}, "1501": {"ticket_number": 104644, "subject": "Bluebeam Revu ", "status": 5, "priority": 1, "updated_at": "2024-10-28T18:37:25Z"}, "1502": {"ticket_number": 104611, "subject": "Request for <PERSON><PERSON> Ali : Data outlets are requested at a new office at Red Lion Annex | NEW Site Plant, Office or Facility", "status": 2, "priority": 2, "updated_at": "2024-10-29T22:21:01Z"}, "1503": {"ticket_number": 104603, "subject": "Request for <PERSON> : Bluebeam Basic - BlueBeam Annual Subscription Plans", "status": 5, "priority": 2, "updated_at": "2024-10-24T12:56:42Z"}, "1504": {"ticket_number": 104592, "subject": "Cyber Security Metrics are due", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:27:13Z"}, "1505": {"ticket_number": 104569, "subject": "On-board <PERSON> (Mon, 2024, Oct 7) - Temporary KNA - Fontana: SAP B1 - Morin Access & Permissions", "status": 5, "priority": 1, "updated_at": "2024-10-29T15:05:53Z"}, "1506": {"ticket_number": 104563, "subject": "Adobe - Creative Cloud - <PERSON><PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-26T20:37:14Z"}, "1507": {"ticket_number": 104546, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Request for <PERSON> by System:", "status": 5, "priority": 2, "updated_at": "2024-10-24T15:37:22Z"}, "1508": {"ticket_number": 104541, "subject": "Off-Board <PERSON> 07OCT24 1800", "status": 5, "priority": 2, "updated_at": "2024-10-24T15:37:20Z"}, "1509": {"ticket_number": 104542, "subject": "Off-Board <PERSON> / - Requested By <PERSON>: Recover Company Assets", "status": 5, "priority": 2, "updated_at": "2024-10-24T15:37:21Z"}, "1510": {"ticket_number": 104436, "subject": "LS to CS Rec - Not in CS", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:51:23Z"}, "1511": {"ticket_number": 104399, "subject": "Bluebeam Complete Renewal - October 31st - KIP Mexico", "status": 2, "priority": 1, "updated_at": "2024-10-31T00:48:31Z"}, "1512": {"ticket_number": 104385, "subject": "Internet Access", "status": 5, "priority": 1, "updated_at": "2024-10-31T16:03:49Z"}, "1513": {"ticket_number": 104345, "subject": "RE: Ticket [#SR-99513] Updated with Reply: Digital storage for production documents", "status": 5, "priority": 1, "updated_at": "2024-10-31T11:54:49Z"}, "1514": {"ticket_number": 104315, "subject": "Tanium Patching -  KNA-L-8852", "status": 2, "priority": 1, "updated_at": "2024-10-29T16:32:49Z"}, "1515": {"ticket_number": 104303, "subject": "Wix Tool Set", "status": 2, "priority": 1, "updated_at": "2024-10-29T23:54:38Z"}, "1516": {"ticket_number": 104218, "subject": "Windows reimage to 11", "status": 5, "priority": 1, "updated_at": "2024-10-30T18:44:28Z"}, "1517": {"ticket_number": 104199, "subject": "Terminated account - Immediate action required | C<PERSON><PERSON> Adams", "status": 5, "priority": 1, "updated_at": "2024-10-30T10:08:10Z"}, "1518": {"ticket_number": 104182, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T05:18:50Z"}, "1519": {"ticket_number": 104169, "subject": "OLE Action", "status": 2, "priority": 1, "updated_at": "2024-10-30T12:26:57Z"}, "1520": {"ticket_number": 104168, "subject": "Request for <PERSON> : Non-Standard Application - No License Required", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:38:58Z"}, "1521": {"ticket_number": 104162, "subject": "Lansweeper- kna-p-1470- duplicate barcode", "status": 5, "priority": 1, "updated_at": "2024-10-26T23:37:04Z"}, "1522": {"ticket_number": 104161, "subject": "Lansweeper- Printer - HP039387-asset tag, rename", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:15:26Z"}, "1523": {"ticket_number": 104159, "subject": "Lansweeper- Printer - HPI6E81CC asset tag, rename", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:21:12Z"}, "1524": {"ticket_number": 104156, "subject": "Modesto - Operator Account", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:42:27Z"}, "1525": {"ticket_number": 104128, "subject": "On-board <PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Little Rock", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:59:34Z"}, "1526": {"ticket_number": 104114, "subject": "Lansweeper-verizon- User was assigned a \"for disposal device\" and a phone with a suspended line", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:36:45Z"}, "1527": {"ticket_number": 104103, "subject": "Request for <PERSON> : Pocahontas Palo alto Setup - Network Equipment & Accessories", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:23:06Z"}, "1528": {"ticket_number": 104088, "subject": "Stacker desktop replacement", "status": 5, "priority": 1, "updated_at": "2024-10-24T18:51:55Z"}, "1529": {"ticket_number": 104086, "subject": "ITGC SEC-UAC-02-AD - Quarterly Review of Generic Accounts", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:46:20Z"}, "1530": {"ticket_number": 104082, "subject": "iPhone for <PERSON><PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-30T16:17:12Z"}, "1531": {"ticket_number": 104017, "subject": "SEC-UAC-10 - UAC Domain Admin Review KC_ITM_8.1.1", "status": 5, "priority": 1, "updated_at": "2024-10-25T20:02:40Z"}, "1532": {"ticket_number": 103991, "subject": "JAPRT 06 printer issue", "status": 5, "priority": 1, "updated_at": "2024-10-25T17:39:56Z"}, "1533": {"ticket_number": 103985, "subject": "Data - Verizon & Telus Mobile Data Retrieval", "status": 2, "priority": 1, "updated_at": "2024-10-28T13:51:43Z"}, "1534": {"ticket_number": 103970, "subject": "Quarterly review of Contractors account disposition – Active, Standby, Remove", "status": 5, "priority": 2, "updated_at": "2024-10-30T13:51:45Z"}, "1535": {"ticket_number": 103935, "subject": "Qualys Level 4 Internal Vulnerability - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:43:10Z"}, "1536": {"ticket_number": 103932, "subject": "Qualys Level 5 Internal Vulnerability - ************ -78031 ~ Writeable SNMP Information", "status": 2, "priority": 2, "updated_at": "2024-10-28T21:34:36Z"}, "1537": {"ticket_number": 103812, "subject": "MFA", "status": 2, "priority": 2, "updated_at": "2024-10-30T16:38:23Z"}, "1538": {"ticket_number": 103770, "subject": "Cellular Concrete invoice", "status": 5, "priority": 1, "updated_at": "2024-10-25T15:06:29Z"}, "1539": {"ticket_number": 103737, "subject": "New iPhone for <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:41:41Z"}, "1540": {"ticket_number": 103735, "subject": "Replacement - iPhone 16 for <PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-30T15:31:49Z"}, "1541": {"ticket_number": 103695, "subject": "TAF-JES-Plant-UPS: <PERSON> (<PERSON>): Request timed out (ICMP error # 11010)", "status": 4, "priority": 3, "updated_at": "2024-10-31T18:28:53Z"}, "1542": {"ticket_number": 103648, "subject": "OneDrive No Access Error", "status": 5, "priority": 3, "updated_at": "2024-10-31T17:37:59Z"}, "1543": {"ticket_number": 103607, "subject": "On-board <PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - Baltimore", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:59:12Z"}, "1544": {"ticket_number": 103586, "subject": "Unity HUB Install request", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:10:43Z"}, "1545": {"ticket_number": 103519, "subject": "EUS2-VBP-MHQ: SSH Disk Free (SSH Disk Free): 6 % (Free Space /hana/backup) is below the error limit of 10 % in Free Space /hana/backup", "status": 5, "priority": 2, "updated_at": "2024-10-28T18:36:43Z"}, "1546": {"ticket_number": 103517, "subject": "Request for <PERSON> : Laptop - Standard", "status": 5, "priority": 2, "updated_at": "2024-10-29T15:06:26Z"}, "1547": {"ticket_number": 103456, "subject": "(Merged to  102053) Vulnerability over tcp/25 [#SR-102053}", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:12:17Z"}, "1548": {"ticket_number": 103440, "subject": "AWIP Shipment Output", "status": 5, "priority": 1, "updated_at": "2024-10-25T12:43:19Z"}, "1549": {"ticket_number": 103395, "subject": "Add groups to Exclaimer Mailflow rule AND add 3 users to Exclaimer Cloud Portal", "status": 5, "priority": 2, "updated_at": "2024-10-25T20:37:20Z"}, "1550": {"ticket_number": 103391, "subject": "Tanium Patching -  KNA-L-14229", "status": 5, "priority": 1, "updated_at": "2024-10-31T16:37:40Z"}, "1551": {"ticket_number": 103381, "subject": "Network Cable Install : <PERSON><PERSON>", "status": 2, "priority": 2, "updated_at": "2024-10-30T12:24:14Z"}, "1552": {"ticket_number": 103379, "subject": "Qualys Level 3 Internal Vulnerability - ************", "status": 5, "priority": 1, "updated_at": "2024-10-25T20:37:18Z"}, "1553": {"ticket_number": 103356, "subject": "VBP-<PERSON><PERSON> - Enroll in MDM? ", "status": 2, "priority": 2, "updated_at": "2024-10-30T17:48:47Z"}, "1554": {"ticket_number": 103353, "subject": "TSP-BUR - Enroll in MDM", "status": 2, "priority": 2, "updated_at": "2024-10-30T14:41:59Z"}, "1555": {"ticket_number": 103308, "subject": "Paging system - 3098", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:51:25Z"}, "1556": {"ticket_number": 103305, "subject": "AD User Account Migration - <PERSON><PERSON><PERSON> (KGR) move to KNA", "status": 4, "priority": 2, "updated_at": "2024-10-30T23:16:35Z"}, "1557": {"ticket_number": 103297, "subject": "Request for <PERSON> : UPS - Uninterruptable Power Supply", "status": 2, "priority": 1, "updated_at": "2024-10-30T09:52:27Z"}, "1558": {"ticket_number": 103293, "subject": "KNA-KIP-Langley Laydown Area - Non IT UPS replacement", "status": 2, "priority": 1, "updated_at": "2024-10-30T21:23:38Z"}, "1559": {"ticket_number": 103208, "subject": "KNAASH-ITTL1: Free Disk Space (Multi Drive) (WMI Free Disk Space (Multi Disk)): 4.98 GB (Free Bytes C:) is below the error limit of 5 GB in Free Bytes C:", "status": 4, "priority": 2, "updated_at": "2024-10-30T22:55:29Z"}, "1560": {"ticket_number": 103191, "subject": "Request for <PERSON> : Laptop - Engineering/Drafting", "status": 5, "priority": 2, "updated_at": "2024-10-30T23:38:57Z"}, "1561": {"ticket_number": 103188, "subject": "Software Installation", "status": 2, "priority": 1, "updated_at": "2024-10-28T17:18:49Z"}, "1562": {"ticket_number": 103180, "subject": "AWIP - Enroll in MDM", "status": 2, "priority": 2, "updated_at": "2024-10-25T23:16:40Z"}, "1563": {"ticket_number": 103142, "subject": "Everyone Email Distribution Lists not working and KB needs updating", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:05:18Z"}, "1564": {"ticket_number": 103034, "subject": "Lansweeper- KNAMAT-ADMIN2- enabled inactive device", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:27Z"}, "1565": {"ticket_number": 103031, "subject": "Pocahontas network equipment - Network Equipment & Accessories", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:46:04Z"}, "1566": {"ticket_number": 103026, "subject": "SAP ERROR - AWIP - SAP BILLING ERRORS", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:40:06Z"}, "1567": {"ticket_number": 103015, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : UPS - Uninterruptable Power Supply", "status": 2, "priority": 2, "updated_at": "2024-10-30T12:39:57Z"}, "1568": {"ticket_number": 103013, "subject": "New Phone for <PERSON><PERSON>", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:48:40Z"}, "1569": {"ticket_number": 102954, "subject": "Phone - Unable to login", "status": 5, "priority": 1, "updated_at": "2024-10-25T12:37:15Z"}, "1570": {"ticket_number": 102922, "subject": "Morin laptops (deland and modesto) 9/23/2024", "status": 2, "priority": 2, "updated_at": "2024-10-28T16:00:54Z"}, "1571": {"ticket_number": 102903, "subject": "Update Teams and OS", "status": 2, "priority": 1, "updated_at": "2024-10-30T15:30:44Z"}, "1572": {"ticket_number": 102901, "subject": "<PERSON> - locked out of Outlook ", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:26Z"}, "1573": {"ticket_number": 102900, "subject": "Rename printers name in the print server - Bristol", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:37:37Z"}, "1574": {"ticket_number": 102893, "subject": "Password reset <PERSON> ", "status": 5, "priority": 2, "updated_at": "2024-10-25T10:50:35Z"}, "1575": {"ticket_number": 102888, "subject": "Sign in blocked ", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:22Z"}, "1576": {"ticket_number": 102835, "subject": "<PERSON><PERSON><PERSON> Account Issues", "status": 5, "priority": 2, "updated_at": "2024-10-24T21:37:18Z"}, "1577": {"ticket_number": 102803, "subject": "patches not installed due to machine being turned off during patch window- 5 machines", "status": 5, "priority": 2, "updated_at": "2024-10-25T18:14:53Z"}, "1578": {"ticket_number": 102754, "subject": "(Merged to  98605) Lansweeper- kna-p-14255- On Network? not scanning", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:03:19Z"}, "1579": {"ticket_number": 102736, "subject": "Request for Winston Card : Mobile Phone - New, Reassign or Replace", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:59:31Z"}, "1580": {"ticket_number": 102658, "subject": "Remediate workstation PCs with Win 11 21H2 (soon to be OOS)", "status": 5, "priority": 1, "updated_at": "2024-10-29T20:35:56Z"}, "1581": {"ticket_number": 102635, "subject": "Cant access email", "status": 5, "priority": 2, "updated_at": "2024-10-24T20:37:28Z"}, "1582": {"ticket_number": 102632, "subject": "Operator -   <PERSON><PERSON><PERSON>", "status": 4, "priority": 2, "updated_at": "2024-10-31T14:23:21Z"}, "1583": {"ticket_number": 102618, "subject": "Access", "status": 4, "priority": 2, "updated_at": "2024-10-31T14:24:21Z"}, "1584": {"ticket_number": 102585, "subject": "Users Online", "status": 5, "priority": 2, "updated_at": "2024-10-26T18:42:57Z"}, "1585": {"ticket_number": 102539, "subject": "Re: RMA Request - KIP Mattoon, IL", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:18:09Z"}, "1586": {"ticket_number": 102348, "subject": "Fwd: Teams", "status": 5, "priority": 2, "updated_at": "2024-10-24T20:37:26Z"}, "1587": {"ticket_number": 102327, "subject": "Re: shire reports", "status": 2, "priority": 1, "updated_at": "2024-10-28T13:21:45Z"}, "1588": {"ticket_number": 102309, "subject": "User Email", "status": 5, "priority": 1, "updated_at": "2024-10-24T20:37:23Z"}, "1589": {"ticket_number": 102305, "subject": "<PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-25T15:37:23Z"}, "1590": {"ticket_number": 102295, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T05:14:11Z"}, "1591": {"ticket_number": 102240, "subject": "Tanium Scanning Issues - KNA-L-8345", "status": 4, "priority": 1, "updated_at": "2024-10-30T14:40:09Z"}, "1592": {"ticket_number": 102206, "subject": "Lansweeper- Printer - PRT-B404 - asset tag, rename, and not seen - Moncton", "status": 5, "priority": 1, "updated_at": "2024-10-31T16:35:37Z"}, "1593": {"ticket_number": 102203, "subject": "Lansweeper- Printer - Canon iPF780 1.10 -asset tag, rename, and not seen", "status": 2, "priority": 1, "updated_at": "2024-10-24T12:42:53Z"}, "1594": {"ticket_number": 102072, "subject": "Request for <PERSON> : Network equipment for Tate Pocahontas - CO - Network Equipment & Accessories", "status": 2, "priority": 2, "updated_at": "2024-10-31T17:24:34Z"}, "1595": {"ticket_number": 102056, "subject": "Qualys Level 4 Internal Vulnerability - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:10:28Z"}, "1596": {"ticket_number": 102053, "subject": "Qualys Level 4 Internal Vulnerability - tcp/25 on EUS2-KNA-DNP1 38695 ~ TLS ROBOT Vulnerability Detected", "status": 2, "priority": 1, "updated_at": "2024-10-28T17:41:33Z"}, "1597": {"ticket_number": 102037, "subject": "Qualys Level 5 Internal Vulnerabilities - 78031 ~ Writeable SNMP Information", "status": 5, "priority": 2, "updated_at": "2024-10-29T12:33:55Z"}, "1598": {"ticket_number": 102024, "subject": "Lansweeper - Unknown Devices", "status": 5, "priority": 1, "updated_at": "2024-10-28T14:16:25Z"}, "1599": {"ticket_number": 101991, "subject": "Exchange Online Public Folders - Requesting Access", "status": 2, "priority": 1, "updated_at": "2024-10-30T23:35:56Z"}, "1600": {"ticket_number": 101967, "subject": "Coil invoice posting - currency", "status": 3, "priority": 1, "updated_at": "2024-10-31T15:10:58Z"}, "1601": {"ticket_number": 101926, "subject": "TAF-STP-IDF2-UPS: APC UPS Health (SNMP Custom Advanced): Warning caused by lookup value 'invalid Test' in channel 'Last Battery Test Status' - 4 m 47 s  (Run Time Remaining) is below the error limit of 5 m  in Run Time Remaining. Battery Low, Shutdown ...", "status": 5, "priority": 3, "updated_at": "2024-10-30T18:56:44Z"}, "1602": {"ticket_number": 101770, "subject": "Upgrade <PERSON><PERSON><PERSON><PERSON>'s La<PERSON><PERSON> ", "status": 5, "priority": 1, "updated_at": "2024-10-30T22:37:05Z"}, "1603": {"ticket_number": 101764, "subject": "Request for <PERSON> by <PERSON> : Stormwind Studios - License for Online IT Training", "status": 4, "priority": 2, "updated_at": "2024-10-29T18:53:30Z"}, "1604": {"ticket_number": 101634, "subject": "Need new shire pc", "status": 2, "priority": 3, "updated_at": "2024-10-30T17:48:27Z"}, "1605": {"ticket_number": 101620, "subject": "Cell Phone Upgrade - <PERSON> & <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:47:17Z"}, "1606": {"ticket_number": 101616, "subject": "iPads for shipping", "status": 2, "priority": 1, "updated_at": "2024-10-30T13:20:22Z"}, "1607": {"ticket_number": 101534, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : IT Systems - Daily Lansweeper Asset Review & Maintenance", "status": 2, "priority": 2, "updated_at": "2024-10-30T14:39:11Z"}, "1608": {"ticket_number": 101466, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T04:33:00Z"}, "1609": {"ticket_number": 101465, "subject": "Terminated account - Immediate action required | <PERSON>", "status": 5, "priority": 1, "updated_at": "2024-10-30T04:26:36Z"}, "1610": {"ticket_number": 101448, "subject": "Lansweeper- Printer - LBL-B028- asset tag, rename, and not seen - VIC", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:51:21Z"}, "1611": {"ticket_number": 101444, "subject": "EAS - Lansweeper- Printer -HPF98150- asset tag, rename, and not seen", "status": 2, "priority": 1, "updated_at": "2024-10-24T14:50:07Z"}, "1612": {"ticket_number": 101435, "subject": "Lansweeper- Printer - NPI7993C2- asset tag, rename ,and not seen", "status": 5, "priority": 1, "updated_at": "2024-10-28T21:00:41Z"}, "1613": {"ticket_number": 101354, "subject": "Asset # 8842 Unlock and Updates", "status": 5, "priority": 1, "updated_at": "2024-10-29T14:00:54Z"}, "1614": {"ticket_number": 101304, "subject": "Deland Generic Account - AWS Machine", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:58:09Z"}, "1615": {"ticket_number": 101288, "subject": "EAS - Lansweeper- Printer - KONICA MINOLTA bizhub 5000i- asset tag and rename", "status": 3, "priority": 1, "updated_at": "2024-10-28T20:17:38Z"}, "1616": {"ticket_number": 101287, "subject": "EAS - Lansweeper- Printer -KMNB42200C8B51F- asset tag and rename", "status": 3, "priority": 1, "updated_at": "2024-10-28T20:16:24Z"}, "1617": {"ticket_number": 101285, "subject": "EAS - Lansweeper- Printer- NPI59B524- asset tag and rename", "status": 3, "priority": 1, "updated_at": "2024-10-28T20:17:20Z"}, "1618": {"ticket_number": 101234, "subject": "Qualys Level 4 Internal Vulnerability -38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-24T17:13:07Z"}, "1619": {"ticket_number": 101229, "subject": "Qualys Level 5 Internal Vulnerability - 10.177.106.5 - 78031 ~ Writeable SNMP Information", "status": 2, "priority": 1, "updated_at": "2024-10-30T12:50:32Z"}, "1620": {"ticket_number": 101119, "subject": "knacol-prsv1 - remove printers (Columbus)", "status": 2, "priority": 1, "updated_at": "2024-10-31T18:15:34Z"}, "1621": {"ticket_number": 101081, "subject": "Lansweeper- kna-l-7594- Not seen for 30+ days", "status": 5, "priority": 1, "updated_at": "2024-10-28T17:16:38Z"}, "1622": {"ticket_number": 100979, "subject": "laptop", "status": 2, "priority": 2, "updated_at": "2024-10-30T18:29:12Z"}, "1623": {"ticket_number": 100957, "subject": "Request for <PERSON> : Printer", "status": 2, "priority": 2, "updated_at": "2024-10-24T16:13:05Z"}, "1624": {"ticket_number": 100937, "subject": "Request for <PERSON> : Access & Permissions - SharePoint & Teams and Office 365 Outlook Groups", "status": 5, "priority": 2, "updated_at": "2024-10-30T17:37:47Z"}, "1625": {"ticket_number": 100768, "subject": "Re: 2024 VEEAM Renewal - Due, November 23rd", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:35:59Z"}, "1626": {"ticket_number": 100743, "subject": "Add LBS to coil layout", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:37:26Z"}, "1627": {"ticket_number": 100731, "subject": "[REP] Computer: Active Part of workgroup - Not Part of Domain (customized)", "status": 2, "priority": 3, "updated_at": "2024-10-29T12:38:59Z"}, "1628": {"ticket_number": 100628, "subject": "Lansweeper- TV on Network", "status": 2, "priority": 1, "updated_at": "2024-10-30T16:31:18Z"}, "1629": {"ticket_number": 100454, "subject": "lansweeper- printer serial: ADXM013000869", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:40:15Z"}, "1630": {"ticket_number": 100445, "subject": "knafon-admin1 - Not seen for 30 days", "status": 4, "priority": 2, "updated_at": "2024-10-29T22:45:57Z"}, "1631": {"ticket_number": 100425, "subject": "2 PC’s for Monterrey’s new AWS SPC System.", "status": 2, "priority": 1, "updated_at": "2024-10-29T23:21:50Z"}, "1632": {"ticket_number": 100403, "subject": "Creating Generic account for Monterrey - Operator Account", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:30:16Z"}, "1633": {"ticket_number": 100394, "subject": "Fw: Dri-Design Vendor was hacked", "status": 3, "priority": 2, "updated_at": "2024-10-30T18:22:32Z"}, "1634": {"ticket_number": 100393, "subject": " KNA - TATE Jessup and Redlion - Equipment Disposal", "status": 5, "priority": 2, "updated_at": "2024-10-29T20:12:26Z"}, "1635": {"ticket_number": 100215, "subject": "Qualys Level 5 Internal Vulnerability - 78031 ~ Writeable SNMP Information", "status": 2, "priority": 2, "updated_at": "2024-10-30T17:52:38Z"}, "1636": {"ticket_number": 100203, "subject": "SAP Migration into Excel", "status": 2, "priority": 1, "updated_at": "2024-10-28T11:42:56Z"}, "1637": {"ticket_number": 100161, "subject": "Stock Desktops/Laptops Request", "status": 2, "priority": 2, "updated_at": "2024-10-30T15:02:23Z"}, "1638": {"ticket_number": 100007, "subject": "SEC-VMNSP-24 Training and Testing - Phishing Test", "status": 3, "priority": 1, "updated_at": "2024-10-24T20:32:42Z"}, "1639": {"ticket_number": 99998, "subject": "Monthly Reconciliation Between Asset Register and MDM", "status": 2, "priority": 2, "updated_at": "2024-10-29T14:33:27Z"}, "1640": {"ticket_number": 99955, "subject": "Endpoint Encryption - PC out of warranty", "status": 2, "priority": 2, "updated_at": "2024-10-30T19:32:50Z"}, "1641": {"ticket_number": 99921, "subject": "Disk failure on KNAMON-VMHOST1 ESXi server", "status": 5, "priority": 2, "updated_at": "2024-10-28T18:56:59Z"}, "1642": {"ticket_number": 99657, "subject": "SharePoint access with company provided iPhone and iPad", "status": 5, "priority": 1, "updated_at": "2024-10-25T21:37:19Z"}, "1643": {"ticket_number": 99610, "subject": "Migration from KAF to KNA - <PERSON><PERSON> (Mon, 2024, Oct 21) - Full-Time Employee KNA - <PERSON><PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-24T17:15:13Z"}, "1644": {"ticket_number": 99575, "subject": "(Merged to  106142) <PERSON><PERSON> - 01OCT24 - Laptop - Standard (Non-Bundled)", "status": 5, "priority": 2, "updated_at": "2024-10-29T15:37:35Z"}, "1645": {"ticket_number": 99504, "subject": " Generating  generic account for operators at Caledon - Laydown Operator Account", "status": 5, "priority": 1, "updated_at": "2024-10-30T12:23:54Z"}, "1646": {"ticket_number": 99439, "subject": "Replacing SNMP Version 1/2c", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:08:43Z"}, "1647": {"ticket_number": 99387, "subject": "Waterjet Software", "status": 2, "priority": 1, "updated_at": "2024-10-29T13:28:31Z"}, "1648": {"ticket_number": 99385, "subject": "Oisin<PERSON><PERSON><PERSON><PERSON> - AD Account Migration KNA to KDF - 30SEP24", "status": 5, "priority": 1, "updated_at": "2024-10-29T15:38:54Z"}, "1649": {"ticket_number": 99318, "subject": "New Phones", "status": 2, "priority": 1, "updated_at": "2024-10-31T17:28:05Z"}, "1650": {"ticket_number": 99299, "subject": "Qualys Level 4 Internal Vulnerability - 192.168.154.252 - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-24T15:37:18Z"}, "1651": {"ticket_number": 99163, "subject": "Request for Rob Lickers : Laptop - Standard (Non-Bundled)", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:24:10Z"}, "1652": {"ticket_number": 99129, "subject": "Domain expiry reminder ", "status": 5, "priority": 2, "updated_at": "2024-10-25T14:29:58Z"}, "1653": {"ticket_number": 99112, "subject": "Request for <PERSON>: Salesforce Microsoft Exchange Connection | Access & Permissions - Database DB", "status": 2, "priority": 2, "updated_at": "2024-10-30T21:30:59Z"}, "1654": {"ticket_number": 99081, "subject": "Repair laptop kna-l-12398, still under warranty", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:32:06Z"}, "1655": {"ticket_number": 98804, "subject": "Review all the UPS in KNA-VBP locations for preparing CAPEX. ", "status": 5, "priority": 2, "updated_at": "2024-10-30T18:14:45Z"}, "1656": {"ticket_number": 98799, "subject": "Request for Margaret Thames, PHR : Tablet Shared iPad", "status": 5, "priority": 2, "updated_at": "2024-10-24T19:37:29Z"}, "1657": {"ticket_number": 98665, "subject": "Request for <PERSON> : Laptop - Standard (Non-Bundled)", "status": 5, "priority": 2, "updated_at": "2024-10-29T23:47:02Z"}, "1658": {"ticket_number": 98661, "subject": "Request for <PERSON> : Switch replacement: Mexico - Network Equipment & Accessories", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:16:04Z"}, "1659": {"ticket_number": 98649, "subject": "Fix all workstation PCs with a critical patch older than 90 days not applied", "status": 2, "priority": 3, "updated_at": "2024-10-30T14:36:22Z"}, "1660": {"ticket_number": 98605, "subject": "Qualys Level 3 Internal Vulnerability - Printers - 48169 ~ Remote Management Service Accepting Unencrypted Credentials Detected (FTP)", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:03:18Z"}, "1661": {"ticket_number": 98506, "subject": "knabri-admin1 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-31T00:59:12Z"}, "1662": {"ticket_number": 98504, "subject": "knaeas-admin1 - Not seen for 30 days", "status": 2, "priority": 1, "updated_at": "2024-10-30T22:36:24Z"}, "1663": {"ticket_number": 98478, "subject": "Qualys Vulnerability - KNALIT-HMIVM1 - ************ - 38695 ~ TLS ROBOT Vulnerability Detected", "status": 5, "priority": 1, "updated_at": "2024-10-28T11:56:46Z"}, "1664": {"ticket_number": 98430, "subject": "Laptop screen dead", "status": 2, "priority": 1, "updated_at": "2024-10-29T15:57:51Z"}, "1665": {"ticket_number": 98410, "subject": "No Exclaimer Email Signatures for Freshservice | Exchange Online Rule Exception for Exclaimer Cloud", "status": 5, "priority": 2, "updated_at": "2024-10-25T21:37:19Z"}, "1666": {"ticket_number": 98181, "subject": "Request for <PERSON> : Laptop - Engineering/Drafting (Non-Bundled)", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:34:47Z"}, "1667": {"ticket_number": 98125, "subject": "Macros in Excel blocked after SharePoint migration - require full access", "status": 2, "priority": 1, "updated_at": "2024-10-30T17:39:59Z"}, "1668": {"ticket_number": 98084, "subject": "Ekahau 2024 Renewal - November 13th Due Date", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:38:28Z"}, "1669": {"ticket_number": 98074, "subject": "Update Power BI Desktop on VM *************", "status": 2, "priority": 1, "updated_at": "2024-10-29T15:13:24Z"}, "1670": {"ticket_number": 98061, "subject": "Request for <PERSON> : Cellular Signal Booster - Business Office", "status": 2, "priority": 2, "updated_at": "2024-10-30T18:27:38Z"}, "1671": {"ticket_number": 97899, "subject": "Crowdstrike - Monthly Health Review - Sensor Update", "status": 5, "priority": 1, "updated_at": "2024-10-31T14:45:38Z"}, "1672": {"ticket_number": 97791, "subject": "Below iPads are not in ABM, intune and LS is missing SN number on them.", "status": 2, "priority": 1, "updated_at": "2024-10-30T20:26:01Z"}, "1673": {"ticket_number": 97744, "subject": "Tanium Patching -  2024-07 Cumulative Update for .NET Framework 3.5 and 4.8.1 for Windows 11, version 23H2 for x64 (KB5039895)", "status": 5, "priority": 1, "updated_at": "2024-10-30T20:37:29Z"}, "1674": {"ticket_number": 97743, "subject": "Tanium Patching -  2024-07 Cumulative Update for .NET Framework 3.5, 4.8 and 4.8.1 for Windows 10 Version 22H2 for x64 (KB5041019)", "status": 5, "priority": 1, "updated_at": "2024-10-30T21:37:32Z"}, "1675": {"ticket_number": 97742, "subject": "Tanium Patching -  2024-07 Cumulative Update for Windows 10 Version 22H2 for x64-based Systems (KB5040427)", "status": 4, "priority": 1, "updated_at": "2024-10-29T19:29:05Z"}, "1676": {"ticket_number": 97741, "subject": "Tanium Patching -  2024-07 Cumulative Update for Windows 11 Version 23H2 for x64-based Systems (KB5040442)", "status": 4, "priority": 1, "updated_at": "2024-10-29T18:45:25Z"}, "1677": {"ticket_number": 97591, "subject": "Spreadsheet Server", "status": 3, "priority": 1, "updated_at": "2024-10-29T20:13:23Z"}, "1678": {"ticket_number": 97087, "subject": "Enable Transcription in Teams", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:06:55Z"}, "1679": {"ticket_number": 96980, "subject": "Access to Hans<PERSON><PERSON>", "status": 4, "priority": 1, "updated_at": "2024-10-30T13:30:23Z"}, "1680": {"ticket_number": 96795, "subject": "Printer for New QC Office", "status": 4, "priority": 1, "updated_at": "2024-10-30T20:12:41Z"}, "1681": {"ticket_number": 96618, "subject": "Tanium Patching -  KNAFON-ADMIN1", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:30:04Z"}, "1682": {"ticket_number": 96502, "subject": "Sharepoint site storage full ", "status": 5, "priority": 2, "updated_at": "2024-10-31T15:37:35Z"}, "1683": {"ticket_number": 96256, "subject": "(Merged to  69486) SEC-CISM-01: Conduct two Annual Scenario / Tabletop Exercises of the CIR", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:25:53Z"}, "1684": {"ticket_number": 96050, "subject": "(Merged to  99461) Tanium - Scanning issues", "status": 5, "priority": 1, "updated_at": "2024-10-24T16:40:11Z"}, "1685": {"ticket_number": 96012, "subject": "Create an SOP for Asset Management - IP Reconciliation", "status": 2, "priority": 1, "updated_at": "2024-10-29T16:08:01Z"}, "1686": {"ticket_number": 95890, "subject": "(Merged to  85503) Tanium Patching -  KNAMON-ADMINPC1", "status": 5, "priority": 1, "updated_at": "2024-10-29T17:51:44Z"}, "1687": {"ticket_number": 95726, "subject": "secondary monitors not connecting, nor recognized on from the laptop", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:10:12Z"}, "1688": {"ticket_number": 95725, "subject": "Request for <PERSON> : Printer", "status": 2, "priority": 2, "updated_at": "2024-10-30T21:01:29Z"}, "1689": {"ticket_number": 95521, "subject": "Autodesk Accounts - SSO Go live TBD", "status": 2, "priority": 1, "updated_at": "2024-10-31T16:30:00Z"}, "1690": {"ticket_number": 95413, "subject": "Request for <PERSON><PERSON><PERSON><PERSON> : Change passwords for all the Teams room (Logitech) to be the same |User Account Maintenance - AD, O365 Data", "status": 5, "priority": 2, "updated_at": "2024-10-30T20:08:26Z"}, "1691": {"ticket_number": 95392, "subject": "Qualys Level 4 Internal Vulnerability -172.16.114.9 - 42430 ~ OpenSSL Memory Leak Vulnerability (Heartbleed Bug)", "status": 5, "priority": 1, "updated_at": "2024-10-30T19:51:09Z"}, "1692": {"ticket_number": 95389, "subject": "Qualys Level 4 Internal Vulnerability - 38863 ~ Weak SSL/TLS Key Exchange", "status": 5, "priority": 1, "updated_at": "2024-10-30T13:02:27Z"}, "1693": {"ticket_number": 95222, "subject": "Lansweeper - \"Broken\" - KNA-L-13643", "status": 5, "priority": 1, "updated_at": "2024-10-29T19:31:30Z"}, "1694": {"ticket_number": 95020, "subject": "Request for <PERSON>: AD Network Account Creation - <PERSON>", "status": 5, "priority": 2, "updated_at": "2024-10-28T20:23:26Z"}, "1695": {"ticket_number": 95005, "subject": "Meeting rooms can be hard to lookup, searching by the expected name doesn't always work", "status": 5, "priority": 1, "updated_at": "2024-10-29T16:05:52Z"}, "1696": {"ticket_number": 94792, "subject": "Tanium Patching -  KNAFON-ADMIN1", "status": 5, "priority": 1, "updated_at": "2024-10-30T17:30:26Z"}, "1697": {"ticket_number": 94295, "subject": "GR55 Cost Center Mapping ", "status": 3, "priority": 1, "updated_at": "2024-10-31T13:56:17Z"}, "1698": {"ticket_number": 93729, "subject": "Software Removal - Web Companion", "status": 2, "priority": 1, "updated_at": "2024-10-30T14:32:20Z"}, "1699": {"ticket_number": 93722, "subject": "Request for <PERSON> : SharePoint - New Site", "status": 2, "priority": 2, "updated_at": "2024-10-30T20:09:50Z"}, "1700": {"ticket_number": 92747, "subject": "Server Cabinet by Customer Service Beeping", "status": 5, "priority": 1, "updated_at": "2024-10-24T13:49:24Z"}, "1701": {"ticket_number": 92692, "subject": " Desktop - Standard Replacement for Tate", "status": 2, "priority": 2, "updated_at": "2024-10-25T14:29:00Z"}, "1702": {"ticket_number": 92592, "subject": "On-Boarding: SOP Creation", "status": 2, "priority": 2, "updated_at": "2024-10-29T14:31:11Z"}, "1703": {"ticket_number": 91464, "subject": "Request for <PERSON> : Printer - MAC", "status": 5, "priority": 2, "updated_at": "2024-10-25T17:37:45Z"}, "1704": {"ticket_number": 91433, "subject": "Cellphone portal corrections", "status": 5, "priority": 1, "updated_at": "2024-10-29T23:20:59Z"}, "1705": {"ticket_number": 89868, "subject": "Request for <PERSON> : Logitech Sync Plus for Alerting", "status": 2, "priority": 2, "updated_at": "2024-10-30T14:09:06Z"}, "1706": {"ticket_number": 89589, "subject": "Qualys Level 5 Internal Vulnerability - 10.177.116.18- 78031 ~ Writeable SNMP Information", "status": 5, "priority": 1, "updated_at": "2024-10-30T16:48:31Z"}, "1707": {"ticket_number": 87410, "subject": "New IRW West Team for teams", "status": 5, "priority": 1, "updated_at": "2024-10-26T14:39:32Z"}, "1708": {"ticket_number": 85503, "subject": "Tanium Patching -  KNAMON-ADMINPC1", "status": 5, "priority": 1, "updated_at": "2024-10-30T15:09:18Z"}, "1709": {"ticket_number": 85053, "subject": "Power BI Capacity Licence request for Tate Access Floors", "status": 2, "priority": 2, "updated_at": "2024-10-30T21:18:42Z"}, "1710": {"ticket_number": 84651, "subject": "Request for <PERSON> : Laptop, headsets and monitors", "status": 5, "priority": 2, "updated_at": "2024-10-29T16:43:03Z"}, "1711": {"ticket_number": 84304, "subject": "Request for <PERSON> : Spreadsheet Server | Solution Provisioning", "status": 3, "priority": 2, "updated_at": "2024-10-29T13:29:27Z"}, "1712": {"ticket_number": 83292, "subject": "<PERSON> - New Drafting Computer", "status": 2, "priority": 1, "updated_at": "2024-10-30T18:21:55Z"}, "1713": {"ticket_number": 82988, "subject": "Document request for How to be a badass at helpdesk", "status": 2, "priority": 2, "updated_at": "2024-10-30T21:23:38Z"}, "1714": {"ticket_number": 82969, "subject": "Document request for Current Imaging process", "status": 2, "priority": 2, "updated_at": "2024-10-31T14:41:58Z"}, "1715": {"ticket_number": 78886, "subject": "Request for <PERSON><PERSON> : Mimecast - Continuity IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:36:51Z"}, "1716": {"ticket_number": 78885, "subject": "Request for <PERSON><PERSON> : Mimecast - Reporting IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:36:49Z"}, "1717": {"ticket_number": 78884, "subject": "Request for <PERSON><PERSON> Gobel : Mimecast - URL Protection configuration IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:36:49Z"}, "1718": {"ticket_number": 78883, "subject": "Request for <PERSON><PERSON> Go<PERSON> : Mimecast - Attachment Management IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-29T14:36:48Z"}, "1719": {"ticket_number": 78882, "subject": "Request for <PERSON><PERSON> : Mimecast - Internal directories IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:48:41Z"}, "1720": {"ticket_number": 78881, "subject": "Request for <PERSON><PERSON> : Mimecast - Password complexity IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:46:36Z"}, "1721": {"ticket_number": 78880, "subject": "Request for <PERSON><PERSON> : Mimecast - Account contact IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:46:15Z"}, "1722": {"ticket_number": 78879, "subject": "Request for <PERSON><PERSON> Go<PERSON> : Mimecast - System Notifications IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:16:26Z"}, "1723": {"ticket_number": 78878, "subject": "Request for <PERSON><PERSON> : Mimecast - User access and Permissions IT Device and Data Maintenance", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:15:56Z"}, "1724": {"ticket_number": 78872, "subject": "Request for <PERSON><PERSON> : MimeCast - DNS Authentication Inbound not configured to check DKIM and DMARC IT Device and Data Maintenance", "status": 5, "priority": 3, "updated_at": "2024-10-27T19:37:07Z"}, "1725": {"ticket_number": 78870, "subject": "Request for <PERSON><PERSON> : MimeCast - No Anti spoofing policies IT Device and Data Maintenance", "status": 5, "priority": 3, "updated_at": "2024-10-25T16:48:50Z"}, "1726": {"ticket_number": 78553, "subject": "SEC-VMNSP-20 - Bi-Annual Firewall User Access Review", "status": 5, "priority": 1, "updated_at": "2024-10-25T14:30:24Z"}, "1727": {"ticket_number": 77853, "subject": "Qualys Level 3 Internal Vulnerability - 86714 ~ Web Server Vulnerable to Redirection Page Cross-Site Scripting Attacks", "status": 5, "priority": 1, "updated_at": "2024-10-25T18:37:23Z"}, "1728": {"ticket_number": 77249, "subject": "Physically segmented LAN for MetFab machines", "status": 2, "priority": 1, "updated_at": "2024-10-31T14:00:49Z"}, "1729": {"ticket_number": 75553, "subject": "Qualys Level 3 Internal Vulnerability - 38739 ~ Deprecated SSH Cryptographic Settings", "status": 3, "priority": 1, "updated_at": "2024-10-28T17:46:14Z"}, "1730": {"ticket_number": 69486, "subject": "SEC-CISM-01: Conduct two Annual Scenario / Tabletop Exercises of the CIR", "status": 2, "priority": 1, "updated_at": "2024-10-29T17:25:49Z"}, "1731": {"ticket_number": 66034, "subject": "FW: Scan production sheets", "status": 5, "priority": 1, "updated_at": "2024-10-31T11:54:42Z"}, "1732": {"ticket_number": 63747, "subject": "How do we ship items direct through SAP", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:17:45Z"}, "1733": {"ticket_number": 61230, "subject": "Canada KA IT Admin PC Replacement", "status": 2, "priority": 2, "updated_at": "2024-10-24T14:21:33Z"}, "1734": {"ticket_number": 61226, "subject": "US KA IT Admin PC Replacement", "status": 2, "priority": 2, "updated_at": "2024-10-24T14:21:03Z"}, "1735": {"ticket_number": 60196, "subject": "Qualys Level 5 Vulnerability - Siemens AG PLC - ************* - Siemens AG *************", "status": 3, "priority": 3, "updated_at": "2024-10-29T18:13:32Z"}, "1736": {"ticket_number": 53304, "subject": "Hybrid Orders issue with Accessories not included. ", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:16:43Z"}, "1737": {"ticket_number": 40880, "subject": "SAP Changing Postal Code - Order 1-82354", "status": 5, "priority": 1, "updated_at": "2024-10-25T13:20:09Z"}, "1738": {"ticket_number": 22203, "subject": "FW: Global Sanctions -List of countries block", "status": 5, "priority": 1, "updated_at": "2024-10-31T12:37:00Z"}, "1739": {"ticket_number": 9589, "subject": "Request for <PERSON> : Salesforce CRM - Termination", "status": 5, "priority": 2, "updated_at": "2024-10-24T16:37:28Z"}}}