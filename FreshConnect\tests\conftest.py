"""
Common fixtures for pytest.
"""

import os
import json
import pytest
import tempfile
from unittest.mock import MagicMock, patch

# Add path to the freshconnect package
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import freshconnect modules
from freshconnect.api.freshservice import FreshServiceClient
from freshconnect.data.database import Database
from freshconnect.data.backends.tinydb_backend import TinyDBBackend


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    class MockResponse:
        def __init__(self, json_data, status_code=200, headers=None):
            self.json_data = json_data
            self.status_code = status_code
            self.headers = headers or {}
            self.text = json.dumps(json_data)

        def json(self):
            return self.json_data

        def raise_for_status(self):
            if self.status_code >= 400:
                raise Exception(f"HTTP Error: {self.status_code}")

    return MockResponse


@pytest.fixture
def mock_api_client():
    """Create a mock FreshService API client."""
    client = MagicMock(spec=FreshServiceClient)
    client.domain = "test.freshservice.com"
    client.api_key = "test_api_key"
    client.base_url = "https://test.freshservice.com/api/v2"

    # Mock the get_tickets method
    client.get_tickets.return_value = {
        "tickets": [
            {
                "id": 1,
                "subject": "Test Ticket 1",
                "description": "This is a test ticket",
                "status": 2,
                "priority": 1,
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z"
            },
            {
                "id": 2,
                "subject": "Test Ticket 2",
                "description": "This is another test ticket",
                "status": 3,
                "priority": 2,
                "created_at": "2023-01-03T00:00:00Z",
                "updated_at": "2023-01-04T00:00:00Z"
            }
        ]
    }

    # Mock the get_ticket method
    client.get_ticket.return_value = {
        "ticket": {
            "id": 1,
            "subject": "Test Ticket 1",
            "description": "This is a test ticket",
            "status": 2,
            "priority": 1,
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-02T00:00:00Z"
        }
    }

    # Mock the get_ticket_history method
    client.get_ticket_history.return_value = {
        "conversations": [
            {
                "id": 1,
                "body": "This is a test conversation",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z",
                "actor": {
                    "id": 1,
                    "name": "Test Agent"
                }
            }
        ]
    }

    # Mock the get_all_pages method
    def mock_get_all_pages(endpoint, **kwargs):
        if endpoint == "tickets":
            return [
                {
                    "id": 1,
                    "subject": "Test Ticket 1"
                },
                {
                    "id": 2,
                    "subject": "Test Ticket 2"
                }
            ]
        else:  # Default for service_catalog/items
            return [
                {
                    "id": 1,
                    "name": "Test Service Item 1",
                    "description": "This is a test service item",
                    "category": {
                        "id": 1,
                        "name": "Test Category"
                    },
                    "visibility": "all"
                },
                {
                    "id": 2,
                    "name": "Test Service Item 2",
                    "description": "This is another test service item",
                    "category": {
                        "id": 2,
                        "name": "Another Category"
                    },
                    "visibility": "agents"
                }
            ]

    client.get_all_pages.side_effect = mock_get_all_pages

    return client


@pytest.fixture
def temp_db_file():
    """Create a temporary database file."""
    fd, path = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    yield path
    try:
        os.unlink(path)
    except (PermissionError, FileNotFoundError):
        # On Windows, sometimes the file is still in use
        # Just log it and continue
        pass


@pytest.fixture
def mock_db(temp_db_file):
    """Create a mock database."""
    db = TinyDBBackend(temp_db_file)

    # Add some test data
    db.clear_and_insert_items('tickets', [
        {
            "id": 1,
            "subject": "Test Ticket 1",
            "description": "This is a test ticket",
            "status": 2,
            "priority": 1,
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-02T00:00:00Z"
        },
        {
            "id": 2,
            "subject": "Test Ticket 2",
            "description": "This is another test ticket",
            "status": 3,
            "priority": 2,
            "created_at": "2023-01-03T00:00:00Z",
            "updated_at": "2023-01-04T00:00:00Z"
        }
    ])

    db.clear_and_insert_items('ticket_history', [
        {
            "ticket_id": 1,
            "ticket": {
                "id": 1,
                "subject": "Test Ticket 1"
            },
            "history": [
                {
                    "id": 1,
                    "body": "This is a test conversation",
                    "created_at": "2023-01-01T00:00:00Z",
                    "actor": {
                        "id": 1,
                        "name": "Test Agent"
                    },
                    "content": "set group as Test Group"
                }
            ],
            "fetched_at": "2023-01-02T00:00:00Z"
        }
    ])

    db.clear_and_insert_items('service_items', [
        {
            "id": 1,
            "name": "Test Service Item 1",
            "description": "This is a test service item",
            "category": {
                "id": 1,
                "name": "Test Category"
            },
            "visibility": "all"
        },
        {
            "id": 2,
            "name": "Test Service Item 2",
            "description": "This is another test service item",
            "category": {
                "id": 2,
                "name": "Another Category"
            },
            "visibility": "agents"
        }
    ])

    return db


@pytest.fixture
def mock_env_vars():
    """Mock environment variables."""
    with patch.dict(os.environ, {
        'FRESHSERVICE_DOMAIN': 'test.freshservice.com',
        'FRESHSERVICE_API_KEY': 'test_api_key',
        'DB_TYPE': 'tinydb',
        'DB_PATH': 'test_db.json'
    }):
        yield
