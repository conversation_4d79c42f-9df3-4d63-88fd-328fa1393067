import os
import time
from base64 import b64encode
import requests
from datetime import datetime
from dbFunctions import DatabaseManager, initialize_db


class TicketHistoryFetcher:
    def __init__(self, domain, api_key):
        """
        Initialize the FreshService ticket history fetcher
        
        Args:
            domain (str): Your FreshService domain (e.g., 'company.freshservice.com')
            api_key (str): Your FreshService API key
        """
        self.base_url = f"https://{domain}/api/v2"
        self.auth_token = b64encode(f"{api_key}:X".encode()).decode()
        self.headers = {
            'Authorization': f'Basic {self.auth_token}',
            'Content-Type': 'application/json'
        }

    def get_ticket_history(self, ticket_id):
        """
        Fetch the history for a specific ticket
        
        Args:
            ticket_id (int): The ticket ID to fetch history for
            
        Returns:
            list: List of history entries
        """
        url = f"{self.base_url}/tickets/{ticket_id}/activities"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            # Check rate limiting
            remaining = int(response.headers.get('X-Ratelimit-Remaining', 0))
            if remaining < 50:
                print(f"Rate limit low ({remaining}), pausing for 1 second...")
                time.sleep(1)
                
            history_data = response.json()
            
            # Ensure we're working with the activities list from the response
            if 'activities' in history_data:
                activities = history_data['activities']
                # Format each activity entry
                formatted_activities = []
                for activity in activities:
                    formatted_activity = {
                        'actor': {
                            'id': activity.get('actor', {}).get('id'),
                            'name': activity.get('actor', {}).get('name')
                        },
                        'content': activity.get('content'),
                        'sub_contents': activity.get('sub_contents', []),
                        'created_at': activity.get('created_at')
                    }
                    formatted_activities.append(formatted_activity)
                return formatted_activities
            return []
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching history for ticket {ticket_id}: {str(e)}")
            return []

def get_env_config():
    """
    Retrieve configuration from environment variables with fallback to .env file
    
    Returns:
        tuple: (domain, api_key)
    """
    # Try getting from environment variables first
    domain = os.getenv('FRESHSERVICE_DOMAIN')
    api_key = os.getenv('FRESHSERVICE_API_KEY')
    
    # If not found in environment, try loading from .env file
    if not (domain and api_key):
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            domain = os.getenv('FRESHSERVICE_DOMAIN')
            api_key = os.getenv('FRESHSERVICE_API_KEY')
        except ImportError:
            print("python-dotenv not installed. Using only environment variables.")
    
    # Validate configuration
    if not domain:
        raise ValueError("FRESHSERVICE_DOMAIN environment variable not set")
    
    if not api_key:
        raise ValueError("FRESHSERVICE_API_KEY environment variable not set")
    
    return domain, api_key



def main():
    print("Starting ticket history fetch process...")
    
    # Initialize database managers
    print("Initializing databases...")
    db_manager = initialize_db('tickets.json', encoding='utf-8')
    history_db = initialize_db('ticketHistory.json', encoding='utf-8')
    
    # Get environment configuration
    print("Getting environment configuration...")
    
    domain, api_key = get_env_config()
    
    # Initialize fetcher
    print("Initializing ticket fetcher...")
    fetcher = TicketHistoryFetcher(domain, api_key)
    
    try:
        # Get all tickets from the database
        print("Attempting to read tickets from database...")
        try:
            tickets = db_manager.get_table('tickets')
        except UnicodeDecodeError:
            print("Warning: Encountered encoding issue, trying with UTF-8 encoding...")
            # Force database manager to use UTF-8
            db_manager.encoding = 'utf-8'
            tickets = db_manager.get_table('tickets')
        
        if not tickets:
            print("Error: No tickets found in database!")
            print("Database location:", os.path.join(os.path.dirname(__file__), 'Storage', 'db.json'))
            return
            
        print(f"Found {len(tickets)} tickets in database")
        
        # Fetch and store history for each ticket
        all_history = []
        for ticket in tickets:
            ticket_number = ticket.get('ticket_number')
            if not ticket_number:
                print(f"Warning: Ticket missing ticket_number: {ticket}")
                continue
            print(f"Fetching history for ticket {ticket_number}")
            
            history_entries = fetcher.get_ticket_history(ticket_number)
            all_history.extend(history_entries)
        
        # Store all history entries
        if all_history:
            # Convert list to dictionary with incrementing keys
            history_dict = {'history': {
                str(i+1): entry for i, entry in enumerate(all_history)
            }}
            items_stored = history_db.insert_items('history', [history_dict])
            print(f"Successfully stored {len(all_history)} history entries")
        
    except Exception as e:
        print(f"Error processing ticket histories: {str(e)}")

if __name__ == "__main__":
    main()
