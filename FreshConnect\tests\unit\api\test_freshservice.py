"""
Unit tests for the FreshService API client.
"""

import pytest
from unittest.mock import patch, MagicMock
import requests
import json

from freshconnect.api.freshservice import FreshServiceClient


@pytest.mark.unit
@pytest.mark.api
class TestFreshServiceClient:
    """Tests for the FreshServiceClient class."""

    def test_init(self):
        """Test initialization of the client."""
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        assert client.domain == "test.freshservice.com"
        assert client.api_key == "test_api_key"
        assert client.base_url == "https://test.freshservice.com/api/v2"

    def test_init_with_missing_api_key(self):
        """Test initialization with missing API key."""
        with pytest.raises(ValueError):
            FreshServiceClient(domain="test.freshservice.com", api_key=None)

    @patch('requests.Session.get')
    def test_get_tickets(self, mock_get, mock_response):
        """Test getting tickets."""
        # Setup mock response
        mock_response_obj = mock_response({
            "tickets": [
                {
                    "id": 1,
                    "subject": "Test Ticket 1"
                }
            ]
        })
        mock_get.return_value = mock_response_obj

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client.get_tickets(updated_since="2023-01-01T00:00:00Z")

        # Verify result
        assert "tickets" in result
        assert len(result["tickets"]) == 1
        assert result["tickets"][0]["id"] == 1

        # Verify the request was made correctly
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        # The URL is now passed as a positional argument in the _make_request method
        assert "tickets" in args[0]
        assert kwargs["params"]["updated_since"] == "2023-01-01T00:00:00Z"

    @patch('requests.Session.get')
    def test_get_ticket(self, mock_get, mock_response):
        """Test getting a specific ticket."""
        # Setup mock response
        mock_response_obj = mock_response({
            "ticket": {
                "id": 1,
                "subject": "Test Ticket 1"
            }
        })
        mock_get.return_value = mock_response_obj

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client.get_ticket(1)

        # Verify result
        assert "ticket" in result
        assert result["ticket"]["id"] == 1

        # Verify the request was made correctly
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        # The URL is now passed as a positional argument in the _make_request method
        assert "tickets/1" in args[0]

    @patch('requests.Session.get')
    def test_get_ticket_history(self, mock_get, mock_response):
        """Test getting ticket history."""
        # Setup mock response
        mock_response_obj = mock_response({
            "conversations": [
                {
                    "id": 1,
                    "body": "Test conversation"
                }
            ]
        })
        mock_get.return_value = mock_response_obj

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client.get_ticket_history(1)

        # Verify result
        assert "conversations" in result
        assert len(result["conversations"]) == 1
        assert result["conversations"][0]["id"] == 1

        # Verify the request was made correctly
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        # The URL is now passed as a positional argument in the _make_request method
        assert "tickets/1/conversations" in args[0]

    @patch('requests.Session.get')
    def test_get_agents(self, mock_get, mock_response):
        """Test getting agents."""
        # Setup mock response
        mock_response_obj = mock_response({
            "agents": [
                {
                    "id": 1,
                    "name": "Test Agent"
                }
            ]
        })
        mock_get.return_value = mock_response_obj

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client.get_agents()

        # Verify result
        assert "agents" in result
        assert len(result["agents"]) == 1
        assert result["agents"][0]["id"] == 1

        # Verify the request was made correctly
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        # The URL is now passed as a positional argument in the _make_request method
        assert "agents" in args[0]

    @patch('requests.Session.get')
    def test_get_service_items(self, mock_get, mock_response):
        """Test getting service items."""
        # Setup mock response
        mock_response_obj = mock_response({
            "service_items": [
                {
                    "id": 1,
                    "name": "Test Service Item"
                }
            ]
        })
        mock_get.return_value = mock_response_obj

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client.get_service_items()

        # Verify result
        assert "service_items" in result
        assert len(result["service_items"]) == 1
        assert result["service_items"][0]["id"] == 1

        # Verify the request was made correctly
        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        # The URL is now passed as a positional argument in the _make_request method
        assert "service_catalog/items" in args[0]

    @patch('requests.Session.get')
    def test_rate_limiting(self, mock_get, mock_response):
        """Test handling of rate limiting."""
        # Setup mock responses
        rate_limited_response = mock_response({}, status_code=429, headers={"Retry-After": "1"})
        success_response = mock_response({
            "tickets": [
                {
                    "id": 1,
                    "subject": "Test Ticket 1"
                }
            ]
        })

        # Configure mock to return rate limited response first, then success
        mock_get.side_effect = [rate_limited_response, success_response]

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")
        result = client._make_request("GET", "tickets")

        # Verify result
        assert "tickets" in result
        assert len(result["tickets"]) == 1

        # Verify the request was made twice
        assert mock_get.call_count == 2

    @patch('requests.Session.get')
    def test_retry_on_error(self, mock_get):
        """Test retry on error."""
        # Setup mock to raise an exception
        mock_get.side_effect = requests.exceptions.RequestException("Test error")

        # Create client and call method
        client = FreshServiceClient(domain="test.freshservice.com", api_key="test_api_key")

        # Verify that the exception is raised after retries
        with pytest.raises(requests.exceptions.RequestException):
            client._make_request("GET", "tickets")

        # Verify the request was attempted multiple times
        assert mock_get.call_count > 1

    def test_get_all_pages(self, mock_api_client):
        """Test getting all pages of results."""
        # Use the mock_api_client fixture which already has get_all_pages mocked
        results = mock_api_client.get_all_pages("tickets")

        # Verify the results
        assert len(results) == 2
        assert results[0]["id"] == 1
        assert results[1]["id"] == 2
