"""
Command-line interface for FreshConnect.
"""

import argparse
import logging
import sys
from datetime import datetime, timedelta

from .config import settings
from .processors.tickets import fetch_tickets, fetch_ticket_history, fetch_all_ticket_history
from .processors.agents import analyze_agent_activity, generate_agent_report
from .processors.services import fetch_service_items, analyze_service_catalog
from .web.app import run_app

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='FreshConnect CLI')

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Fetch tickets command
    fetch_parser = subparsers.add_parser('fetch-tickets', help='Fetch tickets from FreshService')
    fetch_parser.add_argument('--since', help='Fetch tickets updated since (ISO format)')
    fetch_parser.add_argument('--days', type=int, help='Fetch tickets updated in the last N days')
    fetch_parser.add_argument('--max', type=int, help='Maximum number of tickets to fetch')

    # Fetch ticket history command
    history_parser = subparsers.add_parser('fetch-history', help='Fetch ticket history')
    history_parser.add_argument('--ticket-id', type=int, help='Fetch history for a specific ticket')
    history_parser.add_argument('--all', action='store_true', help='Fetch history for all tickets')
    history_parser.add_argument('--max', type=int, help='Maximum number of tickets to process')

    # Agent analysis command
    agent_parser = subparsers.add_parser('analyze-agents', help='Analyze agent activity')
    agent_parser.add_argument('--output', help='Path to save the visualization')
    agent_parser.add_argument('--report', action='store_true', help='Generate a comprehensive report')

    # Service catalog command
    service_parser = subparsers.add_parser('fetch-services', help='Fetch service catalog items')
    service_parser.add_argument('--max', type=int, help='Maximum number of items to fetch')
    service_parser.add_argument('--analyze', action='store_true', help='Analyze the service catalog')

    # Web server command
    web_parser = subparsers.add_parser('web', help='Run the web server')

    return parser.parse_args()

def main():
    """Main entry point for the CLI."""
    # Set up logging
    settings.setup_logging()

    # Parse arguments
    args = parse_args()

    if args.command == 'fetch-tickets':
        # Determine the 'since' parameter
        updated_since = None
        if args.since:
            updated_since = args.since
        elif args.days:
            # Calculate date N days ago
            updated_since = (datetime.utcnow() - timedelta(days=args.days)).isoformat()

        # Fetch tickets
        count = fetch_tickets(updated_since, args.max)
        logger.info(f"Fetched and stored {count} tickets")

    elif args.command == 'fetch-history':
        if args.ticket_id:
            # Fetch history for a specific ticket
            history = fetch_ticket_history(args.ticket_id)
            if history:
                logger.info(f"Fetched history for ticket {args.ticket_id}")
            else:
                logger.error(f"Failed to fetch history for ticket {args.ticket_id}")
        elif args.all:
            # Fetch history for all tickets
            count = fetch_all_ticket_history(args.max)
            logger.info(f"Processed history for {count} tickets")
        else:
            logger.error("Either --ticket-id or --all is required")
            return 1

    elif args.command == 'analyze-agents':
        if args.report:
            # Generate a comprehensive agent report
            report = generate_agent_report(args.output)
            if report:
                logger.info("Generated agent activity report")
                # Print some key metrics
                logger.info(f"Total actions: {report['metrics']['action_count']}")
                logger.info(f"Unique agents: {report['metrics']['unique_actors']}")
            else:
                logger.error("Failed to generate agent report")
                return 1
        else:
            # Analyze agent activity
            results = analyze_agent_activity(args.output)
            if results:
                logger.info("Analyzed agent activity")
                logger.info(f"Total actions: {results['action_count']}")
                logger.info(f"Unique agents: {results['unique_actors']}")
            else:
                logger.error("Failed to analyze agent activity")
                return 1

    elif args.command == 'fetch-services':
        # Fetch service catalog items
        count = fetch_service_items(args.max)
        logger.info(f"Fetched and stored {count} service catalog items")

        if args.analyze:
            # Analyze the service catalog
            analysis = analyze_service_catalog()
            if analysis:
                logger.info("Analyzed service catalog")
                logger.info(f"Total items: {analysis['total_items']}")
                logger.info(f"Categories: {len(analysis['categories'])}")
                for category, count in analysis['categories'].items():
                    logger.info(f"  {category}: {count} items")
            else:
                logger.error("Failed to analyze service catalog")
                return 1

    elif args.command == 'web':
        # Run the web server
        logger.info(f"Starting web server on {settings.WEB_CONFIG['host']}:{settings.WEB_CONFIG['port']}")
        run_app()

    else:
        logger.error("No command specified")
        return 1

    return 0

if __name__ == '__main__':
    sys.exit(main())
