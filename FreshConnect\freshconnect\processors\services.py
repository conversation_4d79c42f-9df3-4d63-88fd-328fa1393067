"""
Service catalog processing module for FreshConnect.

This module handles fetching and processing service catalog items from FreshService.
"""

import logging
import time
from datetime import datetime
from ..api.freshservice import FreshServiceClient
from ..data.database import initialize_db
from ..config import settings

logger = logging.getLogger(__name__)

class ServiceProcessor:
    """Processor for FreshService service catalog items."""
    
    def __init__(self, api_client=None, db=None):
        """
        Initialize the service processor.
        
        Args:
            api_client (FreshServiceClient, optional): FreshService API client. Defaults to None.
            db (Database, optional): Database instance. Defaults to None.
        """
        self.api_client = api_client or FreshServiceClient()
        self.db = db or initialize_db()
        logger.debug("Initialized ServiceProcessor")
    
    def fetch_and_store_service_items(self, max_items=None):
        """
        Fetch service catalog items from FreshService and store them in the database.
        
        Args:
            max_items (int, optional): Maximum number of items to fetch. Defaults to None.
            
        Returns:
            int: Number of items fetched and stored
        """
        logger.info("Fetching service catalog items")
        
        try:
            # Get all service items using the API client's pagination helper
            all_items = self.api_client.get_all_pages('service_catalog/items')
            
            # Limit the number of items if specified
            if max_items:
                all_items = all_items[:max_items]
            
            # Store items in the database
            if all_items:
                self.db.clear_and_insert_items('service_items', all_items)
                logger.info(f"Stored {len(all_items)} service catalog items in the database")
            else:
                logger.warning("No service catalog items found")
            
            return len(all_items)
            
        except Exception as e:
            logger.error(f"Error fetching service catalog items: {str(e)}")
            return 0
    
    def get_service_item_categories(self):
        """
        Get a list of unique service item categories.
        
        Returns:
            list: List of category names
        """
        try:
            # Get all service items from the database
            items = self.db.find_items('service_items')
            
            if not items:
                logger.warning("No service items found in database")
                return []
            
            # Extract unique categories
            categories = set()
            for item in items:
                category = item.get('category', {}).get('name')
                if category:
                    categories.add(category)
            
            return sorted(list(categories))
            
        except Exception as e:
            logger.error(f"Error getting service item categories: {str(e)}")
            return []
    
    def get_items_by_category(self, category_name):
        """
        Get service items filtered by category.
        
        Args:
            category_name (str): Category name to filter by
            
        Returns:
            list: Filtered service items
        """
        try:
            # Get all service items from the database
            items = self.db.find_items('service_items')
            
            if not items:
                logger.warning("No service items found in database")
                return []
            
            # Filter by category
            filtered_items = [
                item for item in items
                if item.get('category', {}).get('name') == category_name
            ]
            
            return filtered_items
            
        except Exception as e:
            logger.error(f"Error getting items by category: {str(e)}")
            return []
    
    def analyze_service_catalog(self):
        """
        Analyze the service catalog and generate statistics.
        
        Returns:
            dict: Analysis results
        """
        try:
            # Get all service items from the database
            items = self.db.find_items('service_items')
            
            if not items:
                logger.warning("No service items found in database")
                return {}
            
            # Get categories
            categories = {}
            for item in items:
                category = item.get('category', {}).get('name', 'Uncategorized')
                categories[category] = categories.get(category, 0) + 1
            
            # Count items with and without attachments
            with_attachments = sum(1 for item in items if item.get('attachments'))
            
            # Count items by visibility
            visibility_counts = {}
            for item in items:
                visibility = item.get('visibility', 'Unknown')
                visibility_counts[visibility] = visibility_counts.get(visibility, 0) + 1
            
            # Return analysis results
            return {
                'total_items': len(items),
                'categories': categories,
                'with_attachments': with_attachments,
                'without_attachments': len(items) - with_attachments,
                'visibility': visibility_counts
            }
            
        except Exception as e:
            logger.error(f"Error analyzing service catalog: {str(e)}")
            return {}


def fetch_service_items(max_items=None):
    """
    Fetch service catalog items from FreshService and store them in the database.
    
    Args:
        max_items (int, optional): Maximum number of items to fetch. Defaults to None.
        
    Returns:
        int: Number of items fetched and stored
    """
    processor = ServiceProcessor()
    return processor.fetch_and_store_service_items(max_items)


def get_service_categories():
    """
    Get a list of unique service item categories.
    
    Returns:
        list: List of category names
    """
    processor = ServiceProcessor()
    return processor.get_service_item_categories()


def analyze_service_catalog():
    """
    Analyze the service catalog and generate statistics.
    
    Returns:
        dict: Analysis results
    """
    processor = ServiceProcessor()
    return processor.analyze_service_catalog()
