# Import required module for Excel operations
Import-Module ImportExcel

function Run-Reconciliation {
    try {
        Write-Host "Working directory: $(Get-Location)"
        
        # Check if files exist in current directory
        if (-not (Test-Path "MDMtoLS.xlsx")) {
            throw "MDMtoLS.xlsx not found in current directory"
        }
        if (-not (Test-Path "LStoMDM.xlsx")) {
            throw "LStoMDM.xlsx not found in current directory"
        }
        
        # Read the Excel files from current directory
        $mdmData = Import-Excel -Path "MDMtoLS.xlsx"
        $lsData = Import-Excel -Path "LStoMDM.xlsx"

        # Get today's date in the required format
        $today = Get-Date -Format "yyyy.MM.dd"
        $outputFile = "KC_ITM_21.1_$today.xlsx"

        # Initialize arrays for mismatches
        $notInLS = @()
        $notInMDM = @()

        # Check MDM records not in LS
        foreach ($row in $mdmData) {
            if ($row.'Serial number' -notin $lsData.Serialnumber) {
                $rowCopy = $row | Select-Object *
                $notInLS += $rowCopy
            }
        }

        # Check LS records not in MDM
        foreach ($row in $lsData) {
            if ($row.Serialnumber -notin $mdmData.'Serial number') {
                $rowCopy = $row | Select-Object *
                $notInMDM += $rowCopy
            }
        }

        # Export to Excel
        $mdmData | Export-Excel -Path $outputFile -WorksheetName 'MDMtoLS' -AutoSize
        $lsData | Export-Excel -Path $outputFile -WorksheetName 'LStoMDM' -AutoSize
        
        if ($notInLS.Count -gt 0) {
            $notInLS | Export-Excel -Path $outputFile -WorksheetName 'Not in LS' -AutoSize
        }
        if ($notInMDM.Count -gt 0) {
            $notInMDM | Export-Excel -Path $outputFile -WorksheetName 'Not in MDM' -AutoSize
        }

        Write-Host "Reconciliation completed. Output file: $outputFile"
        Write-Host "Found $($notInLS.Count) records not in LS"
        Write-Host "Found $($notInMDM.Count) records not in MDM"
    }
    catch {
        Write-Error "Error occurred during reconciliation: $_"
    }
}

# Run the reconciliation if script is executed directly
if ($MyInvocation.InvocationName -ne '.') {
    Run-Reconciliation
}
