#!/usr/bin/env python3
"""
FreshService Users Fetcher

This standalone script fetches the first 10 users (requesters) from FreshService
and displays all their attributes.

Usage:
    python fetch_freshservice_users.py

Environment Variables:
    FRESHSERVICE_DOMAIN: Your FreshService domain (e.g., company.freshservice.com)
    FRESHSERVICE_API_KEY: Your FreshService API key

Author: IT Team
Date: 2025-01-03
"""

import os
import sys
import json
import requests
from base64 import b64encode
from datetime import datetime
from dotenv import load_dotenv

class FreshServiceUserFetcher:
    """A class to fetch users from FreshService API."""
    
    def __init__(self, domain=None, api_key=None):
        """
        Initialize the FreshService user fetcher.
        
        Args:
            domain (str): FreshService domain (e.g., 'company.freshservice.com')
            api_key (str): FreshService API key
        """
        # Load environment variables from .env file if it exists
        load_dotenv()
        
        self.domain = domain or os.getenv('FRESHSERVICE_DOMAIN')
        self.api_key = api_key or os.getenv('FRESHSERVICE_API_KEY')
        
        if not self.domain:
            raise ValueError("FRESHSERVICE_DOMAIN must be provided as parameter or environment variable")
        
        if not self.api_key:
            raise ValueError("FRESHSERVICE_API_KEY must be provided as parameter or environment variable")
        
        # Remove protocol if included in domain
        self.domain = self.domain.replace('https://', '').replace('http://', '')
        
        self.base_url = f"https://{self.domain}/api/v2"
        self.session = self._create_session()
        
        print(f"Initialized FreshService client for domain: {self.domain}")
    
    def _create_session(self):
        """Create and configure a requests session for API calls."""
        session = requests.Session()
        auth_token = b64encode(f"{self.api_key}:X".encode()).decode()
        session.headers.update({
            'Authorization': f'Basic {auth_token}',
            'Content-Type': 'application/json'
        })
        return session
    
    def _make_request(self, method, endpoint, params=None, retry_count=0):
        """
        Make an HTTP request to the FreshService API with retry logic.
        
        Args:
            method (str): HTTP method (GET, POST, PUT, DELETE)
            endpoint (str): API endpoint (without base URL)
            params (dict, optional): Query parameters
            retry_count (int): Current retry attempt
            
        Returns:
            dict: JSON response from the API
            
        Raises:
            requests.exceptions.RequestException: If the request fails after retries
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        max_retries = 3
        retry_delay = 2  # seconds
        
        try:
            print(f"Making {method} request to: {url}")
            if params:
                print(f"Parameters: {params}")
            
            response = self.session.request(method, url, params=params, timeout=30)
            
            # Handle rate limiting
            if response.status_code == 429:
                if retry_count < max_retries:
                    print(f"Rate limited. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                    import time
                    time.sleep(retry_delay)
                    return self._make_request(method, endpoint, params, retry_count + 1)
                else:
                    raise requests.exceptions.RequestException("Rate limit exceeded after maximum retries")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            if retry_count < max_retries:
                print(f"Request failed: {e}. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                import time
                time.sleep(retry_delay)
                return self._make_request(method, endpoint, params, retry_count + 1)
            else:
                print(f"Request failed after {max_retries} retries: {e}")
                raise
    
    def get_users(self, page=1, per_page=10):
        """
        Get users (requesters) from FreshService.
        
        Args:
            page (int): Page number for pagination
            per_page (int): Number of users per page (max 100)
            
        Returns:
            dict: JSON response containing users
        """
        params = {
            'page': page,
            'per_page': min(per_page, 100)  # FreshService API limit
        }
        
        print(f"Fetching users (page {page}, {per_page} per page)")
        return self._make_request('GET', 'requesters', params=params)
    
    def get_user(self, user_id):
        """
        Get a specific user by ID.
        
        Args:
            user_id (int): User ID
            
        Returns:
            dict: User details
        """
        print(f"Fetching user {user_id}")
        return self._make_request('GET', f'requesters/{user_id}')
    
    def fetch_first_10_users(self):
        """
        Fetch the first 10 users and return their details.
        
        Returns:
            list: List of user dictionaries
        """
        try:
            response = self.get_users(page=1, per_page=10)
            users = response.get('requesters', [])
            
            print(f"\nSuccessfully fetched {len(users)} users")
            return users
            
        except Exception as e:
            print(f"Error fetching users: {e}")
            return []

def print_user_details(users):
    """
    Print detailed information about users.
    
    Args:
        users (list): List of user dictionaries
    """
    if not users:
        print("No users found.")
        return
    
    print(f"\n{'='*80}")
    print(f"FRESHSERVICE USERS - FIRST {len(users)} USERS")
    print(f"{'='*80}")
    
    for i, user in enumerate(users, 1):
        print(f"\n{'-'*60}")
        print(f"USER #{i} - ID: {user.get('id', 'N/A')}")
        print(f"{'-'*60}")
        
        # Basic Information
        print(f"Name: {user.get('first_name', '')} {user.get('last_name', '')}")
        print(f"Email: {user.get('email', 'N/A')}")
        print(f"Primary Email: {user.get('primary_email', 'N/A')}")
        print(f"Mobile: {user.get('mobile_phone_number', 'N/A')}")
        print(f"Work Phone: {user.get('work_phone_number', 'N/A')}")
        print(f"Job Title: {user.get('job_title', 'N/A')}")
        print(f"Department: {user.get('department_names', 'N/A')}")
        print(f"Location: {user.get('location_name', 'N/A')}")
        print(f"Language: {user.get('language', 'N/A')}")
        print(f"Time Zone: {user.get('time_zone', 'N/A')}")
        
        # Status and Activity
        print(f"Active: {user.get('active', 'N/A')}")
        print(f"VIP User: {user.get('is_agent', 'N/A')}")
        print(f"Created At: {user.get('created_at', 'N/A')}")
        print(f"Updated At: {user.get('updated_at', 'N/A')}")
        
        # Custom Fields (if any)
        custom_fields = user.get('custom_fields', {})
        if custom_fields:
            print(f"Custom Fields:")
            for field, value in custom_fields.items():
                print(f"  {field}: {value}")
        
        # All other attributes
        print(f"\nAll Attributes:")
        for key, value in user.items():
            if key not in ['first_name', 'last_name', 'email', 'primary_email', 
                          'mobile_phone_number', 'work_phone_number', 'job_title',
                          'department_names', 'location_name', 'language', 'time_zone',
                          'active', 'is_agent', 'created_at', 'updated_at', 'custom_fields']:
                print(f"  {key}: {value}")

def save_users_to_file(users, filename=None):
    """
    Save users data to a JSON file.
    
    Args:
        users (list): List of user dictionaries
        filename (str): Output filename (optional)
    """
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"freshservice_users_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\nUsers data saved to: {filename}")
        
    except Exception as e:
        print(f"Error saving users to file: {e}")

def main():
    """Main function to fetch and display FreshService users."""
    print("FreshService Users Fetcher")
    print("=" * 50)
    
    try:
        # Initialize the fetcher
        fetcher = FreshServiceUserFetcher()
        
        # Fetch the first 10 users
        print("\nFetching first 10 users from FreshService...")
        users = fetcher.fetch_first_10_users()
        
        if users:
            # Display user details
            print_user_details(users)
            
            # Save to file
            save_users_to_file(users)
            
            print(f"\n{'='*80}")
            print(f"SUMMARY: Successfully fetched {len(users)} users")
            print(f"{'='*80}")
            
        else:
            print("No users were fetched. Please check your API credentials and domain.")
            
    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("\nPlease ensure you have set the following environment variables:")
        print("- FRESHSERVICE_DOMAIN (e.g., company.freshservice.com)")
        print("- FRESHSERVICE_API_KEY (your FreshService API key)")
        print("\nOr create a .env file with these variables.")
        sys.exit(1)
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
