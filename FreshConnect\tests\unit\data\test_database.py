"""
Unit tests for the database module.
"""

import pytest
import os
from unittest.mock import patch, MagicMock

from freshconnect.data.database import Database, DatabaseFactory, initialize_db
from freshconnect.data.backends.tinydb_backend import TinyDBBackend
from freshconnect.data.backends.sqlite_backend import SQLiteBackend


@pytest.mark.unit
@pytest.mark.data
class TestDatabase:
    """Tests for the Database base class."""

    def test_abstract_methods(self):
        """Test that abstract methods raise NotImplementedError."""
        db = Database()

        with pytest.raises(NotImplementedError):
            db.get_table("test_table")

        with pytest.raises(NotImplementedError):
            db.clear_and_insert_items("test_table", [])

        with pytest.raises(NotImplementedError):
            db.insert_items("test_table", [])

        with pytest.raises(NotImplementedError):
            db.find_items("test_table")

        with pytest.raises(NotImplementedError):
            db.update_items("test_table", {}, {})

        with pytest.raises(NotImplementedError):
            db.delete_items("test_table", {})


@pytest.mark.unit
@pytest.mark.data
class TestDatabaseFactory:
    """Tests for the DatabaseFactory class."""

    @patch.dict('freshconnect.config.settings.DATABASE_CONFIG', {'type': 'tinydb', 'path': 'test_db.json', 'encoding': 'utf-8'})
    @patch('os.makedirs')
    @patch('freshconnect.data.backends.tinydb_backend.TinyDBBackend')
    def test_get_database_tinydb(self, mock_tinydb, mock_makedirs):
        """Test getting a TinyDB database."""
        # Setup mock
        mock_tinydb.return_value = MagicMock()

        # Call the factory method
        db = DatabaseFactory.get_database()

        # Verify the correct backend was created
        mock_tinydb.assert_called_once_with('test_db.json', 'utf-8')
        assert db is mock_tinydb.return_value

    @patch.dict('freshconnect.config.settings.DATABASE_CONFIG', {'type': 'sqlite', 'path': 'test_db.sqlite'})
    @patch('os.makedirs')
    @patch('freshconnect.data.backends.sqlite_backend.SQLiteBackend')
    def test_get_database_sqlite(self, mock_sqlite, mock_makedirs):
        """Test getting a SQLite database."""
        # Setup mock
        mock_sqlite.return_value = MagicMock()

        # Call the factory method
        db = DatabaseFactory.get_database()

        # Verify the correct backend was created
        mock_sqlite.assert_called_once_with('test_db.sqlite')
        assert db is mock_sqlite.return_value

    @patch.dict('freshconnect.config.settings.DATABASE_CONFIG', {'type': 'invalid', 'path': 'test_db.json'})
    @patch('os.makedirs')
    def test_get_database_invalid(self, mock_makedirs):
        """Test getting an invalid database type."""
        with pytest.raises(ValueError):
            DatabaseFactory.get_database()


@pytest.mark.unit
@pytest.mark.data
class TestTinyDBBackend:
    """Tests for the TinyDBBackend class."""

    def test_init(self, temp_db_file):
        """Test initialization of the TinyDB backend."""
        db = TinyDBBackend(temp_db_file)
        assert db.db_path == temp_db_file
        assert db.encoding == 'utf-8'

    def test_get_table(self, temp_db_file):
        """Test getting a table."""
        db = TinyDBBackend(temp_db_file)
        table = db.get_table("test_table")
        assert table is not None

    def test_clear_and_insert_items(self, temp_db_file):
        """Test clearing and inserting items."""
        db = TinyDBBackend(temp_db_file)
        items = [{"id": 1, "name": "Test 1"}, {"id": 2, "name": "Test 2"}]

        # Insert items
        count = db.clear_and_insert_items("test_table", items)
        assert count == 2

        # Verify items were inserted
        result = db.find_items("test_table")
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[1]["id"] == 2

        # Insert new items (should clear old ones)
        new_items = [{"id": 3, "name": "Test 3"}]
        count = db.clear_and_insert_items("test_table", new_items)
        assert count == 1

        # Verify old items were cleared
        result = db.find_items("test_table")
        assert len(result) == 1
        assert result[0]["id"] == 3

    def test_insert_items(self, temp_db_file):
        """Test inserting items without clearing."""
        db = TinyDBBackend(temp_db_file)
        items = [{"id": 1, "name": "Test 1"}]

        # Insert items
        count = db.insert_items("test_table", items)
        assert count == 1

        # Insert more items (should not clear old ones)
        more_items = [{"id": 2, "name": "Test 2"}]
        count = db.insert_items("test_table", more_items)
        assert count == 1

        # Verify all items are present
        result = db.find_items("test_table")
        assert len(result) == 2

    def test_find_items(self, temp_db_file):
        """Test finding items."""
        db = TinyDBBackend(temp_db_file)
        items = [
            {"id": 1, "name": "Test 1", "category": "A"},
            {"id": 2, "name": "Test 2", "category": "B"},
            {"id": 3, "name": "Test 3", "category": "A"}
        ]

        # Insert items
        db.clear_and_insert_items("test_table", items)

        # Find all items
        result = db.find_items("test_table")
        assert len(result) == 3

        # Find items with query
        result = db.find_items("test_table", {"category": "A"})
        assert len(result) == 2
        assert result[0]["id"] in [1, 3]
        assert result[1]["id"] in [1, 3]

    def test_update_items(self, temp_db_file):
        """Test updating items."""
        db = TinyDBBackend(temp_db_file)
        items = [
            {"id": 1, "name": "Test 1", "category": "A"},
            {"id": 2, "name": "Test 2", "category": "B"}
        ]

        # Insert items
        db.clear_and_insert_items("test_table", items)

        # Update items
        count = db.update_items("test_table", {"category": "A"}, {"status": "active"})
        assert count == 1

        # Verify update
        result = db.find_items("test_table", {"id": 1})
        assert len(result) == 1
        assert result[0]["status"] == "active"

        # Verify other item was not updated
        result = db.find_items("test_table", {"id": 2})
        assert len(result) == 1
        assert "status" not in result[0]

    def test_delete_items(self, temp_db_file):
        """Test deleting items."""
        db = TinyDBBackend(temp_db_file)
        items = [
            {"id": 1, "name": "Test 1", "category": "A"},
            {"id": 2, "name": "Test 2", "category": "B"}
        ]

        # Insert items
        db.clear_and_insert_items("test_table", items)

        # Delete items
        count = db.delete_items("test_table", {"category": "A"})
        assert count == 1

        # Verify deletion
        result = db.find_items("test_table")
        assert len(result) == 1
        assert result[0]["id"] == 2


@pytest.mark.unit
@pytest.mark.data
class TestSQLiteBackend:
    """Tests for the SQLiteBackend class."""

    def test_init(self, temp_db_file):
        """Test initialization of the SQLite backend."""
        # Use a .sqlite extension for the test file
        sqlite_file = temp_db_file.replace('.json', '.sqlite')

        db = SQLiteBackend(sqlite_file)
        assert db.db_path == sqlite_file

        # Clean up
        db.conn.close()
        if os.path.exists(sqlite_file):
            os.unlink(sqlite_file)

    def test_get_table(self, temp_db_file):
        """Test getting a table."""
        # Use a .sqlite extension for the test file
        sqlite_file = temp_db_file.replace('.json', '.sqlite')

        db = SQLiteBackend(sqlite_file)
        table_name = db.get_table("test_table")
        assert table_name == "test_table"

        # Verify the table was created
        cursor = db._execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_table'")
        assert cursor.fetchone() is not None

        # Clean up
        db.conn.close()
        if os.path.exists(sqlite_file):
            os.unlink(sqlite_file)

    def test_clear_and_insert_items(self, temp_db_file):
        """Test clearing and inserting items."""
        # Use a .sqlite extension for the test file
        sqlite_file = temp_db_file.replace('.json', '.sqlite')

        db = SQLiteBackend(sqlite_file)
        items = [{"id": 1, "name": "Test 1"}, {"id": 2, "name": "Test 2"}]

        # Insert items
        count = db.clear_and_insert_items("test_table", items)
        assert count == 2

        # Verify items were inserted
        result = db.find_items("test_table")
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[1]["id"] == 2

        # Insert new items (should clear old ones)
        new_items = [{"id": 3, "name": "Test 3"}]
        count = db.clear_and_insert_items("test_table", new_items)
        assert count == 1

        # Verify old items were cleared
        result = db.find_items("test_table")
        assert len(result) == 1
        assert result[0]["id"] == 3

        # Clean up
        db.conn.close()
        if os.path.exists(sqlite_file):
            os.unlink(sqlite_file)


@pytest.mark.unit
@pytest.mark.data
class TestInitializeDB:
    """Tests for the initialize_db function."""

    @patch('freshconnect.data.database.DatabaseFactory.get_database')
    def test_initialize_db(self, mock_get_database):
        """Test initializing the database."""
        # Setup mock
        mock_db = MagicMock()
        mock_get_database.return_value = mock_db

        # Call the function
        db = initialize_db()

        # Verify the database was created
        mock_get_database.assert_called_once()
        assert db is mock_db

    @patch('freshconnect.data.database.DatabaseFactory.get_database')
    def test_initialize_db_error(self, mock_get_database):
        """Test error handling when initializing the database."""
        # Setup mock to raise an exception
        mock_get_database.side_effect = Exception("Test error")

        # Verify the exception is propagated
        with pytest.raises(Exception):
            initialize_db()
