"""
Agent processing module for FreshConnect.

This module handles analyzing agent activity data from FreshService tickets.
"""

import logging
import json
import plotly.graph_objects as go
from collections import Counter
from plotly.subplots import make_subplots
from datetime import datetime, timedelta, UTC
from ..data.database import initialize_db
from ..config import settings

logger = logging.getLogger(__name__)

class AgentProcessor:
    """Processor for FreshService agent data."""

    def __init__(self, db=None):
        """
        Initialize the agent processor.

        Args:
            db (Database, optional): Database instance. Defaults to None.
        """
        self.db = db or initialize_db()
        logger.debug("Initialized AgentProcessor")

    def analyze_activity(self, output_file=None, show_plot=True):
        """
        Analyze agent activity based on ticket history.

        Args:
            output_file (str, optional): Path to save the visualization. Defaults to None.
            show_plot (bool, optional): Whether to show the plot in browser. Defaults to True.

        Returns:
            dict: Analysis results
        """
        logger.info("Analyzing agent activity")

        try:
            # Get tickets and history from database
            tickets = self.db.find_items('tickets')
            ticket_history = self.db.find_items('ticket_history')

            if not tickets:
                logger.warning("No tickets found in database")
                return None

            if not ticket_history:
                logger.warning("No ticket history found in database")
                return None

            # Extract all actor names, group setters, and timestamps
            actors = []
            group_setters = []
            action_times = []

            # Process ticket history
            for history_item in ticket_history:
                history_data = history_item.get('history', {})

                for event in history_data:
                    actor_name = event.get('actor', {}).get('name')
                    if actor_name and actor_name != "System" and actor_name != "Ticket Workflow":
                        actors.append(actor_name)

                        # Check if this event involves setting a group
                        content = event.get('content', '').lower()
                        if 'set group as' in content:
                            group_setters.append(actor_name)

                        # Record timestamp and actor
                        if 'created_at' in event:
                            timestamp = datetime.fromisoformat(event['created_at'].replace('Z', '+00:00'))
                            action_times.append((timestamp, actor_name))

            # Count occurrences
            actor_counts = Counter(actors)
            group_setter_counts = Counter(group_setters)

            # Sort by count
            sorted_actors = dict(sorted(actor_counts.items(), key=lambda x: x[1], reverse=True))
            sorted_group_setters = dict(sorted(group_setter_counts.items(), key=lambda x: x[1], reverse=True))

            # Create visualization
            fig = self._create_visualization(sorted_actors, sorted_group_setters, action_times)

            # Save or show the plot
            if output_file:
                fig.write_html(output_file)
                logger.info(f"Saved visualization to {output_file}")

            if show_plot:
                fig.show()

            # Return analysis results
            return {
                'actor_counts': dict(actor_counts),
                'group_setter_counts': dict(group_setter_counts),
                'action_count': len(action_times),
                'unique_actors': len(actor_counts)
            }

        except Exception as e:
            logger.error(f"Error analyzing agent activity: {str(e)}")
            return None

    def _create_visualization(self, sorted_actors, sorted_group_setters, action_times):
        """
        Create a visualization of agent activity.

        Args:
            sorted_actors (dict): Sorted dictionary of actor counts
            sorted_group_setters (dict): Sorted dictionary of group setter counts
            action_times (list): List of (timestamp, actor) tuples

        Returns:
            plotly.graph_objects.Figure: Plotly figure
        """
        # Create subplot visualization
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('Overall Agent Activity', 'Group Setting Activity', 'Action Timing'),
            vertical_spacing=0.1,
            row_heights=[0.33, 0.33, 0.34]
        )

        # Add overall activity bar
        fig.add_trace(
            go.Bar(x=list(sorted_actors.keys()), y=list(sorted_actors.values()), name="Overall Activity"),
            row=1, col=1
        )

        # Add group setting activity bar
        fig.add_trace(
            go.Bar(x=list(sorted_group_setters.keys()), y=list(sorted_group_setters.values()), name="Group Settings"),
            row=2, col=1
        )

        # Count occurrences of timestamp-actor pairs
        action_counts = Counter(action_times)

        # Sort action times and prepare data with sizes
        sorted_actions = sorted(action_counts.items(), key=lambda x: x[0][0])

        if sorted_actions:  # Check if there are any actions
            timestamps = [x[0][0] for x in sorted_actions]
            actors_time = [x[0][1] for x in sorted_actions]
            sizes = [x[1] * 10 for x in sorted_actions]  # Multiply by 10 to make sizes more visible

            # Add scatter plot for timing
            fig.add_trace(
                go.Scatter(
                    x=timestamps,
                    y=actors_time,
                    mode='markers',
                    name="Action Timing",
                    marker=dict(
                        size=sizes,
                        sizemode='area',
                        sizeref=2.*max(sizes)/(40.**2) if sizes else 1,  # Normalize sizes
                        sizemin=4,  # Minimum size for single occurrences
                        color=sizes,  # Use sizes for color intensity
                        colorscale='RdYlBu_r',  # Red-Yellow-Blue reversed (warm center)
                        showscale=True,  # Show colorbar
                        colorbar=dict(
                            title="Actions",
                            x=1.1  # Move colorbar outside the plot
                        )
                    )
                ),
                row=3, col=1
            )

        # Update layout
        fig.update_layout(
            height=1800,  # Increased height for three graphs
            width=1200,
            showlegend=False,
            title_text="Agent Activity Analysis"
        )

        # Update x-axis properties
        fig.update_xaxes(tickangle=-45, row=1, col=1)
        fig.update_xaxes(tickangle=-45, row=2, col=1)
        fig.update_xaxes(title_text="Time", row=3, col=1)

        # Update y-axis titles
        fig.update_yaxes(title_text="Number of Actions", row=1, col=1)
        fig.update_yaxes(title_text="Number of Group Changes", row=2, col=1)
        fig.update_yaxes(title_text="Agent", row=3, col=1)

        # Add more bottom margin for rotated labels
        fig.update_layout(margin=dict(b=150))

        return fig

    def analyze_recent_activity(self, minutes=10):
        """
        Analyze agent activity in the last specified number of minutes.

        Args:
            minutes (int, optional): Number of minutes to look back. Defaults to 10.

        Returns:
            dict: Analysis results with recent activity data
        """
        logger.info(f"Analyzing agent activity in the last {minutes} minutes")

        try:
            # Get tickets and history from database
            tickets = self.db.find_items('tickets')
            ticket_history = self.db.find_items('ticket_history')

            if not tickets:
                logger.warning("No tickets found in database")
                return None

            if not ticket_history:
                logger.warning("No ticket history found in database")
                return None

            # Calculate the cutoff time (now - minutes)
            cutoff_time = datetime.now().astimezone().replace(microsecond=0) - timedelta(minutes=minutes)

            # Extract recent actor names, actions, and timestamps
            recent_actors = []
            recent_actions = []
            recent_tickets = set()

            # Process ticket history
            for history_item in ticket_history:
                ticket_id = history_item.get('ticket_id')
                history_data = history_item.get('history', {})

                for event in history_data:
                    # Skip if no created_at timestamp
                    if 'created_at' not in event:
                        continue

                    # Parse timestamp and check if it's recent
                    timestamp = datetime.fromisoformat(event['created_at'].replace('Z', '+00:00'))
                    if timestamp >= cutoff_time:
                        actor_name = event.get('actor', {}).get('name')
                        if actor_name and actor_name != "System" and actor_name != "Ticket Workflow":
                            recent_actors.append(actor_name)

                            # Record the action type
                            action_type = "update"  # Default action type
                            content = event.get('content', '').lower()

                            if 'created the ticket' in content:
                                action_type = "create"
                            elif 'closed the ticket' in content:
                                action_type = "close"
                            elif 'reopened the ticket' in content:
                                action_type = "reopen"
                            elif 'replied to the ticket' in content:
                                action_type = "reply"
                            elif 'added a note' in content:
                                action_type = "note"
                            elif 'set group as' in content:
                                action_type = "group_change"
                            elif 'set status as' in content:
                                action_type = "status_change"
                            elif 'set priority as' in content:
                                action_type = "priority_change"

                            recent_actions.append({
                                'actor': actor_name,
                                'action': action_type,
                                'timestamp': timestamp.isoformat(),
                                'ticket_id': ticket_id,
                                'content': event.get('content', '')
                            })

                            # Add to set of recently active tickets
                            if ticket_id:
                                recent_tickets.add(ticket_id)

            # Count occurrences
            actor_counts = Counter(recent_actors)

            # Prepare results
            results = {
                'timestamp': datetime.now().astimezone().replace(microsecond=0).isoformat(),
                'period_minutes': minutes,
                'actor_counts': dict(actor_counts),
                'action_count': len(recent_actions),
                'unique_actors': len(actor_counts),
                'active_tickets': len(recent_tickets),
                'actions': recent_actions
            }

            # Store results in database
            self.db.insert_items('recent_agent_activity', [results])
            logger.info(f"Stored recent agent activity in database: {len(recent_actions)} actions by {len(actor_counts)} agents")

            return results

        except Exception as e:
            logger.error(f"Error analyzing recent agent activity: {str(e)}")
            return None

    def fetch_agents_and_groups(self):
        """
        Fetch agent details and their group memberships from FreshService.

        This method retrieves all agents and their group memberships from FreshService
        and stores them in the database.

        Returns:
            list: List of dictionaries containing agent details with groups
        """
        logger.info("Fetching agent details and group memberships")

        try:
            # Import here to avoid circular imports
            from ..api.freshservice import FreshServiceClient

            # Initialize FreshService client
            client = FreshServiceClient()

            # Get all agents
            all_agents = client.get_all_pages('agents')
            if not all_agents:
                logger.warning("No agents found in FreshService")
                return []

            logger.info(f"Found {len(all_agents)} agents in FreshService")

            # Get all groups
            all_groups = client.get_all_pages('groups')
            if not all_groups:
                logger.warning("No groups found in FreshService")
                # Return agents without group information
                agents_without_groups = [{
                    'id': agent.get('id'),
                    'name': agent.get('first_name', '') + ' ' + agent.get('last_name', ''),
                    'email': agent.get('email'),
                    'active': agent.get('active', False),
                    'groups': []
                } for agent in all_agents]

                # Store in database
                self.db.clear_and_insert_items('agent_details', agents_without_groups)
                logger.info(f"Stored details for {len(agents_without_groups)} agents (without groups) in the database")

                return agents_without_groups

            logger.info(f"Found {len(all_groups)} groups in FreshService")

            # For each group, get its members and map them to agents
            agent_groups = {}

            for group in all_groups:
                group_id = group['id']
                group_name = group['name']

                # Skip group member fetching as it's causing 404 errors
                # Instead, we'll use a different approach to associate agents with groups
                logger.info(f"Skipping member fetching for group {group_id} ({group_name}) to avoid 404 errors")

                # Note: In a real implementation, you might want to use a different API endpoint
                # or method to determine group membership. For now, we'll leave agents without
                # group associations to avoid the errors.

            # Create the final result with agent details and their groups
            result = []
            for agent in all_agents:
                agent_id = agent.get('id')
                first_name = agent.get('first_name', '') or ''
                last_name = agent.get('last_name', '') or ''
                result.append({
                    'id': agent_id,
                    'name': f"{first_name} {last_name}".strip(),
                    'email': agent.get('email'),
                    'active': agent.get('active', False),
                    'groups': agent_groups.get(agent_id, [])
                })

            # Store the results in the database
            self.db.clear_and_insert_items('agent_details', result)
            logger.info(f"Stored details for {len(result)} agents in the database")

            return result

        except Exception as e:
            logger.error(f"Error fetching agent details: {str(e)}")
            return []


def analyze_agent_activity(output_file=None, show_plot=True):
    """
    Analyze agent activity based on ticket history.

    Args:
        output_file (str, optional): Path to save the visualization. Defaults to None.
        show_plot (bool, optional): Whether to show the plot in browser. Defaults to True.

    Returns:
        dict: Analysis results
    """
    processor = AgentProcessor()
    return processor.analyze_activity(output_file, show_plot)


def generate_agent_report(output_file=None):
    """
    Generate a comprehensive agent activity report.

    Args:
        output_file (str, optional): Path to save the report. Defaults to None.

    Returns:
        dict: Report data
    """
    processor = AgentProcessor()
    results = processor.analyze_activity(output_file, show_plot=False)

    if not results:
        logger.warning("No results available for agent report")
        return None

    # Generate a report with additional metrics
    report = {
        'timestamp': datetime.now(UTC).isoformat(),
        'metrics': results,
        'top_agents': dict(sorted(results['actor_counts'].items(), key=lambda x: x[1], reverse=True)[:10]),
        'top_group_setters': dict(sorted(results['group_setter_counts'].items(), key=lambda x: x[1], reverse=True)[:5])
    }

    # Save report to database
    db = initialize_db()
    db.insert_items('agent_reports', [report])

    logger.info("Generated agent activity report")
    return report


def analyze_recent_agent_activity(minutes=10):
    """
    Analyze agent activity in the last specified number of minutes and store results in the database.

    Args:
        minutes (int, optional): Number of minutes to look back. Defaults to 10.

    Returns:
        dict: Analysis results with recent activity data
    """
    processor = AgentProcessor()
    return processor.analyze_recent_activity(minutes)


def fetch_agent_details():
    """
    Fetch agent IDs, names, and the groups they belong to from FreshService.

    This function retrieves all agents and their group memberships from FreshService
    and returns a structured result with agent IDs, names, and their associated groups.

    Returns:
        list: List of dictionaries containing agent details with the following structure:
            {
                'id': int,
                'name': str,
                'email': str,
                'active': bool,
                'groups': [
                    {
                        'id': int,
                        'name': str
                    },
                    ...
                ]
            }
    """
    processor = AgentProcessor()
    return processor.fetch_agents_and_groups()
