from time import sleep
import requests
from requests.models import HTTPBasicAuth
import json

class FreshServiceConnector:
    """A class to handle all FreshService API connections and operations"""
    
    def __init__(self, domain="usegale.freshservice.com", api_key="Oq9HGkzKmPkHfCokti", password="trash"):
        """
        Initialize the FreshService connector
        
        Args:
            domain (str): FreshService domain
            api_key (str): API key for authentication
            password (str): Password for authentication
        """
        self.base_url = f"https://{domain}/api/v2"
        self.api_key = api_key
        self.password = password
        self.auth = HTTPBasicAuth(username=api_key, password=password)
        self.headers = {"Content-Type": "application/json"}

    def post_software_users(self, json_obj, soft_id):
        """Create new associations between user and software"""
        payload = json.dumps(json_obj)
        url = f"{self.base_url}/applications/{soft_id}/users"
        
        response = requests.post(
            url=url,
            data=payload,
            auth=self.auth,
            headers=self.headers
        )
        
        print(response.status_code)
        print(response.content)

    def get_all_software(self):
        """Returns array of JSON containing all software"""
        url = f"{self.base_url}/applications"
        response = requests.get(url=url, auth=self.auth)
        return response.json()

    def get_licenses_for_software(self, soft_id):
        """Get licenses for specific software"""
        url = f"{self.base_url}/applications/{soft_id}/licenses"
        response = requests.get(url=url, auth=self.auth)
        return response.json()

    def get_user_id_from_email(self, email):
        """Get user ID from email address"""
        url = f"{self.base_url}/requesters"
        params = {"include_agents": "true", "email": email}

        try:
            response = requests.get(url, params=params, auth=self.auth)
            response.raise_for_status()
            payload = response.json()
            try:
                return payload["requesters"][0]["id"]
            except Exception:
                print(f"{email} - no such email")
                return None

        except requests.exceptions.HTTPError as error:
            print(error)
            return None

    def get_updated_tickets(self, dt_since):
        """Get tickets updated since a specific date"""
        url = f"{self.base_url}/tickets?updated_since={dt_since}"
        
        dict_json = {}
        page_num = 1
        
        while True:
            response = requests.get(url=url, auth=self.auth)
            dict_json[f"page{page_num}"] = response.json()
            
            check_head = response.headers

            if int(check_head['X-Ratelimit-Remaining']) < 60:
                print("Pausing for flow control - Updated Tickets")
                sleep(1)

            try:
                url = check_head['Link'][1:-13]
                page_num += 1
            except:
                return dict_json

    def get_ticket_history(self, ticket_id):
        """Get history for a specific ticket"""
        url = f"{self.base_url}/tickets/{ticket_id}/activities"
        
        dict_json = {}
        page_num = 1
        
        while True:
            response = requests.get(url=url, auth=self.auth)
            dict_json[f"page{page_num}"] = response.json()
            
            check_head = response.headers
            print(check_head['X-Ratelimit-Remaining'])

            if int(check_head['X-Ratelimit-Remaining']) < 60:
                print("Pausing for flow control - Updated Tickets: transactions remaining")
                sleep(1)

            try:
                url = check_head['Link'][1:-13]
                page_num += 1
            except:
                return dict_json
