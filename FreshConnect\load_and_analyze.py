"""
Load Ticket Activity and Analyze Recent Agent Activity

This script:
1. Fetches tickets from FreshService
2. Fetches history for all tickets
3. Analyzes agent activity in the last 10 minutes
4. Stores the results in the database
5. Displays a summary of the findings

Usage:
    python load_and_analyze.py [--days DAYS] [--minutes MINUTES]

Options:
    --days DAYS       Number of days to look back for tickets (default: 7)
    --minutes MINUTES Number of minutes to look back for agent activity (default: 10)
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Import the necessary functions
from freshconnect.processors.tickets import fetch_tickets, fetch_all_ticket_history
from freshconnect.processors.agents import analyze_recent_agent_activity
from freshconnect.config import settings
from freshconnect.api.freshservice import FreshServiceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(settings.STORAGE_DIR, 'load_and_analyze.log'))
    ]
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Load ticket activity and analyze recent agent activity')
    parser.add_argument('--days', type=int, default=7, help='Number of days to look back for tickets (default: 7)')
    parser.add_argument('--minutes', type=int, default=10, help='Number of minutes to look back for agent activity (default: 10)')
    parser.add_argument('--max-tickets', type=int, default=20, help='Maximum number of tickets to process (default: 20)')
    parser.add_argument('--skip-history', action='store_true', help='Skip fetching ticket history')
    parser.add_argument('--skip-tickets', action='store_true', help='Skip fetching tickets')
    return parser.parse_args()

def main():
    """Main function to load data and analyze activity."""
    # Load environment variables from .env file
    load_dotenv()

    # Check if API key is set
    api_key = os.getenv('FRESHSERVICE_API_KEY')
    domain = os.getenv('FRESHSERVICE_DOMAIN')

    if not api_key:
        logger.error("FRESHSERVICE_API_KEY environment variable is not set")
        print("Error: FRESHSERVICE_API_KEY environment variable is not set.")
        print("Please set this in your .env file or environment variables.")
        return

    if not domain:
        logger.error("FRESHSERVICE_DOMAIN environment variable is not set")
        print("Error: FRESHSERVICE_DOMAIN environment variable is not set.")
        print("Please set this in your .env file or environment variables.")
        return

    # Manually set the API key and domain in settings
    settings.FRESHSERVICE_CONFIG['api_key'] = api_key
    settings.FRESHSERVICE_CONFIG['domain'] = domain

    print(f"Using FreshService domain: {domain}")

    args = parse_arguments()

    logger.info(f"Starting ticket load and agent activity analysis at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Looking back {args.days} days for tickets and {args.minutes} minutes for agent activity")

    # Step 1: Calculate the date to look back for tickets
    updated_since = (datetime.now().astimezone().replace(microsecond=0) - timedelta(days=args.days)).isoformat()

    # Step 2: Fetch tickets from FreshService (if not skipped)
    if not args.skip_tickets:
        logger.info(f"Fetching tickets updated since {updated_since}")
        ticket_count = fetch_tickets(updated_since=updated_since)
        logger.info(f"Fetched and stored {ticket_count} tickets")
    else:
        logger.info("Skipping ticket fetch as requested")
        ticket_count = 1  # Set to non-zero to allow history fetch if not skipped

    # Step 3: Fetch history for all tickets (if not skipped)
    if ticket_count > 0 and not args.skip_history:
        logger.info(f"Fetching history for up to {args.max_tickets} tickets")
        history_count = fetch_all_ticket_history(max_tickets=args.max_tickets)
        logger.info(f"Processed history for {history_count} tickets")
    elif args.skip_history:
        logger.info("Skipping ticket history fetch as requested")
    else:
        logger.warning("No tickets found, skipping history fetch")

    # Step 4: Analyze recent agent activity
    logger.info(f"Analyzing agent activity in the last {args.minutes} minutes")
    results = analyze_recent_agent_activity(minutes=args.minutes)

    # Step 5: Display results
    if results:
        print("\n" + "="*50)
        print(f"AGENT ACTIVITY ANALYSIS - Last {args.minutes} minutes")
        print("="*50)
        print(f"Analysis timestamp: {results['timestamp']}")
        print(f"Total actions: {results['action_count']}")
        print(f"Unique agents: {results['unique_actors']}")
        print(f"Active tickets: {results['active_tickets']}")

        if results['actor_counts']:
            print("\nTop agents by activity:")
            for agent, count in sorted(results['actor_counts'].items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  {agent}: {count} actions")

        if results['actions']:
            print("\nMost recent actions:")
            for action in sorted(results['actions'], key=lambda x: x['timestamp'], reverse=True)[:5]:
                print(f"  {action['timestamp']} - {action['actor']} - {action['action']} - Ticket #{action['ticket_id']}")

        print("\nResults stored in database table: 'recent_agent_activity'")
        print("="*50)
    else:
        print("\nNo recent agent activity found or error occurred during analysis.")

    logger.info(f"Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error in main process: {str(e)}", exc_info=True)
        sys.exit(1)
