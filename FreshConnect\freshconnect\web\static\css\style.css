/* FreshConnect Web UI Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    padding-top: 56px;
}

.navbar-brand {
    font-weight: bold;
}

.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.main-content {
    padding: 2rem 1.5rem;
}

.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    font-weight: 500;
}

.form-group {
    margin-bottom: 1rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.table {
    background-color: #fff;
}

.table thead th {
    border-top: none;
    background-color: #f8f9fa;
}

.alert {
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
    margin-bottom: 1.5rem;
}

.dashboard-stat {
    text-align: center;
    padding: 1.5rem;
}

.dashboard-stat .stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-stat .stat-label {
    font-size: 1rem;
    color: #6c757d;
}

.footer {
    padding: 1.5rem 0;
    color: #6c757d;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}
