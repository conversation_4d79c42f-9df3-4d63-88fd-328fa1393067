# FreshConnect

FreshConnect is a Python package for integrating with FreshService, analyzing ticket data, and automating workflows.

## Features

- **API Integration**: Unified client for FreshService API
- **Data Storage**: SQLite database for reliable data storage
- **Ticket Processing**: Fetch and analyze ticket data
- **Webhook Handling**: Receive and process FreshService webhooks
- **Web Interface**: Dashboard with agent performance metrics, ticket analytics, service catalog management, and webhook handling
- **Command-Line Interface**: Manage operations from the command line

## Original Goals

- Provide management insight on defined metrics such as ticket touches, articles written or updated, escalating between levels and teams.
- Connect discrete systems together for automations and sync:
  - FreshService to ITAM when something is assigned
  - ITAM to FreshService when an asset is created
- Get the tangled mess of Automator events under control

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/freshconnect.git
cd freshconnect

# Install the package
pip install -e .
```

## Configuration

FreshConnect uses environment variables for configuration. Create a `.env` file in the project root:

```
# FreshService API Configuration
FRESHSERVICE_DOMAIN=yourdomain.freshservice.com
FRESHSERVICE_API_KEY=your_api_key

# Database Configuration
DB_PATH=storage/freshconnect.db

# Web Server Configuration
WEB_HOST=0.0.0.0
WEB_PORT=5000
WEB_DEBUG=False
```

## Usage

### Command-Line Interface

```bash
# Fetch tickets updated in the last 7 days
freshconnect fetch-tickets --days 7

# Fetch history for a specific ticket
freshconnect fetch-history --ticket-id 12345

# Fetch history for all tickets
freshconnect fetch-history --all

# Analyze agent activity
freshconnect analyze-agents

# Generate agent report
freshconnect generate-agent-report

# Fetch service catalog items
freshconnect fetch-services

# Run the web server
freshconnect web

# Or use the dedicated script
python run_web_app.py
```

### Python API

```python
from freshconnect.api.freshservice import FreshServiceClient
from freshconnect.processors.tickets import fetch_tickets

# Create a FreshService client
client = FreshServiceClient()

# Fetch tickets
tickets = client.get_tickets(updated_since='2023-01-01T00:00:00Z')

# Process tickets
fetch_tickets(updated_since='2023-01-01T00:00:00Z')

# Analyze agent activity
from freshconnect.processors.agents import analyze_agent_activity
activity_data = analyze_agent_activity()

# Fetch agent details
from freshconnect.processors.agents import fetch_agent_details
agent_details = fetch_agent_details()

# Fetch service catalog items
from freshconnect.processors.services import fetch_service_items
service_items = fetch_service_items()
```

### Web Interface

FreshConnect includes a web interface that provides a user-friendly way to interact with the FreshService data and analytics. The web interface includes:

- **Dashboard**: Overview of key metrics, agent performance, and recent activity
- **Tickets**: View and analyze ticket data
- **Agents**: View agent details, activity, and performance metrics
- **Services**: Manage and analyze the service catalog
- **Webhooks**: View and manage webhook events
- **Settings**: Configure the application

To access the web interface, run the web server and navigate to `http://localhost:5000` in your browser:

```bash
python run_web_app.py
```

## Project Structure

```
freshconnect/
├── __init__.py
├── __main__.py
├── cli.py
├── config/
│   ├── __init__.py
│   └── settings.py
├── api/
│   ├── __init__.py
│   └── freshservice.py
├── data/
│   ├── __init__.py
│   ├── database.py
│   └── backends/
│       ├── __init__.py
│       ├── tinydb_backend.py
│       └── sqlite_backend.py
├── processors/
│   ├── __init__.py
│   ├── tickets.py
│   ├── agents.py
│   └── services.py
├── web/
│   ├── __init__.py
│   └── app.py
└── utils/
    └── __init__.py
```

## Development

### Setting Up a Development Environment

```bash
# Create a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -e ".[dev]"
```

## Next Steps

- Add comprehensive test suite
- Enhance the web dashboard with additional analytics
- Implement more robust error handling
- Add documentation for all modules
- Optimize database queries for better performance

## Standalone Scripts

Standalone scripts that were previously part of this repository have been moved to a separate directory (`StandaloneScripts`). These include:

- Reconciliation scripts
- Bill processing scripts
- Legacy utility scripts

These scripts are not part of the modular FreshConnect package and are maintained separately.
