{% extends "base.html" %}

{% block title %}FreshConnect - Webhooks{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-plug me-2"></i>Webhooks</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshEvents">
                <i class="fas fa-sync-alt me-1"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#webhookInfoModal">
                <i class="fas fa-info-circle me-1"></i> Setup Info
            </button>
        </div>
    </div>
</div>

<!-- Webhook URL Card -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-link me-1"></i> Webhook URL
            </div>
            <div class="card-body">
                <div class="input-group">
                    <input type="text" class="form-control" id="webhookUrl" value="{{ webhook_url }}" readonly>
                    <button class="btn btn-outline-secondary" type="button" id="copyWebhookUrl">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
                <div class="form-text">
                    Use this URL in FreshService to send webhook events to FreshConnect.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Events -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-history me-1"></i> Recent Webhook Events
                    </div>
                    <div>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary" id="clearEvents">
                                <i class="fas fa-trash-alt me-1"></i> Clear Events
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Timestamp</th>
                                <th>Event Type</th>
                                <th>Source</th>
                                <th>Status</th>
                                <th>Actions Taken</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for event in webhook_events|default([]) %}
                            <tr>
                                <td>{{ event.id }}</td>
                                <td>{{ event.timestamp }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ event.event_type }}</span>
                                </td>
                                <td>{{ event.source }}</td>
                                <td>
                                    {% if event.status == 'received' %}
                                    <span class="badge bg-info">Received</span>
                                    {% elif event.status == 'processed' %}
                                    <span class="badge bg-success">Processed</span>
                                    {% elif event.status == 'error' %}
                                    <span class="badge bg-danger">Error</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ event.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ event.action_taken|default('None') }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-primary view-payload-btn" data-event-id="{{ event.id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center">No webhook events found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if pagination and pagination.total_pages > 1 %}
                <nav aria-label="Webhook event pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item {% if pagination.current_page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('webhooks', page=pagination.current_page-1) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        {% for page in range(1, pagination.total_pages + 1) %}
                        <li class="page-item {% if page == pagination.current_page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('webhooks', page=page) }}">{{ page }}</a>
                        </li>
                        {% endfor %}
                        
                        <li class="page-item {% if pagination.current_page == pagination.total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('webhooks', page=pagination.current_page+1) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Webhook Info Modal -->
<div class="modal fade" id="webhookInfoModal" tabindex="-1" aria-labelledby="webhookInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webhookInfoModalLabel">Webhook Setup Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5>Setting Up Webhooks in FreshService</h5>
                <p>Follow these steps to set up webhooks in FreshService:</p>
                
                <ol>
                    <li>Log in to your FreshService admin account</li>
                    <li>Go to <strong>Admin</strong> &gt; <strong>Workflows</strong> &gt; <strong>Automator</strong></li>
                    <li>Click <strong>Create Automator</strong></li>
                    <li>Select the event type you want to trigger the webhook (e.g., Ticket Created, Ticket Updated)</li>
                    <li>Add any conditions you want to apply</li>
                    <li>For the action, select <strong>Trigger Webhook</strong></li>
                    <li>Enter the webhook URL: <code>{{ webhook_url }}</code></li>
                    <li>Save the automator rule</li>
                </ol>
                
                <h5>Supported Webhook Events</h5>
                <p>FreshConnect supports the following webhook events:</p>
                
                <ul>
                    <li><strong>ticket_created</strong>: Triggered when a new ticket is created</li>
                    <li><strong>ticket_updated</strong>: Triggered when a ticket is updated</li>
                    <li><strong>service_item_updated</strong>: Triggered when a service catalog item is updated</li>
                    <li><strong>agent_assignment_updated</strong>: Triggered when a ticket is assigned to an agent</li>
                </ul>
                
                <h5>Webhook Payload Format</h5>
                <p>FreshService sends webhook payloads in JSON format. The payload structure varies depending on the event type.</p>
                
                <div class="alert alert-info">
                    <p>For more information about FreshService webhooks, refer to the <a href="https://api.freshservice.com/v2/#webhooks" target="_blank">FreshService API documentation</a>.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Payload Modal -->
<div class="modal fade" id="payloadModal" tabindex="-1" aria-labelledby="payloadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="payloadModalLabel">Webhook Payload</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="payloadContent" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="copyPayload">
                    <i class="fas fa-copy me-1"></i> Copy Payload
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy webhook URL button
        document.getElementById('copyWebhookUrl').addEventListener('click', function() {
            const webhookUrl = document.getElementById('webhookUrl');
            webhookUrl.select();
            document.execCommand('copy');
            
            // Show a tooltip or change button text temporarily
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check"></i> Copied!';
            setTimeout(() => {
                this.innerHTML = originalText;
            }, 2000);
        });
        
        // Refresh events button
        document.getElementById('refreshEvents').addEventListener('click', function() {
            window.location.reload();
        });
        
        // Clear events button
        document.getElementById('clearEvents').addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all webhook events? This action cannot be undone.')) {
                // Send a request to clear events
                fetch('{{ url_for("clear_webhook_events") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Error clearing webhook events: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while clearing webhook events.');
                });
            }
        });
        
        // View payload buttons
        document.querySelectorAll('.view-payload-btn').forEach(button => {
            button.addEventListener('click', function() {
                const eventId = this.getAttribute('data-event-id');
                
                // Fetch the payload data
                fetch(`{{ url_for("get_webhook_payload", event_id=0) }}`.replace('0', eventId))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Format and display the payload
                        const payloadContent = document.getElementById('payloadContent');
                        payloadContent.textContent = JSON.stringify(data.payload, null, 2);
                        
                        // Show the modal
                        const modal = new bootstrap.Modal(document.getElementById('payloadModal'));
                        modal.show();
                    } else {
                        alert('Error fetching payload: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while fetching the payload.');
                });
            });
        });
        
        // Copy payload button
        document.getElementById('copyPayload').addEventListener('click', function() {
            const payloadContent = document.getElementById('payloadContent');
            
            // Create a temporary textarea element
            const textarea = document.createElement('textarea');
            textarea.value = payloadContent.textContent;
            document.body.appendChild(textarea);
            
            // Select and copy the text
            textarea.select();
            document.execCommand('copy');
            
            // Remove the temporary textarea
            document.body.removeChild(textarea);
            
            // Show a tooltip or change button text temporarily
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check me-1"></i> Copied!';
            setTimeout(() => {
                this.innerHTML = originalText;
            }, 2000);
        });
    });
</script>
{% endblock %}
