"""
Script Launcher - A Flask application for running standalone scripts.

This application provides a web interface for browsing and running standalone scripts
with appropriate parameters.
"""

import os
import sys
import json
import shutil
import tempfile
import subprocess
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify

from scripts_db import init_db, get_all_scripts, get_script_by_id, add_script, update_script, delete_script

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.urandom(24)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size
app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()  # Temporary directory for uploads

# Initialize database
db_session = init_db()

# Root directory for scripts
SCRIPTS_ROOT = Path(__file__).parent.parent

# Allowed file extensions
ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv', 'pdf'}

def allowed_file(filename):
    """Check if a file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def handle_file_upload(file_obj):
    """Save an uploaded file to the temporary directory."""
    if file_obj and allowed_file(file_obj.filename):
        filename = secure_filename(file_obj.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file_obj.save(file_path)
        return file_path
    return None

@app.route('/')
def index():
    """Home page with list of available scripts."""
    scripts = get_all_scripts()
    return render_template('index.html', scripts=scripts)

@app.route('/script/<int:script_id>')
def script_form(script_id):
    """Display form for a specific script."""
    script = get_script_by_id(script_id)
    if not script:
        flash('Script not found', 'danger')
        return redirect(url_for('index'))

    return render_template('script_form.html', script=script)

@app.route('/run_script/<int:script_id>', methods=['POST'])
def run_script(script_id):
    """Run a script with the provided parameters."""
    script = get_script_by_id(script_id)
    if not script:
        return jsonify({'success': False, 'message': 'Script not found'})

    # Get parameters from form and handle file uploads
    params = {}
    uploaded_files = []

    for param in script['parameters']:
        param_name = param['name']

        if param['type'] == 'file':
            # Handle file upload
            if param_name in request.files:
                file_obj = request.files[param_name]
                file_path = handle_file_upload(file_obj)
                if file_path:
                    params[param_name] = file_path
                    uploaded_files.append(file_path)
                else:
                    return jsonify({
                        'success': False,
                        'message': f'Invalid file for parameter {param_name}'
                    })
            elif param['required']:
                return jsonify({
                    'success': False,
                    'message': f'Required file parameter {param_name} is missing'
                })
        else:
            # Handle regular form parameters
            param_value = request.form.get(param_name, '')
            params[param_name] = param_value

    # Prepare command
    script_path = os.path.join(SCRIPTS_ROOT, script['path'])

    try:
        # Run the script
        if script['type'] == 'python':
            result = run_python_script(script_path, params)
        elif script['type'] == 'powershell':
            result = run_powershell_script(script_path, params)
        else:
            return jsonify({'success': False, 'message': f'Unsupported script type: {script["type"]}'})

        # Clean up uploaded files
        for file_path in uploaded_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                app.logger.error(f"Error removing temporary file {file_path}: {str(e)}")

        return jsonify({
            'success': True,
            'message': 'Script executed successfully',
            'output': result
        })
    except Exception as e:
        # Clean up uploaded files on error
        for file_path in uploaded_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as cleanup_error:
                app.logger.error(f"Error removing temporary file {file_path}: {str(cleanup_error)}")

        return jsonify({
            'success': False,
            'message': f'Error executing script: {str(e)}'
        })

def run_python_script(script_path, params):
    """Run a Python script with the given parameters."""
    cmd = [sys.executable, script_path]

    # Add parameters
    for name, value in params.items():
        if value:  # Only add non-empty parameters
            cmd.append(f"--{name}")
            cmd.append(value)

    # Run the script
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode != 0:
        raise Exception(f"Script execution failed: {result.stderr}")

    return result.stdout

def run_powershell_script(script_path, params):
    """Run a PowerShell script with the given parameters."""
    cmd = ['powershell', '-ExecutionPolicy', 'Bypass', '-File', script_path]

    # Add parameters
    for name, value in params.items():
        if value:  # Only add non-empty parameters
            cmd.append(f"-{name}")
            cmd.append(value)

    # Run the script
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode != 0:
        raise Exception(f"Script execution failed: {result.stderr}")

    return result.stdout

@app.route('/admin')
def admin():
    """Admin page for managing scripts."""
    scripts = get_all_scripts()
    return render_template('admin.html', scripts=scripts)

@app.route('/admin/add', methods=['GET', 'POST'])
def add_script_route():
    """Add a new script."""
    if request.method == 'POST':
        script_data = {
            'name': request.form['name'],
            'description': request.form['description'],
            'path': request.form['path'],
            'type': request.form['type'],
            'category': request.form['category'],
            'parameters': json.loads(request.form['parameters'])
        }

        add_script(script_data)
        flash('Script added successfully', 'success')
        return redirect(url_for('admin'))

    return render_template('add_script.html')

@app.route('/admin/edit/<int:script_id>', methods=['GET', 'POST'])
def edit_script(script_id):
    """Edit an existing script."""
    script = get_script_by_id(script_id)
    if not script:
        flash('Script not found', 'danger')
        return redirect(url_for('admin'))

    if request.method == 'POST':
        script_data = {
            'id': script_id,
            'name': request.form['name'],
            'description': request.form['description'],
            'path': request.form['path'],
            'type': request.form['type'],
            'category': request.form['category'],
            'parameters': json.loads(request.form['parameters'])
        }

        update_script(script_data)
        flash('Script updated successfully', 'success')
        return redirect(url_for('admin'))

    return render_template('edit_script.html', script=script)

@app.route('/admin/delete/<int:script_id>', methods=['POST'])
def delete_script_route(script_id):
    """Delete a script."""
    delete_script(script_id)
    flash('Script deleted successfully', 'success')
    return redirect(url_for('admin'))

@app.route('/api/scripts')
def api_scripts():
    """API endpoint to get all scripts."""
    scripts = get_all_scripts()
    return jsonify(scripts)

@app.route('/api/script/<int:script_id>')
def api_script(script_id):
    """API endpoint to get a specific script."""
    script = get_script_by_id(script_id)
    if not script:
        return jsonify({'error': 'Script not found'}), 404
    return jsonify(script)

if __name__ == '__main__':
    app.run(debug=True, port=5001)  # Use a different port than FreshConnect
