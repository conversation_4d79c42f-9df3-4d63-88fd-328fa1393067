from flask import Flask, request, jsonify
from webhook_db import init_db, WebhookEvent
import json

app = Flask(__name__)
db_session = init_db()

@app.route('/webhook', methods=['POST'])
def webhook():
    try:
        event_type = request.headers.get('X-Event-Type', 'unknown')
        payload = request.get_json()
        
        # Store the webhook event
        event = WebhookEvent(
            event_type=event_type,
            payload=payload
        )
        db_session.add(event)
        db_session.commit()
        
        return jsonify({'status': 'success'}), 200
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/events', methods=['GET'])
def list_events():
    events = db_session.query(WebhookEvent).all()
    return jsonify([{
        'id': e.id,
        'timestamp': e.timestamp.isoformat(),
        'event_type': e.event_type,
        'payload': e.payload
    } for e in events])

if __name__ == '__main__':
    app.run(debug=True, port=5000)
