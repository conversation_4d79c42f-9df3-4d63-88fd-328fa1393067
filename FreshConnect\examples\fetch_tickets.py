"""
Example script to fetch tickets from FreshService.
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the parent directory to the path so we can import freshconnect
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file
load_dotenv()

# Import freshconnect modules
from freshconnect.api.freshservice import FreshServiceClient
from freshconnect.processors.tickets import fetch_tickets
from freshconnect.config import settings

# Set up logging
settings.setup_logging()
logger = logging.getLogger(__name__)

def main():
    """Main function to demonstrate fetching tickets."""
    try:
        # Create a FreshService client
        client = FreshServiceClient()
        
        # Calculate date 7 days ago
        seven_days_ago = (datetime.utcnow() - timedelta(days=7)).isoformat()
        
        # Fetch tickets directly using the API client
        logger.info(f"Fetching tickets updated since {seven_days_ago}")
        response = client.get_tickets(updated_since=seven_days_ago)
        tickets = response.get('tickets', [])
        logger.info(f"Fetched {len(tickets)} tickets directly using the API client")
        
        # Print the first ticket (if any)
        if tickets:
            logger.info(f"First ticket: #{tickets[0]['id']} - {tickets[0]['subject']}")
        
        # Fetch and store tickets using the processor
        logger.info("Fetching and storing tickets using the processor")
        count = fetch_tickets(updated_since=seven_days_ago)
        logger.info(f"Fetched and stored {count} tickets using the processor")
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
