#!/usr/bin/env python3
"""
Department ID Extractor

This script reads the departments JSON file and creates a simple CSV/text file
with just the department IDs and names for easy reference.

Usage:
    python extract_department_ids.py <departments_json_file>

Example:
    python extract_department_ids.py freshservice_departments_20250711_152359.json
"""

import sys
import json
import csv
from datetime import datetime


def extract_department_ids(json_file):
    """
    Extract department IDs and names from the JSON file.
    
    Args:
        json_file (str): Path to the departments JSON file
        
    Returns:
        list: List of tuples (id, name)
    """
    print(f"Loading departments from: {json_file}")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    departments = data.get('departments', [])
    
    # Extract ID and name pairs
    dept_list = []
    for dept in departments:
        dept_id = dept.get('id')
        dept_name = dept.get('name', 'Unknown')
        if dept_id:
            dept_list.append((dept_id, dept_name))
    
    # Sort by name for easier reading
    dept_list.sort(key=lambda x: x[1].lower())
    
    print(f"Extracted {len(dept_list)} departments")
    return dept_list


def save_to_csv(dept_list, output_file):
    """Save department list to CSV file."""
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Department_ID', 'Department_Name'])
        writer.writerows(dept_list)
    
    print(f"CSV file saved: {output_file}")


def save_to_text(dept_list, output_file):
    """Save department list to text file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("FreshService Department IDs and Names\n")
        f.write("=" * 50 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Departments: {len(dept_list)}\n\n")
        
        for dept_id, dept_name in dept_list:
            f.write(f"{dept_id:>12} | {dept_name}\n")
    
    print(f"Text file saved: {output_file}")


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python extract_department_ids.py <departments_json_file>")
        print("Example: python extract_department_ids.py freshservice_departments_20250711_152359.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        # Extract department data
        dept_list = extract_department_ids(json_file)
        
        if not dept_list:
            print("No departments found in the file.")
            sys.exit(1)
        
        # Generate output filenames
        base_name = json_file.replace('.json', '')
        csv_file = f"{base_name}_ids.csv"
        txt_file = f"{base_name}_ids.txt"
        
        # Save in both formats
        save_to_csv(dept_list, csv_file)
        save_to_text(dept_list, txt_file)
        
        # Print summary to console
        print(f"\nDepartment Summary:")
        print("=" * 50)
        for dept_id, dept_name in dept_list:
            print(f"{dept_id:>12} | {dept_name}")
        
        print(f"\nFiles created:")
        print(f"  CSV: {csv_file}")
        print(f"  Text: {txt_file}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
