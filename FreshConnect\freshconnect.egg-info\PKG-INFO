Metadata-Version: 2.4
Name: freshconnect
Version: 0.1.0
Summary: A package for integrating with FreshService and analyzing ticket data
Author: IT Team
Author-email: <EMAIL>
Keywords: freshservice,tickets,analytics
Requires-Python: >=3.6
Requires-Dist: requests>=2.25.0
Requires-Dist: tinydb>=4.5.0
Requires-Dist: flask>=2.0.0
Requires-Dist: sqlalchemy>=1.4.0
Requires-Dist: python-dotenv>=0.19.0
Requires-Dist: plotly>=5.3.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: openpyxl>=3.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-cov>=4.0.0; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: keywords
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
