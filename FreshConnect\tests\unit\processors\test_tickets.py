"""
Unit tests for the tickets processor module.
"""

import pytest
from unittest.mock import patch, MagicMock

from freshconnect.processors.tickets import TicketProcessor, fetch_tickets, fetch_ticket_history, fetch_all_ticket_history


@pytest.mark.unit
@pytest.mark.processors
class TestTicketProcessor:
    """Tests for the TicketProcessor class."""
    
    def test_init(self, mock_api_client, mock_db):
        """Test initialization of the processor."""
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        assert processor.api_client is mock_api_client
        assert processor.db is mock_db
    
    def test_fetch_and_store_tickets(self, mock_api_client, mock_db):
        """Test fetching and storing tickets."""
        # Create processor with mocks
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        
        # Call the method
        count = processor.fetch_and_store_tickets(updated_since="2023-01-01T00:00:00Z")
        
        # Verify the API was called
        mock_api_client.get_tickets.assert_called_once_with("2023-01-01T00:00:00Z", 1, 30)
        
        # Verify the database was updated
        assert count == 2  # Two tickets in the mock response
    
    def test_fetch_and_store_tickets_with_max(self, mock_api_client, mock_db):
        """Test fetching and storing tickets with a maximum limit."""
        # Create processor with mocks
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        
        # Call the method with max_tickets=1
        count = processor.fetch_and_store_tickets(updated_since="2023-01-01T00:00:00Z", max_tickets=1)
        
        # Verify the API was called
        mock_api_client.get_tickets.assert_called_once_with("2023-01-01T00:00:00Z", 1, 30)
        
        # Verify only one ticket was processed
        assert count == 1
    
    def test_fetch_and_store_ticket_history(self, mock_api_client, mock_db):
        """Test fetching and storing ticket history."""
        # Create processor with mocks
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        
        # Call the method
        history = processor.fetch_and_store_ticket_history(1)
        
        # Verify the API was called
        mock_api_client.get_ticket.assert_called_once_with(1)
        mock_api_client.get_ticket_history.assert_called_once_with(1)
        
        # Verify the result
        assert history is not None
        assert history["ticket_id"] == 1
        assert "ticket" in history
        assert "history" in history
    
    def test_fetch_and_store_all_ticket_history(self, mock_api_client, mock_db):
        """Test fetching and storing history for all tickets."""
        # Create processor with mocks
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        
        # Call the method
        count = processor.fetch_and_store_all_ticket_history()
        
        # Verify the API was called for each ticket
        assert mock_api_client.get_ticket.call_count == 2
        assert mock_api_client.get_ticket_history.call_count == 2
        
        # Verify the result
        assert count == 2
    
    def test_fetch_and_store_all_ticket_history_with_max(self, mock_api_client, mock_db):
        """Test fetching and storing history for all tickets with a maximum limit."""
        # Create processor with mocks
        processor = TicketProcessor(api_client=mock_api_client, db=mock_db)
        
        # Call the method with max_tickets=1
        count = processor.fetch_and_store_all_ticket_history(max_tickets=1)
        
        # Verify the API was called for only one ticket
        assert mock_api_client.get_ticket.call_count == 1
        assert mock_api_client.get_ticket_history.call_count == 1
        
        # Verify the result
        assert count == 1


@pytest.mark.unit
@pytest.mark.processors
class TestTicketFunctions:
    """Tests for the ticket processor functions."""
    
    @patch('freshconnect.processors.tickets.TicketProcessor')
    def test_fetch_tickets(self, mock_processor_class):
        """Test the fetch_tickets function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.fetch_and_store_tickets.return_value = 2
        mock_processor_class.return_value = mock_processor
        
        # Call the function
        count = fetch_tickets(updated_since="2023-01-01T00:00:00Z", max_tickets=10)
        
        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.fetch_and_store_tickets.assert_called_once_with("2023-01-01T00:00:00Z", 10)
        assert count == 2
    
    @patch('freshconnect.processors.tickets.TicketProcessor')
    def test_fetch_ticket_history(self, mock_processor_class):
        """Test the fetch_ticket_history function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.fetch_and_store_ticket_history.return_value = {"ticket_id": 1}
        mock_processor_class.return_value = mock_processor
        
        # Call the function
        history = fetch_ticket_history(1)
        
        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.fetch_and_store_ticket_history.assert_called_once_with(1)
        assert history == {"ticket_id": 1}
    
    @patch('freshconnect.processors.tickets.TicketProcessor')
    def test_fetch_all_ticket_history(self, mock_processor_class):
        """Test the fetch_all_ticket_history function."""
        # Setup mock
        mock_processor = MagicMock()
        mock_processor.fetch_and_store_all_ticket_history.return_value = 2
        mock_processor_class.return_value = mock_processor
        
        # Call the function
        count = fetch_all_ticket_history(max_tickets=10)
        
        # Verify the processor was created and called
        mock_processor_class.assert_called_once()
        mock_processor.fetch_and_store_all_ticket_history.assert_called_once_with(10)
        assert count == 2
