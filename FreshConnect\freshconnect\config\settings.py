"""
Configuration settings for the FreshConnect application.

This module loads configuration from environment variables and provides
default values where appropriate.
"""

import os
import logging
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).resolve().parent.parent.parent
STORAGE_DIR = os.path.join(BASE_DIR, 'storage')

# Ensure storage directory exists
os.makedirs(STORAGE_DIR, exist_ok=True)

# FreshService API Configuration
FRESHSERVICE_CONFIG = {
    'domain': os.getenv('FRESHSERVICE_DOMAIN', 'kaservicedesk.freshservice.com'),
    'api_key': os.getenv('FRESHSERVICE_API_KEY', ''),
    'timeout': int(os.getenv('FRESHSERVICE_TIMEOUT', '30')),
}

# Database Configuration
DATABASE_CONFIG = {
    'type': 'sqlite',  # Only SQLite is supported
    'path': os.getenv('DB_PATH', os.path.join(STORAGE_DIR, 'freshconnect.db')),
}

# SQLite Database Configuration (for webhooks)
SQLITE_CONFIG = {
    'path': os.getenv('SQLITE_PATH', os.path.join(STORAGE_DIR, 'webhooks.db')),
    'echo': os.getenv('SQLITE_ECHO', 'False').lower() == 'true',
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'format': os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
    'file': os.getenv('LOG_FILE', os.path.join(STORAGE_DIR, 'freshconnect.log')),
    'max_bytes': int(os.getenv('LOG_MAX_BYTES', 10485760)),  # 10MB
    'backup_count': int(os.getenv('LOG_BACKUP_COUNT', 5)),
}

# Web Server Configuration
WEB_CONFIG = {
    'host': os.getenv('WEB_HOST', '0.0.0.0'),
    'port': int(os.getenv('WEB_PORT', '5000')),
    'debug': os.getenv('WEB_DEBUG', 'False').lower() == 'true',
}

# Initialize logging
def setup_logging():
    """Set up logging based on configuration."""
    log_level = getattr(logging, LOGGING_CONFIG['level'].upper(), logging.INFO)

    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(LOGGING_CONFIG['file'])
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=log_level,
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.StreamHandler(),  # Console handler
            logging.handlers.RotatingFileHandler(
                LOGGING_CONFIG['file'],
                maxBytes=LOGGING_CONFIG['max_bytes'],
                backupCount=LOGGING_CONFIG['backup_count']
            )
        ]
    )

    # Reduce verbosity of some loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
