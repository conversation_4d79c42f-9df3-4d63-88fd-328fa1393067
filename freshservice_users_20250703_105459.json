[{"active": true, "address": null, "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2021-12-10T16:47:24Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": null, "ignore_update_notifications": true, "internal_service_account": false, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "4444", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": null, "language": "en", "last_name": "Kingspan Group IT", "location_id": null, "location_name": null, "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2024-02-14T13:34:39Z", "vip_user": false, "work_phone_number": null, "work_schedule_id": null}, {"active": true, "address": null, "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2024-06-25T17:31:03Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": null, "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "<EMAIL>", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": null, "language": "en", "last_name": null, "location_id": null, "location_name": null, "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "24h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2024-06-25T17:32:05Z", "vip_user": false, "work_phone_number": null, "work_schedule_id": null}, {"active": true, "address": "726 Summerhill Drive, DeLand, FL, 32724", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2025-04-02T16:09:05Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": "Quality", "ignore_update_notifications": null, "internal_service_account": null, "upn": "<PERSON>.<EMAIL>"}, "department_ids": [***********], "department_names": ["Kingspan KNA - Insulated Panels"], "external_id": null, "first_name": "45 days", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": "Quality Technician", "language": "en", "last_name": "<PERSON><PERSON>", "location_id": ***********, "location_name": "KNA - Deland", "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": ***********, "secondary_emails": [], "time_format": "24h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2025-06-02T16:05:05Z", "vip_user": false, "work_phone_number": "************", "work_schedule_id": null}, {"active": true, "address": null, "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2025-04-08T18:51:43Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": null, "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "A_group_e_mail_account_to_share_access_to_3d_printers", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": null, "language": "en", "last_name": null, "location_id": null, "location_name": null, "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "24h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2025-04-08T18:51:43Z", "vip_user": false, "work_phone_number": null, "work_schedule_id": null}, {"active": true, "address": null, "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2024-11-15T16:51:26Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": null, "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "<PERSON><PERSON><PERSON>", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": null, "language": "en", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "location_id": null, "location_name": null, "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "24h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2024-11-15T16:52:31Z", "vip_user": false, "work_phone_number": null, "work_schedule_id": null}, {"active": false, "address": "52 Springvale Road, Red Lion, PA, 17356, United States", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2021-10-12T15:35:56Z", "custom_fields": {"home_phone_ad_2687910608": "************", "department_ad_2992669570": "Engineering", "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "<PERSON>", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": "Manufacturing Engineer", "language": "en", "last_name": "<PERSON>", "location_id": ***********, "location_name": "KNA - Red Lion", "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2021-11-23T17:11:44Z", "vip_user": false, "work_phone_number": "************", "work_schedule_id": null}, {"active": true, "address": "5202-272nd Street, Langley, BC, V4W 1S3, Canada", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2021-10-12T15:36:00Z", "custom_fields": {"home_phone_ad_2687910608": "************", "department_ad_2992669570": "Operations", "ignore_update_notifications": null, "internal_service_account": null, "upn": "<PERSON><PERSON>@kna.kingspan.net"}, "department_ids": [***********], "department_names": ["Kingspan KNA - Insulated Panels"], "external_id": null, "first_name": "<PERSON>", "has_logged_in": true, "id": ***********, "is_agent": false, "job_title": "Operations Manager", "language": "en", "last_name": "<PERSON><PERSON><PERSON>", "location_id": ***********, "location_name": "KNA - Langley", "mobile_phone_number": "************", "primary_email": "<EMAIL>", "reporting_manager_id": ***********, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2025-06-19T22:09:21Z", "vip_user": false, "work_phone_number": "************ X4801", "work_schedule_id": null}, {"active": true, "address": "5050 South Service Road, Unit 200, Burlington, ON, L7L 5Y7, Canada", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2021-10-13T02:56:00Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": "Domestic Sales", "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [***********], "department_names": ["Kingspan KNA - Vicwest"], "external_id": null, "first_name": "<PERSON>", "has_logged_in": true, "id": ***********, "is_agent": false, "job_title": "Regional Sales Coordinator, ICI", "language": "en", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location_id": ***********, "location_name": "VBP - Burlington", "mobile_phone_number": "************", "primary_email": "<EMAIL>", "reporting_manager_id": ***********, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2024-04-08T11:25:43Z", "vip_user": false, "work_phone_number": "******-795-0965", "work_schedule_id": null}, {"active": false, "address": "7510 Montevideo Road, Jessup, MD, 20794, United States", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2022-04-08T16:05:18Z", "custom_fields": {"home_phone_ad_2687910608": null, "department_ad_2992669570": null, "ignore_update_notifications": null, "internal_service_account": null, "upn": null}, "department_ids": [], "department_names": null, "external_id": null, "first_name": "<PERSON>", "has_logged_in": false, "id": ***********, "is_agent": false, "job_title": "CI Consultant", "language": "en", "last_name": "<PERSON><PERSON>", "location_id": ***********, "location_name": "KNA - <PERSON><PERSON>", "mobile_phone_number": null, "primary_email": "<EMAIL>", "reporting_manager_id": null, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2024-07-08T16:18:02Z", "vip_user": false, "work_phone_number": "************", "work_schedule_id": null}, {"active": true, "address": "720 Marion Road, Columbus, OH, 43207, United States", "background_information": null, "can_see_all_changes_from_associated_departments": false, "can_see_all_tickets_from_associated_departments": false, "created_at": "2023-03-01T17:10:22Z", "custom_fields": {"home_phone_ad_2687910608": "************", "department_ad_2992669570": "Production", "ignore_update_notifications": null, "internal_service_account": null, "upn": "<PERSON><PERSON>@kna.kingspan.net"}, "department_ids": [***********], "department_names": ["Kingspan KNA - Insulated Panels"], "external_id": null, "first_name": "<PERSON>", "has_logged_in": true, "id": ***********, "is_agent": false, "job_title": "Production Manager", "language": "en", "last_name": "<PERSON><PERSON><PERSON>", "location_id": ***********, "location_name": "KNA - Columbus", "mobile_phone_number": "************", "primary_email": "<EMAIL>", "reporting_manager_id": ***********, "secondary_emails": [], "time_format": "12h", "time_zone": "Eastern Time (US & Canada)", "updated_at": "2025-07-01T17:29:09Z", "vip_user": false, "work_phone_number": "************ X4602", "work_schedule_id": null}]