@echo off
echo Running Agent Activity Monitor
cd %~dp0

REM Set default values
set DAYS=1
set MINUTES=10
set MAX_TICKETS=20

REM Parse command line arguments
:parse
if "%~1"=="" goto :execute
if /i "%~1"=="--days" set DAYS=%~2&shift&shift&goto :parse
if /i "%~1"=="--minutes" set MINUTES=%~2&shift&shift&goto :parse
if /i "%~1"=="--max-tickets" set MAX_TICKETS=%~2&shift&shift&goto :parse
if /i "%~1"=="--skip-history" set SKIP_HISTORY=--skip-history&shift&goto :parse
if /i "%~1"=="--skip-tickets" set SKIP_TICKETS=--skip-tickets&shift&goto :parse
shift
goto :parse

:execute
echo Running with: Days=%DAYS%, Minutes=%MINUTES%, Max Tickets=%MAX_TICKETS% %SKIP_HISTORY% %SKIP_TICKETS%
python load_and_analyze.py --days %DAYS% --minutes %MINUTES% --max-tickets %MAX_TICKETS% %SKIP_HISTORY% %SKIP_TICKETS%

echo Agent Activity Monitor completed
pause
