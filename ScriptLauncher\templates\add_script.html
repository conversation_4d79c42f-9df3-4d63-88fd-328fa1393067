{% extends "base.html" %}

{% block title %}Script Launcher - Add Script{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-plus me-2"></i>Add New Script</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin') }}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Admin
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Script Details</h5>
            </div>
            <div class="card-body">
                <form id="addScriptForm" method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Script Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="path" class="form-label">Script Path</label>
                        <input type="text" class="form-control" id="path" name="path" required>
                        <div class="form-text">Relative to the workspace root (e.g., StandaloneScripts/script.py)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Script Type</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="python">Python</option>
                                    <option value="powershell">PowerShell</option>
                                    <option value="batch">Batch</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="Bill Processing">Bill Processing</option>
                                    <option value="Reconciliation">Reconciliation</option>
                                    <option value="Utilities">Utilities</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="parameters" class="form-label">Parameters</label>
                        <div id="parametersContainer">
                            <!-- Parameters will be added here dynamically -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="addParameterBtn">
                            <i class="fas fa-plus me-1"></i> Add Parameter
                        </button>
                        <input type="hidden" id="parameters" name="parameters" value="[]">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Script
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Help</h5>
            </div>
            <div class="card-body">
                <h6>Script Path</h6>
                <p>Enter the path to the script file relative to the workspace root directory.</p>
                
                <h6>Script Type</h6>
                <p>Select the type of script you're adding:</p>
                <ul>
                    <li><strong>Python:</strong> .py files that will be executed with Python interpreter</li>
                    <li><strong>PowerShell:</strong> .ps1 files that will be executed with PowerShell</li>
                    <li><strong>Batch:</strong> .bat or .cmd files</li>
                    <li><strong>Other:</strong> Any other script type</li>
                </ul>
                
                <h6>Parameters</h6>
                <p>Define the parameters that users can provide when running the script:</p>
                <ul>
                    <li><strong>Name:</strong> Parameter name (used in command line)</li>
                    <li><strong>Label:</strong> User-friendly label shown in the form</li>
                    <li><strong>Type:</strong> Input type (text, number, checkbox, etc.)</li>
                    <li><strong>Default:</strong> Default value</li>
                    <li><strong>Required:</strong> Whether the parameter is required</li>
                    <li><strong>Help:</strong> Help text shown to users</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        let parameters = [];
        
        // Function to update the parameters JSON
        function updateParametersJson() {
            $('#parameters').val(JSON.stringify(parameters));
        }
        
        // Function to render parameters
        function renderParameters() {
            const container = $('#parametersContainer');
            container.empty();
            
            if (parameters.length === 0) {
                container.append('<div class="alert alert-info">No parameters defined yet.</div>');
                return;
            }
            
            parameters.forEach((param, index) => {
                const paramCard = $(`
                    <div class="card mb-3 parameter-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${param.label || param.name}</h6>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-param-btn" data-index="${index}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">Name</label>
                                        <input type="text" class="form-control param-name" value="${param.name}" data-index="${index}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">Label</label>
                                        <input type="text" class="form-control param-label" value="${param.label || ''}" data-index="${index}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">Type</label>
                                        <select class="form-select param-type" data-index="${index}">
                                            <option value="text" ${param.type === 'text' ? 'selected' : ''}>Text</option>
                                            <option value="number" ${param.type === 'number' ? 'selected' : ''}>Number</option>
                                            <option value="checkbox" ${param.type === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                                            <option value="select" ${param.type === 'select' ? 'selected' : ''}>Select</option>
                                            <option value="file" ${param.type === 'file' ? 'selected' : ''}>File</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">Default Value</label>
                                        <input type="text" class="form-control param-default" value="${param.default || ''}" data-index="${index}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input param-required" type="checkbox" ${param.required ? 'checked' : ''} data-index="${index}">
                                        <label class="form-check-label">Required</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Help Text</label>
                                <input type="text" class="form-control param-help" value="${param.help || ''}" data-index="${index}">
                            </div>
                        </div>
                    </div>
                `);
                
                container.append(paramCard);
            });
            
            // Attach event handlers to the new elements
            $('.param-name').on('change', function() {
                const index = $(this).data('index');
                parameters[index].name = $(this).val();
                updateParametersJson();
            });
            
            $('.param-label').on('change', function() {
                const index = $(this).data('index');
                parameters[index].label = $(this).val();
                updateParametersJson();
            });
            
            $('.param-type').on('change', function() {
                const index = $(this).data('index');
                parameters[index].type = $(this).val();
                updateParametersJson();
            });
            
            $('.param-default').on('change', function() {
                const index = $(this).data('index');
                parameters[index].default = $(this).val();
                updateParametersJson();
            });
            
            $('.param-required').on('change', function() {
                const index = $(this).data('index');
                parameters[index].required = $(this).prop('checked');
                updateParametersJson();
            });
            
            $('.param-help').on('change', function() {
                const index = $(this).data('index');
                parameters[index].help = $(this).val();
                updateParametersJson();
            });
            
            $('.remove-param-btn').on('click', function() {
                const index = $(this).data('index');
                parameters.splice(index, 1);
                updateParametersJson();
                renderParameters();
            });
        }
        
        // Add parameter button click handler
        $('#addParameterBtn').on('click', function() {
            parameters.push({
                name: 'param' + (parameters.length + 1),
                label: 'Parameter ' + (parameters.length + 1),
                type: 'text',
                default: '',
                required: false,
                help: ''
            });
            
            updateParametersJson();
            renderParameters();
        });
        
        // Initial render
        renderParameters();
        
        // Form submission
        $('#addScriptForm').on('submit', function() {
            updateParametersJson();
            return true;
        });
    });
</script>
{% endblock %}
