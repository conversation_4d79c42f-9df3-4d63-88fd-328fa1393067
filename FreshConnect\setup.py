"""
Setup script for the FreshConnect package.
"""

from setuptools import setup, find_packages

setup(
    name="freshconnect",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "requests>=2.25.0",
        "flask>=2.0.0",
        "sqlalchemy>=1.4.0",
        "python-dotenv>=0.19.0",
        "plotly>=5.3.0",
        "pandas>=1.3.0",
        "openpyxl>=3.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "freshconnect=freshconnect.cli:main",
        ],
    },
    author="IT Team",
    author_email="<EMAIL>",
    description="A package for integrating with FreshService and analyzing ticket data",
    keywords="freshservice, tickets, analytics",
    python_requires=">=3.6",
)
