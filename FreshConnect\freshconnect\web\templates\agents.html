{% extends "base.html" %}

{% block title %}FreshConnect - Agent Analysis{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user-tie me-2"></i>Agent Analysis</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="analyzeAgents">
                <i class="fas fa-chart-line me-1"></i> Analyze Activity
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="generateReport">
                <i class="fas fa-file-alt me-1"></i> Generate Report
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshAgents">
                <i class="fas fa-sync-alt me-1"></i> Refresh Agent Data
            </button>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="far fa-calendar-alt me-1"></i> Last 7 Days
            </button>
            <ul class="dropdown-menu" aria-labelledby="timeRangeDropdown">
                <li><a class="dropdown-item" href="#" data-range="7">Last 7 Days</a></li>
                <li><a class="dropdown-item" href="#" data-range="30">Last 30 Days</a></li>
                <li><a class="dropdown-item" href="#" data-range="90">Last 90 Days</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-range="custom">Custom Range</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Recent Agent Activity -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history me-1"></i> Recent Agent Activity (Last 10 Minutes)
            </div>
            <div class="card-body">
                {% if recent_activity %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Period (Minutes)</th>
                                <th>Active Agents</th>
                                <th>Actions</th>
                                <th>Active Tickets</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in recent_activity %}
                            <tr>
                                <td>{{ activity.timestamp }}</td>
                                <td>{{ activity.period_minutes }}</td>
                                <td>{{ activity.unique_actors }}</td>
                                <td>{{ activity.action_count }}</td>
                                <td>{{ activity.active_tickets }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    No recent agent activity data available. Activity is tracked every 10 minutes.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Agent Activity Visualization -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-1"></i> Agent Activity
            </div>
            <div class="card-body">
                {% if visualization_url %}
                <div class="text-center">
                    <iframe src="{{ visualization_url }}" width="100%" height="600" frameborder="0"></iframe>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <p>No agent activity visualization available. Click "Analyze Activity" to generate a visualization.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Agent Stats -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-1"></i> Agent Statistics
            </div>
            <div class="card-body">
                {% if agent_stats %}
                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-primary">{{ agent_stats.unique_actors }}</div>
                                <div class="stat-label">Unique Agents</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-success">{{ agent_stats.action_count }}</div>
                                <div class="stat-label">Total Actions</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-warning">{{ agent_stats.avg_actions_per_agent }}</div>
                                <div class="stat-label">Avg Actions/Agent</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-info">{{ agent_stats.group_changes }}</div>
                                <div class="stat-label">Group Changes</div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <p>No agent statistics available. Click "Analyze Activity" to generate statistics.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Top Agents -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy me-1"></i> Top Agents by Activity
            </div>
            <div class="card-body">
                {% if top_agents %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Agent</th>
                                <th>Actions</th>
                                <th>Percentage</th>
                                <th>Activity</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in top_agents %}
                            <tr>
                                <td>{{ agent.name }}</td>
                                <td>{{ agent.count }}</td>
                                <td>{{ agent.percentage }}%</td>
                                <td>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ agent.percentage }}%;"
                                            aria-valuenow="{{ agent.percentage }}" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <p>No agent data available. Click "Analyze Activity" to generate data.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exchange-alt me-1"></i> Top Group Setters
            </div>
            <div class="card-body">
                {% if top_group_setters %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Agent</th>
                                <th>Group Changes</th>
                                <th>Percentage</th>
                                <th>Activity</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in top_group_setters %}
                            <tr>
                                <td>{{ agent.name }}</td>
                                <td>{{ agent.count }}</td>
                                <td>{{ agent.percentage }}%</td>
                                <td>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ agent.percentage }}%;"
                                            aria-valuenow="{{ agent.percentage }}" aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <p>No group setter data available. Click "Analyze Activity" to generate data.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Agent Details -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-users me-1"></i> Agent Details
            </div>
            <div class="card-body">
                {% if agent_details %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Groups</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in agent_details %}
                            <tr>
                                <td>{{ agent.id }}</td>
                                <td>{{ agent.name }}</td>
                                <td>{{ agent.email }}</td>
                                <td>
                                    {% if agent.active %}
                                    <span class="badge bg-success">Active</span>
                                    {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% for group in agent.groups %}
                                    <span class="badge bg-info me-1">{{ group.name }}</span>
                                    {% else %}
                                    <span class="text-muted">No groups</span>
                                    {% endfor %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <p>No agent details available. Click "Refresh Agent Data" to fetch the latest agent information.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Agent Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tasks me-1"></i> Recent Agent Actions
            </div>
            <div class="card-body">
                {% if recent_activity and recent_activity[0].actions %}
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Agent</th>
                                <th>Action</th>
                                <th>Ticket</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for action in recent_activity[0].actions|default([])|sort(attribute='timestamp', reverse=True) %}
                            <tr>
                                <td>{{ action.timestamp }}</td>
                                <td>{{ action.actor }}</td>
                                <td>
                                    {% if action.action == 'create' %}
                                    <span class="badge bg-success">Created</span>
                                    {% elif action.action == 'close' %}
                                    <span class="badge bg-danger">Closed</span>
                                    {% elif action.action == 'reopen' %}
                                    <span class="badge bg-warning">Reopened</span>
                                    {% elif action.action == 'reply' %}
                                    <span class="badge bg-info">Replied</span>
                                    {% elif action.action == 'note' %}
                                    <span class="badge bg-secondary">Note</span>
                                    {% elif action.action == 'group_change' %}
                                    <span class="badge bg-primary">Group Change</span>
                                    {% elif action.action == 'status_change' %}
                                    <span class="badge bg-dark">Status Change</span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">{{ action.action }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if action.ticket_id %}
                                    <a href="{{ url_for('ticket_detail', ticket_id=action.ticket_id) }}">#{{ action.ticket_id }}</a>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ action.content|default('')|truncate(50) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    No recent agent actions available.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Analysis Modal -->
<div class="modal fade" id="analysisModal" tabindex="-1" aria-labelledby="analysisModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisModalLabel">Analyze Agent Activity</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('analyze_agents') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="outputFile" class="form-label">Output File Name</label>
                        <input type="text" class="form-control" id="outputFile" name="output_file" value="agent_activity.html">
                        <div class="form-text">Name of the file to save the visualization</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="showPlot" name="show_plot" value="true" checked>
                        <label class="form-check-label" for="showPlot">Show plot in browser</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Analyze</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">Generate Agent Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('generate_agent_report') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reportOutputFile" class="form-label">Output File Name</label>
                        <input type="text" class="form-control" id="reportOutputFile" name="output_file" value="agent_report.html">
                        <div class="form-text">Name of the file to save the report</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Generate Report</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Refresh Agent Data Modal -->
<div class="modal fade" id="refreshAgentModal" tabindex="-1" aria-labelledby="refreshAgentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="refreshAgentModalLabel">Refresh Agent Data</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('refresh_agent_details') }}" method="post">
                <div class="modal-body">
                    <p>This will fetch the latest agent information from FreshService, including:</p>
                    <ul>
                        <li>Agent IDs and names</li>
                        <li>Email addresses</li>
                        <li>Active status</li>
                        <li>Group memberships</li>
                    </ul>
                    <p>The operation may take a few moments to complete.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Refresh Data</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Analyze agents button
        document.getElementById('analyzeAgents').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('analysisModal'));
            modal.show();
        });

        // Generate report button
        document.getElementById('generateReport').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        });

        // Refresh agent data button
        document.getElementById('refreshAgents').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('refreshAgentModal'));
            modal.show();
        });

        // Time range dropdown
        document.querySelectorAll('[data-range]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const range = this.getAttribute('data-range');
                const dropdownButton = document.getElementById('timeRangeDropdown');

                if (range === 'custom') {
                    // Show a modal or form for custom date range
                    alert('Custom date range selector would appear here');
                } else {
                    dropdownButton.innerHTML = `<i class="far fa-calendar-alt me-1"></i> Last ${range} Days`;
                    // Here you would reload the page with the new date range
                    // window.location.href = `{{ url_for('agents') }}?range=${range}`;
                }
            });
        });
    });
</script>
{% endblock %}
