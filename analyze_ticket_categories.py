#!/usr/bin/env python3
"""
Analyze ticket categories and subcategories in FreshService
"""

import os
import requests
from base64 import b64encode
from dotenv import load_dotenv
from collections import defaultdict

# Load environment variables
load_dotenv()

domain = os.getenv('FRESHSERVICE_DOMAIN')
api_key = os.getenv('FRESHSERVICE_API_KEY')

if not domain or not api_key:
    print("Missing FRESHSERVICE_DOMAIN or FRESHSERVICE_API_KEY")
    exit(1)

# Remove protocol if included in domain
domain = domain.replace('https://', '').replace('http://', '')

base_url = f"https://{domain}/api/v2"
auth_token = b64encode(f"{api_key}:X".encode()).decode()

headers = {
    'Authorization': f'Basic {auth_token}',
    'Content-Type': 'application/json'
}

print(f"Analyzing ticket categories in: {domain}")
print("=" * 60)

try:
    categories = set()
    subcategories = set()
    category_subcategory_pairs = defaultdict(set)
    status_counts = defaultdict(int)
    total_tickets = 0
    
    # Fetch multiple pages to get a good sample
    for page in range(1, 4):  # Check first 3 pages (up to 300 tickets)
        url = f"{base_url}/tickets"
        params = {'page': page, 'per_page': 100}
        
        print(f"Fetching page {page}...")
        
        response = requests.get(url, headers=headers, params=params, timeout=30)
        
        if response.status_code != 200:
            print(f"Error on page {page}: {response.status_code}")
            break
            
        data = response.json()
        tickets = data.get('tickets', [])
        
        if not tickets:
            print(f"No tickets found on page {page}")
            break
            
        total_tickets += len(tickets)
        
        for ticket in tickets:
            # Collect categories
            category = ticket.get('category')
            if category:
                categories.add(category)
            
            # Collect subcategories
            subcategory = ticket.get('sub_category')
            if subcategory:
                subcategories.add(subcategory)
            
            # Collect category-subcategory pairs
            if category and subcategory:
                category_subcategory_pairs[category].add(subcategory)
            
            # Count statuses
            status = ticket.get('status')
            status_counts[status] += 1
    
    print(f"\nAnalyzed {total_tickets} tickets")
    print("=" * 60)
    
    # Status mapping
    status_map = {
        2: "Open",
        3: "Pending", 
        4: "Resolved",
        5: "Closed"
    }
    
    print("\nTicket Status Distribution:")
    for status, count in sorted(status_counts.items()):
        status_name = status_map.get(status, f"Status {status}")
        print(f"  {status_name}: {count}")
    
    print(f"\nFound {len(categories)} unique categories:")
    for category in sorted(categories):
        print(f"  - {category}")
    
    print(f"\nFound {len(subcategories)} unique subcategories:")
    for subcategory in sorted(subcategories):
        print(f"  - {subcategory}")
    
    print(f"\nCategory-Subcategory combinations:")
    for category in sorted(category_subcategory_pairs.keys()):
        print(f"\n{category}:")
        for subcategory in sorted(category_subcategory_pairs[category]):
            print(f"  - {subcategory}")
    
    # Check specifically for Security-related tickets
    security_tickets = []
    phishing_tickets = []
    
    print(f"\nLooking for Security and Phishing related tickets...")
    
    # Re-fetch to check for specific matches
    for page in range(1, 4):
        url = f"{base_url}/tickets"
        params = {'page': page, 'per_page': 100}
        
        response = requests.get(url, headers=headers, params=params, timeout=30)
        if response.status_code != 200:
            break
            
        data = response.json()
        tickets = data.get('tickets', [])
        
        if not tickets:
            break
            
        for ticket in tickets:
            category = ticket.get('category', '').lower()
            subcategory = ticket.get('sub_category', '').lower()
            subject = ticket.get('subject', '').lower()
            
            # Look for security-related tickets
            if 'security' in category or 'security' in subject:
                security_tickets.append({
                    'id': ticket.get('id'),
                    'subject': ticket.get('subject'),
                    'category': ticket.get('category'),
                    'sub_category': ticket.get('sub_category'),
                    'status': ticket.get('status')
                })
            
            # Look for phishing-related tickets
            if 'phishing' in subcategory or 'phishing' in subject:
                phishing_tickets.append({
                    'id': ticket.get('id'),
                    'subject': ticket.get('subject'),
                    'category': ticket.get('category'),
                    'sub_category': ticket.get('sub_category'),
                    'status': ticket.get('status')
                })
    
    print(f"\nFound {len(security_tickets)} tickets with 'security' in category or subject:")
    for ticket in security_tickets[:5]:  # Show first 5
        print(f"  #{ticket['id']}: {ticket['subject']} (Category: {ticket['category']}, Status: {status_map.get(ticket['status'], ticket['status'])})")
    
    print(f"\nFound {len(phishing_tickets)} tickets with 'phishing' in subcategory or subject:")
    for ticket in phishing_tickets[:5]:  # Show first 5
        print(f"  #{ticket['id']}: {ticket['subject']} (Subcategory: {ticket['sub_category']}, Status: {status_map.get(ticket['status'], ticket['status'])})")
    
    if not security_tickets and not phishing_tickets:
        print("\nNo Security or Phishing related tickets found in the sample.")
        print("You may need to:")
        print("1. Check if the category/subcategory names are different")
        print("2. Look at more tickets (increase the page range)")
        print("3. Verify that such tickets exist in your FreshService instance")
        
except Exception as e:
    print(f"Error: {e}")
