#!/usr/bin/env python3
"""
Example usage of the FreshService Users Fetcher

This script demonstrates how to use the FreshServiceUserFetcher class
programmatically instead of running the standalone script.
"""

from fetch_freshservice_users import FreshServiceUserFetcher, print_user_details, save_users_to_file

def example_usage():
    """Example of how to use the FreshServiceUserFetcher class."""
    
    try:
        # Option 1: Use environment variables (recommended)
        fetcher = FreshServiceUserFetcher()
        
        # Option 2: Pass credentials directly (not recommended for production)
        # fetcher = FreshServiceUserFetcher(
        #     domain="your-domain.freshservice.com",
        #     api_key="your_api_key_here"
        # )
        
        print("Fetching users from FreshService...")
        
        # Fetch first 10 users
        users = fetcher.fetch_first_10_users()
        
        if users:
            print(f"Successfully fetched {len(users)} users")
            
            # Display user details
            print_user_details(users)
            
            # Save to custom filename
            save_users_to_file(users, "my_users_export.json")
            
            # Example: Extract specific information
            print("\n" + "="*60)
            print("SUMMARY REPORT")
            print("="*60)
            
            active_users = [u for u in users if u.get('active', False)]
            print(f"Total users: {len(users)}")
            print(f"Active users: {len(active_users)}")
            
            # List all email addresses
            emails = [u.get('email', 'No email') for u in users]
            print(f"Email addresses: {', '.join(emails)}")
            
            # List departments
            departments = list(set([u.get('department_names', 'No department') for u in users]))
            print(f"Departments: {', '.join(departments)}")
            
        else:
            print("No users found or error occurred")
            
    except ValueError as e:
        print(f"Configuration error: {e}")
        print("Please set FRESHSERVICE_DOMAIN and FRESHSERVICE_API_KEY environment variables")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    example_usage()
