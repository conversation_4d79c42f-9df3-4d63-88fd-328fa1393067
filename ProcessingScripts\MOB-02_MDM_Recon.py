# MOB-02_MDM_Recon.py - Mobile Device Management Reconciliation Script
#
# Purpose:
# This script reconciles data between Mobile Device Management (MDM) and Lifecycle Services (LS) systems
# to identify discrepancies and ensure all devices are properly tracked in both systems.
#
# Process:
# 1. The script reads Excel files from the Inputs folder, identifying them by their first column:
#    - Files with 'MDM' as the first column contain Mobile Device Management data
#    - Files with 'AssetID' as the first column contain Lifecycle Services data
# 2. It combines all files of each type into consolidated datasets
# 3. It performs two-way reconciliation based on serial numbers:
#    - Creates a 'Device not in LS' tab showing devices in MDM that are missing from LS
#    - Creates a 'Device not in MDM' tab showing devices in LS that are missing from MDM
# 4. Each reconciliation tab includes action columns for tracking remediation efforts
#
# Output:
# The script generates a single Excel file named KC_ITM_21.1_YYYY-MM-DD.xlsx (where YYYY-MM-DD is today's date)
# containing the following tabs:
# - LStoMDM: Combined data from all LS files
# - MDMtoLS: Combined data from all MDM files
# - Device not in LS: Devices found in MDM but missing from LS
# - Device not in MDM: Devices found in LS but missing from MDM
#
# Usage:
# Place MDM and LS Excel files in the Inputs folder and run this script.
# The output file will be created in the Outputs folder.
#
# Author: IT Asset Management Team
# Last Updated: 2025-04-03

import os
import pandas as pd
import glob
from datetime import datetime
import numpy as np
import openpyxl
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.utils import get_column_letter
from openpyxl.utils.dataframe import dataframe_to_rows

def combine_excel_files():
    """
    Combines Excel files from the Inputs folder into a single Excel file with two tabs:
    - LStoMDM: Contains all data from files with first column named 'AssetID'
    - MDMtoLS: Contains all data from files with first column named 'MDM'
    """
    # Define input and output directories
    input_dir = os.path.join(os.path.dirname(__file__), 'Inputs')
    output_dir = os.path.join(os.path.dirname(__file__), 'Outputs')

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Get today's date for the output filename in the format KC_ITM_21.1_YYYY-MM-DD.xlsx
    today_date = datetime.now().strftime("%Y-%m-%d")
    output_file = os.path.join(output_dir, f"KC_ITM_21.1_{today_date}.xlsx")

    # Find all Excel files in the input directory
    excel_files = glob.glob(os.path.join(input_dir, "*.xlsx"))

    if len(excel_files) < 1:
        print(f"Error: No Excel files found in the input directory.")
        return None

    # Lists to store the dataframes for each tab
    ls_to_mdm_files = []
    mdm_to_ls_files = []

    # Process each Excel file
    for file_path in excel_files:
        try:
            # Read the first row to check the column names
            df_header = pd.read_excel(file_path, nrows=1)

            # Check the first column name
            if len(df_header.columns) > 0:
                first_column = df_header.columns[0]
                if first_column == 'AssetID':
                    ls_to_mdm_files.append(file_path)
                    print(f"Found file with 'AssetID' as first column: {os.path.basename(file_path)}")
                elif first_column == 'MDM':
                    mdm_to_ls_files.append(file_path)
                    print(f"Found file with 'MDM' as first column: {os.path.basename(file_path)}")
                else:
                    print(f"Warning: File {os.path.basename(file_path)} doesn't have 'AssetID' or 'MDM' as first column. First column is '{first_column}'")
            else:
                print(f"Warning: File {os.path.basename(file_path)} appears to be empty.")
        except Exception as e:
            print(f"Error processing {os.path.basename(file_path)}: {str(e)}")

    # Check if we found files for both tabs
    if not ls_to_mdm_files:
        print("Warning: No files found with 'AssetID' as the first column.")

    if not mdm_to_ls_files:
        print("Warning: No files found with 'MDM' as the first column.")

    if not ls_to_mdm_files and not mdm_to_ls_files:
        print("Error: No valid files found for either tab.")
        return None

    # Create Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Initialize variables to store the combined dataframes
        combined_ls_to_mdm = None
        combined_mdm_to_ls = None

        # Process LStoMDM files
        if ls_to_mdm_files:
            # Combine all LStoMDM files
            all_ls_to_mdm_data = []
            for file_path in ls_to_mdm_files:
                df = pd.read_excel(file_path)
                all_ls_to_mdm_data.append(df)

            if all_ls_to_mdm_data:
                # Concatenate all dataframes if there are multiple files
                if len(all_ls_to_mdm_data) > 1:
                    combined_ls_to_mdm = pd.concat(all_ls_to_mdm_data, ignore_index=True)
                else:
                    combined_ls_to_mdm = all_ls_to_mdm_data[0]

                # Write to Excel
                combined_ls_to_mdm.to_excel(writer, sheet_name='LStoMDM', index=False)
                print(f"Added {len(combined_ls_to_mdm)} rows to LStoMDM tab")

        # Process MDMtoLS files
        if mdm_to_ls_files:
            # Combine all MDMtoLS files
            all_mdm_to_ls_data = []
            for file_path in mdm_to_ls_files:
                df = pd.read_excel(file_path)
                all_mdm_to_ls_data.append(df)

            if all_mdm_to_ls_data:
                # Concatenate all dataframes if there are multiple files
                if len(all_mdm_to_ls_data) > 1:
                    combined_mdm_to_ls = pd.concat(all_mdm_to_ls_data, ignore_index=True)
                else:
                    combined_mdm_to_ls = all_mdm_to_ls_data[0]

                # Write to Excel
                combined_mdm_to_ls.to_excel(writer, sheet_name='MDMtoLS', index=False)
                print(f"Added {len(combined_mdm_to_ls)} rows to MDMtoLS tab")

        # Perform reconciliation if both dataframes are available
        if combined_mdm_to_ls is not None and combined_ls_to_mdm is not None:
            print("\nPerforming reconciliation based on serial number...")

            # Standardize column names for serial number
            mdm_serial_col = 'Serial number'  # Column name in MDM data
            ls_serial_col = 'Serialnumber'    # Column name in LS data

            # Create sets of serial numbers from both datasets
            ls_serials = set(combined_ls_to_mdm[ls_serial_col].str.lower().dropna())
            print(f"Found {len(ls_serials)} unique serial numbers in LS data")

            mdm_serials = set()
            if mdm_serial_col in combined_mdm_to_ls.columns:
                mdm_serials = set(combined_mdm_to_ls[mdm_serial_col].str.lower().dropna())
                print(f"Found {len(mdm_serials)} unique serial numbers in MDM data")

            # 1. Find devices in MDM that are not in LS
            if mdm_serial_col in combined_mdm_to_ls.columns:
                # Create a mask for devices not in LS, ~ is the inversion operator, similar to NOT in this case
                mask_not_in_ls = ~combined_mdm_to_ls[mdm_serial_col].str.lower().isin(ls_serials)
                devices_not_in_ls = combined_mdm_to_ls[mask_not_in_ls].copy()

                if not devices_not_in_ls.empty:
                    print(f"Found {len(devices_not_in_ls)} devices in MDM that are not in LS")

                    # Select required columns if they exist
                    required_cols = [
                        'AD User company',
                        'AD User office',
                        'AD User Email',
                        'Model',
                        'Serial number'
                    ]

                    # Check which required columns exist
                    existing_cols = [col for col in required_cols if col in devices_not_in_ls.columns]
                    missing_cols = [col for col in required_cols if col not in devices_not_in_ls.columns]

                    # Create missing columns with empty values
                    for col in missing_cols:
                        devices_not_in_ls[col] = np.nan

                    # Add the three new columns
                    devices_not_in_ls['Actions required'] = ''
                    devices_not_in_ls['Ticket Link'] = ''
                    devices_not_in_ls['Actions taken'] = ''

                    # Select and reorder columns for the output
                    final_cols = required_cols + ['Actions required', 'Ticket Link', 'Actions taken']
                    devices_not_in_ls = devices_not_in_ls[final_cols]

                    # Write to Excel
                    devices_not_in_ls.to_excel(writer, sheet_name='Device not in LS', index=False)
                    print(f"Added {len(devices_not_in_ls)} rows to 'Device not in LS' tab")
                else:
                    print("No devices found in MDM that are not in LS")
            else:
                print(f"Warning: Column '{mdm_serial_col}' not found in MDM data, cannot perform reconciliation for 'Device not in LS'")

            # 2. Find devices in LS that are not in MDM
            # Create a mask for devices not in MDM, ~ is the inversion operator, similar to NOT in this case
            mask_not_in_mdm = ~combined_ls_to_mdm[ls_serial_col].str.lower().isin(mdm_serials)
            devices_not_in_mdm = combined_ls_to_mdm[mask_not_in_mdm].copy()

            if not devices_not_in_mdm.empty:
                print(f"Found {len(devices_not_in_mdm)} devices in LS that are not in MDM")

                # Select required columns if they exist
                required_cols = [
                    'AssetName',
                    'Model',
                    'IPLocation',
                    'KA Location (C3)',
                    'BarCode',
                    'Latest Owned User',
                    'Serialnumber'
                ]

                # Check which required columns exist
                existing_cols = [col for col in required_cols if col in devices_not_in_mdm.columns]
                missing_cols = [col for col in required_cols if col not in devices_not_in_mdm.columns]

                # Create missing columns with empty values
                for col in missing_cols:
                    devices_not_in_mdm[col] = np.nan

                # Add the three new columns
                devices_not_in_mdm['Actions required'] = ''
                devices_not_in_mdm['Ticket Link'] = ''
                devices_not_in_mdm['Actions taken'] = ''

                # Select and reorder columns for the output
                final_cols = required_cols + ['Actions required', 'Ticket Link', 'Actions taken']
                devices_not_in_mdm = devices_not_in_mdm[final_cols]

                # Write to Excel
                devices_not_in_mdm.to_excel(writer, sheet_name='Device not in MDM', index=False)
                print(f"Added {len(devices_not_in_mdm)} rows to 'Device not in MDM' tab")

                # No Results or PivotSource tabs as requested
            else:
                print("No devices found in LS that are not in MDM")

        # Auto-adjust column widths
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for i, col in enumerate(worksheet.columns):
                max_length = 0
                column = [cell for cell in col]
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                worksheet.column_dimensions[worksheet.cell(row=1, column=i+1).column_letter].width = adjusted_width

    print(f"\nCombined data saved to {output_file}")

    # No post-processing for Results or PivotSource tabs

    return output_file

if __name__ == "__main__":
    print("Starting Excel file combination process...")
    output_file = combine_excel_files()
    if output_file:
        print(f"Processing complete. Results saved to {output_file}")
    else:
        print("Processing failed. No output file was created.")
