# PowerShell script to run the agent activity monitor
# This can be scheduled as a Windows task

# Parse command line arguments
param (
    [int]$Days = 1,
    [int]$Minutes = 10,
    [int]$MaxTickets = 20,
    [switch]$SkipHistory,
    [switch]$SkipTickets
)

# Get the directory of this script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to the script directory
Set-Location $scriptPath

# Log file path
$logFile = Join-Path $scriptPath "storage\agent_activity_monitor.log"

# Create log directory if it doesn't exist
$logDir = Split-Path -Parent $logFile
if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

# Function to write to log
function Write-Log {
    param (
        [string]$message
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $message" | Out-File -Append -FilePath $logFile
    Write-Host "$timestamp - $message"
}

# Start logging
Write-Log "Starting Agent Activity Monitor"

try {
    # Build arguments
    $scriptArgs = @(
        "--days", $Days,
        "--minutes", $Minutes,
        "--max-tickets", $MaxTickets
    )

    if ($SkipHistory) {
        $scriptArgs += "--skip-history"
    }

    if ($SkipTickets) {
        $scriptArgs += "--skip-tickets"
    }

    # Run the Python script
    Write-Log "Running load_and_analyze.py with arguments: $scriptArgs"
    $output = & python load_and_analyze.py $scriptArgs 2>&1

    # Log the output
    foreach ($line in $output) {
        Write-Log $line
    }

    Write-Log "Agent Activity Monitor completed successfully"
} catch {
    Write-Log "Error running Agent Activity Monitor: $_"
    exit 1
}

exit 0
