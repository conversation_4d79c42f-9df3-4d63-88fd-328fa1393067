{% extends "base.html" %}

{% block title %}Script Launcher - Home{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-terminal me-2"></i>Available Scripts</h1>
</div>

{% if scripts %}
    <div class="row">
        {% for script in scripts %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <span class="badge bg-primary float-end">{{ script.category }}</span>
                        <h5 class="card-title mb-0">{{ script.name }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{{ script.description }}</p>
                        <p class="card-text text-muted">
                            <small>
                                <i class="fas {% if script.type == 'python' %}fa-python{% elif script.type == 'powershell' %}fa-terminal{% else %}fa-code{% endif %} me-1"></i>
                                {{ script.type|capitalize }} Script
                            </small>
                        </p>
                    </div>
                    <div class="card-footer">
                        <a href="{{ url_for('script_form', script_id=script.id) }}" class="btn btn-primary">
                            <i class="fas fa-play me-1"></i> Run Script
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> No scripts found. Add scripts in the Admin section.
    </div>
{% endif %}
{% endblock %}
