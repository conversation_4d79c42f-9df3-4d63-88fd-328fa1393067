"""
Unit tests for the command-line interface.
"""

import pytest
import sys
from unittest.mock import patch, MagicMock
from argparse import Namespace

from freshconnect.cli import parse_args, main


@pytest.mark.unit
class TestCLI:
    """Tests for the command-line interface."""
    
    def test_parse_args_fetch_tickets(self, monkeypatch):
        """Test parsing arguments for the fetch-tickets command."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'fetch-tickets', '--since', '2023-01-01T00:00:00Z'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'fetch-tickets'
        assert args.since == '2023-01-01T00:00:00Z'
        assert args.days is None
        assert args.max is None
    
    def test_parse_args_fetch_tickets_with_days(self, monkeypatch):
        """Test parsing arguments for the fetch-tickets command with days."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'fetch-tickets', '--days', '7'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'fetch-tickets'
        assert args.since is None
        assert args.days == 7
        assert args.max is None
    
    def test_parse_args_fetch_history(self, monkeypatch):
        """Test parsing arguments for the fetch-history command."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'fetch-history', '--ticket-id', '1'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'fetch-history'
        assert args.ticket_id == 1
        assert args.all is False
        assert args.max is None
    
    def test_parse_args_fetch_history_all(self, monkeypatch):
        """Test parsing arguments for the fetch-history command with all flag."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'fetch-history', '--all'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'fetch-history'
        assert args.ticket_id is None
        assert args.all is True
        assert args.max is None
    
    def test_parse_args_analyze_agents(self, monkeypatch):
        """Test parsing arguments for the analyze-agents command."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'analyze-agents', '--output', 'output.html'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'analyze-agents'
        assert args.output == 'output.html'
        assert args.report is False
    
    def test_parse_args_fetch_services(self, monkeypatch):
        """Test parsing arguments for the fetch-services command."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'fetch-services', '--analyze'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'fetch-services'
        assert args.max is None
        assert args.analyze is True
    
    def test_parse_args_web(self, monkeypatch):
        """Test parsing arguments for the web command."""
        # Mock sys.argv
        monkeypatch.setattr(sys, 'argv', ['freshconnect', 'web'])
        
        # Parse arguments
        args = parse_args()
        
        # Verify the result
        assert args.command == 'web'
    
    @patch('freshconnect.cli.fetch_tickets')
    def test_main_fetch_tickets(self, mock_fetch_tickets, monkeypatch):
        """Test the main function with the fetch-tickets command."""
        # Mock parse_args to return a namespace with the fetch-tickets command
        mock_args = Namespace(
            command='fetch-tickets',
            since='2023-01-01T00:00:00Z',
            days=None,
            max=None
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_fetch_tickets.assert_called_once_with('2023-01-01T00:00:00Z', None)
    
    @patch('freshconnect.cli.fetch_ticket_history')
    def test_main_fetch_history(self, mock_fetch_history, monkeypatch):
        """Test the main function with the fetch-history command."""
        # Mock parse_args to return a namespace with the fetch-history command
        mock_args = Namespace(
            command='fetch-history',
            ticket_id=1,
            all=False,
            max=None
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_fetch_history.assert_called_once_with(1)
    
    @patch('freshconnect.cli.fetch_all_ticket_history')
    def test_main_fetch_history_all(self, mock_fetch_all_history, monkeypatch):
        """Test the main function with the fetch-history --all command."""
        # Mock parse_args to return a namespace with the fetch-history command
        mock_args = Namespace(
            command='fetch-history',
            ticket_id=None,
            all=True,
            max=None
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_fetch_all_history.assert_called_once_with(None)
    
    def test_main_fetch_history_error(self, monkeypatch):
        """Test the main function with the fetch-history command with no arguments."""
        # Mock parse_args to return a namespace with the fetch-history command
        mock_args = Namespace(
            command='fetch-history',
            ticket_id=None,
            all=False,
            max=None
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 1
        mock_setup_logging.assert_called_once()
    
    @patch('freshconnect.cli.analyze_agent_activity')
    def test_main_analyze_agents(self, mock_analyze_agents, monkeypatch):
        """Test the main function with the analyze-agents command."""
        # Mock parse_args to return a namespace with the analyze-agents command
        mock_args = Namespace(
            command='analyze-agents',
            output='output.html',
            report=False
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Mock analyze_agent_activity to return a result
        mock_analyze_agents.return_value = {'action_count': 10, 'unique_actors': 2}
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_analyze_agents.assert_called_once_with('output.html')
    
    @patch('freshconnect.cli.generate_agent_report')
    def test_main_analyze_agents_report(self, mock_generate_report, monkeypatch):
        """Test the main function with the analyze-agents --report command."""
        # Mock parse_args to return a namespace with the analyze-agents command
        mock_args = Namespace(
            command='analyze-agents',
            output='output.html',
            report=True
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Mock generate_agent_report to return a result
        mock_generate_report.return_value = {
            'metrics': {'action_count': 10, 'unique_actors': 2}
        }
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_generate_report.assert_called_once_with('output.html')
    
    @patch('freshconnect.cli.fetch_service_items')
    def test_main_fetch_services(self, mock_fetch_services, monkeypatch):
        """Test the main function with the fetch-services command."""
        # Mock parse_args to return a namespace with the fetch-services command
        mock_args = Namespace(
            command='fetch-services',
            max=None,
            analyze=False
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_fetch_services.assert_called_once_with(None)
    
    @patch('freshconnect.cli.fetch_service_items')
    @patch('freshconnect.cli.analyze_service_catalog')
    def test_main_fetch_services_analyze(self, mock_analyze_catalog, mock_fetch_services, monkeypatch):
        """Test the main function with the fetch-services --analyze command."""
        # Mock parse_args to return a namespace with the fetch-services command
        mock_args = Namespace(
            command='fetch-services',
            max=None,
            analyze=True
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Mock analyze_service_catalog to return a result
        mock_analyze_catalog.return_value = {'total_items': 10, 'categories': {'Category 1': 5, 'Category 2': 5}}
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_fetch_services.assert_called_once_with(None)
        mock_analyze_catalog.assert_called_once()
    
    @patch('freshconnect.cli.run_app')
    def test_main_web(self, mock_run_app, monkeypatch):
        """Test the main function with the web command."""
        # Mock parse_args to return a namespace with the web command
        mock_args = Namespace(
            command='web'
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 0
        mock_setup_logging.assert_called_once()
        mock_run_app.assert_called_once()
    
    def test_main_no_command(self, monkeypatch):
        """Test the main function with no command."""
        # Mock parse_args to return a namespace with no command
        mock_args = Namespace(
            command=None
        )
        monkeypatch.setattr('freshconnect.cli.parse_args', lambda: mock_args)
        
        # Mock setup_logging
        mock_setup_logging = MagicMock()
        monkeypatch.setattr('freshconnect.config.settings.setup_logging', mock_setup_logging)
        
        # Call the function
        result = main()
        
        # Verify the result
        assert result == 1
        mock_setup_logging.assert_called_once()
