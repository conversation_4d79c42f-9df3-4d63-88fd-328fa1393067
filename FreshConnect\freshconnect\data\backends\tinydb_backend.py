"""
TinyDB backend implementation for the Database interface.
"""

import logging
from tinydb import TinyDB, Query
from ..database import Database

logger = logging.getLogger(__name__)

class TinyDBBackend(Database):
    """TinyDB implementation of the Database interface."""
    
    def __init__(self, db_path, encoding='utf-8'):
        """
        Initialize the TinyDB backend.
        
        Args:
            db_path (str): Path to the database file
            encoding (str, optional): Encoding for the database file. Defaults to 'utf-8'.
        """
        self.db_path = db_path
        self.encoding = encoding
        self.db = TinyDB(db_path, encoding=encoding)
        logger.debug(f"Initialized TinyDB backend at {db_path}")
    
    def get_table(self, table_name):
        """
        Get a table from the database.
        
        Args:
            table_name (str): Name of the table
            
        Returns:
            tinydb.Table: TinyDB table object
        """
        return self.db.table(table_name)
    
    def clear_and_insert_items(self, table_name, items):
        """
        Clear a table and insert new items.
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
            
        Returns:
            int: Number of items inserted
        """
        table = self.get_table(table_name)
        table.truncate()
        table.insert_multiple(items)
        logger.info(f"Cleared table '{table_name}' and inserted {len(items)} items")
        return len(items)
    
    def insert_items(self, table_name, items):
        """
        Insert new items without clearing the table.
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
            
        Returns:
            int: Number of items inserted
        """
        table = self.get_table(table_name)
        table.insert_multiple(items)
        logger.info(f"Inserted {len(items)} items into table '{table_name}'")
        return len(items)
    
    def find_items(self, table_name, query=None):
        """
        Find items in a table.
        
        Args:
            table_name (str): Name of the table
            query (dict, optional): Query criteria. Defaults to None.
            
        Returns:
            list: Matching items
        """
        table = self.get_table(table_name)
        
        if query is None:
            # Return all items
            return table.all()
        
        # Convert dict query to TinyDB Query
        tinydb_query = self._build_query(query)
        return table.search(tinydb_query)
    
    def update_items(self, table_name, query, update_data):
        """
        Update items in a table.
        
        Args:
            table_name (str): Name of the table
            query (dict): Query criteria
            update_data (dict): Data to update
            
        Returns:
            int: Number of items updated
        """
        table = self.get_table(table_name)
        tinydb_query = self._build_query(query)
        result = table.update(update_data, tinydb_query)
        logger.info(f"Updated {len(result)} items in table '{table_name}'")
        return len(result)
    
    def delete_items(self, table_name, query):
        """
        Delete items from a table.
        
        Args:
            table_name (str): Name of the table
            query (dict): Query criteria
            
        Returns:
            int: Number of items deleted
        """
        table = self.get_table(table_name)
        tinydb_query = self._build_query(query)
        result = table.remove(tinydb_query)
        logger.info(f"Deleted {len(result)} items from table '{table_name}'")
        return len(result)
    
    def _build_query(self, query_dict):
        """
        Build a TinyDB query from a dictionary.
        
        Args:
            query_dict (dict): Query criteria as a dictionary
            
        Returns:
            tinydb.Query: TinyDB query object
        """
        if not query_dict:
            return lambda _: True
        
        q = Query()
        conditions = []
        
        for key, value in query_dict.items():
            if isinstance(value, dict):
                # Handle operators like $eq, $gt, $lt, etc.
                for op, op_value in value.items():
                    if op == '$eq':
                        conditions.append(q[key] == op_value)
                    elif op == '$ne':
                        conditions.append(q[key] != op_value)
                    elif op == '$gt':
                        conditions.append(q[key] > op_value)
                    elif op == '$gte':
                        conditions.append(q[key] >= op_value)
                    elif op == '$lt':
                        conditions.append(q[key] < op_value)
                    elif op == '$lte':
                        conditions.append(q[key] <= op_value)
                    elif op == '$in':
                        conditions.append(q[key].one_of(op_value))
                    elif op == '$contains':
                        conditions.append(q[key].search(op_value))
            else:
                # Simple equality
                conditions.append(q[key] == value)
        
        # Combine all conditions with AND
        result = conditions[0]
        for condition in conditions[1:]:
            result = result & condition
            
        return result
