/* Script Launcher Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    padding-top: 56px;
}

.navbar-brand {
    font-weight: bold;
}

.main-content {
    padding: 2rem 1.5rem;
}

.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    font-weight: 500;
}

.form-group {
    margin-bottom: 1rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.table {
    background-color: #fff;
}

.table thead th {
    border-top: none;
    background-color: #f8f9fa;
}

.alert {
    margin-bottom: 1rem;
}

.footer {
    padding: 1.5rem 0;
    color: #6c757d;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Script cards */
.card .card-title {
    font-weight: 600;
}

.card .badge {
    font-size: 0.8rem;
}

/* Form styles */
pre.form-control {
    font-family: 'Consolas', 'Courier New', monospace;
    background-color: #f8f9fa;
    color: #333;
}

/* Parameter cards */
.parameter-card .card-header {
    padding: 0.5rem 1rem;
}

.parameter-card .card-body {
    padding: 1rem;
}

/* Admin table */
.table-responsive {
    overflow-x: auto;
}

.table code {
    white-space: nowrap;
}

/* Script output */
#scriptOutput {
    font-family: 'Consolas', 'Courier New', monospace;
    background-color: #f8f9fa;
    color: #333;
    padding: 1rem;
    border-radius: 0.25rem;
    white-space: pre-wrap;
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}
