#!/usr/bin/env python3
"""
JSON to CSV Converter for Departments

This script converts the departments JSON file to CSV with only ID and Name columns.

Usage:
    python json_to_csv_departments.py <departments_json_file>

Example:
    python json_to_csv_departments.py freshservice_departments_20250711_152359.json
"""

import sys
import json
import csv


def convert_json_to_csv(json_file):
    """
    Convert departments JSON to CSV with ID and Name columns only.
    
    Args:
        json_file (str): Path to the departments JSON file
    """
    print(f"Loading departments from: {json_file}")
    
    # Load JSON data
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    departments = data.get('departments', [])
    print(f"Found {len(departments)} departments")
    
    # Generate output filename
    csv_file = json_file.replace('.json', '_id_name.csv')
    
    # Write to CSV
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        writer.writerow(['id', 'name'])
        
        # Write department data (sorted by name)
        dept_rows = []
        for dept in departments:
            dept_id = dept.get('id')
            dept_name = dept.get('name', '')
            if dept_id:
                dept_rows.append([dept_id, dept_name])
        
        # Sort by name for easier reading
        dept_rows.sort(key=lambda x: x[1].lower())
        
        # Write sorted rows
        writer.writerows(dept_rows)
    
    print(f"CSV file created: {csv_file}")
    print(f"Contains {len(dept_rows)} departments with ID and Name columns")
    
    # Show first few entries
    print(f"\nFirst 10 departments:")
    print("-" * 40)
    for i, (dept_id, dept_name) in enumerate(dept_rows[:10]):
        print(f"{dept_id:>12} | {dept_name}")
    
    if len(dept_rows) > 10:
        print(f"... and {len(dept_rows) - 10} more")


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python json_to_csv_departments.py <departments_json_file>")
        print("Example: python json_to_csv_departments.py freshservice_departments_20250711_152359.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        convert_json_to_csv(json_file)
        
    except FileNotFoundError:
        print(f"Error: File '{json_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
