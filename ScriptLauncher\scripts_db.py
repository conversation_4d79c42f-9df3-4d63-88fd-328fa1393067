"""
Database module for Script Launcher.

This module provides functions for managing script metadata in a SQLite database.
"""

import os
import json
import sqlite3
from pathlib import Path

# Database setup
DB_PATH = Path(__file__).parent / 'data' / 'scripts.db'

def init_db():
    """Initialize the SQLite database for script metadata."""
    # Ensure directory exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

    conn = sqlite3.connect(str(DB_PATH))
    cursor = conn.cursor()

    # Create scripts table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS scripts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        path TEXT NOT NULL,
        type TEXT NOT NULL,
        category TEXT,
        parameters TEXT
    )
    ''')

    conn.commit()

    # Add sample scripts if the table is empty
    cursor.execute('SELECT COUNT(*) FROM scripts')
    count = cursor.fetchone()[0]

    if count == 0:
        add_sample_scripts(conn)

    conn.close()

    return DB_PATH

def add_sample_scripts(conn):
    """Add sample scripts to the database."""
    cursor = conn.cursor()

    sample_scripts = [
        {
            'name': 'Verizon Bill Processor',
            'description': 'Process Verizon bills from the Inputs folder and create Excel reports in the Outputs folder.',
            'path': 'ProcessingScripts/verizon-bill-processor.py',
            'type': 'python',
            'category': 'Bill Processing',
            'parameters': [
                {
                    'name': 'input_dir',
                    'label': 'Input Directory',
                    'type': 'text',
                    'default': 'Inputs',
                    'required': False,
                    'help': 'Directory containing PDF bills to process'
                },
                {
                    'name': 'output_dir',
                    'label': 'Output Directory',
                    'type': 'text',
                    'default': 'Outputs',
                    'required': False,
                    'help': 'Directory where Excel reports will be saved'
                }
            ]
        },
        {
            'name': 'Telus Bill Processor',
            'description': 'Process Telus bills and extract phone line details, hardware credits, and usage data.',
            'path': 'StandaloneScripts/telus-bill-processor.py',
            'type': 'python',
            'category': 'Bill Processing',
            'parameters': [
                {
                    'name': 'pdf_path',
                    'label': 'PDF Path',
                    'type': 'text',
                    'default': 'Storage/PDF/telus_bill.pdf',
                    'required': True,
                    'help': 'Path to the Telus bill PDF file'
                }
            ]
        },
        {
            'name': 'MDM Reconciliation',
            'description': 'Run reconciliation between MDM and LS data to identify mismatches. Creates a report with "Device not in MDM" tab that includes action columns.',
            'path': 'ScriptLauncher/scripts/MDMrecon_custom.py',
            'type': 'python',
            'category': 'Reconciliation',
            'parameters': [
                {
                    'name': 'mdm_file',
                    'label': 'MDM Data File',
                    'type': 'file',
                    'required': True,
                    'help': 'Excel file containing MDM data (MDMtoLS.xlsx)'
                },
                {
                    'name': 'ls_file',
                    'label': 'LS Data File',
                    'type': 'file',
                    'required': True,
                    'help': 'Excel file containing LS data (LStoMDM.xlsx)'
                }
            ]
        },
        {
            'name': 'MDM Reconciliation (PowerShell)',
            'description': 'Run reconciliation between MDM and LS data using PowerShell.',
            'path': 'StandaloneScripts/Recons/MDMreconEvidence.ps1',
            'type': 'powershell',
            'category': 'Reconciliation',
            'parameters': []
        }
    ]

    for script in sample_scripts:
        cursor.execute(
            'INSERT INTO scripts (name, description, path, type, category, parameters) VALUES (?, ?, ?, ?, ?, ?)',
            (
                script['name'],
                script['description'],
                script['path'],
                script['type'],
                script['category'],
                json.dumps(script['parameters'])
            )
        )

    conn.commit()

def get_all_scripts():
    """Get all scripts from the database."""
    conn = sqlite3.connect(str(DB_PATH))
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM scripts ORDER BY category, name')
    rows = cursor.fetchall()

    scripts = []
    for row in rows:
        script = dict(row)
        script['parameters'] = json.loads(script['parameters'])
        scripts.append(script)

    conn.close()
    return scripts

def get_script_by_id(script_id):
    """Get a script by its ID."""
    conn = sqlite3.connect(str(DB_PATH))
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM scripts WHERE id = ?', (script_id,))
    row = cursor.fetchone()

    if row:
        script = dict(row)
        script['parameters'] = json.loads(script['parameters'])
        conn.close()
        return script

    conn.close()
    return None

def add_script(script_data):
    """Add a new script to the database."""
    conn = sqlite3.connect(str(DB_PATH))
    cursor = conn.cursor()

    cursor.execute(
        'INSERT INTO scripts (name, description, path, type, category, parameters) VALUES (?, ?, ?, ?, ?, ?)',
        (
            script_data['name'],
            script_data['description'],
            script_data['path'],
            script_data['type'],
            script_data['category'],
            json.dumps(script_data['parameters'])
        )
    )

    conn.commit()
    script_id = cursor.lastrowid
    conn.close()

    return script_id

def update_script(script_data):
    """Update an existing script in the database."""
    conn = sqlite3.connect(str(DB_PATH))
    cursor = conn.cursor()

    cursor.execute(
        'UPDATE scripts SET name = ?, description = ?, path = ?, type = ?, category = ?, parameters = ? WHERE id = ?',
        (
            script_data['name'],
            script_data['description'],
            script_data['path'],
            script_data['type'],
            script_data['category'],
            json.dumps(script_data['parameters']),
            script_data['id']
        )
    )

    conn.commit()
    conn.close()

    return True

def delete_script(script_id):
    """Delete a script from the database."""
    conn = sqlite3.connect(str(DB_PATH))
    cursor = conn.cursor()

    cursor.execute('DELETE FROM scripts WHERE id = ?', (script_id,))

    conn.commit()
    conn.close()

    return True
