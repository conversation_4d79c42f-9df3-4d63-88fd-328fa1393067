#!/usr/bin/env python3
"""
Simple test script to fetch tickets from FreshService
"""

import os
import requests
from base64 import b64encode
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

domain = os.getenv('FRESHSERVICE_DOMAIN')
api_key = os.getenv('FRESHSERVICE_API_KEY')

if not domain or not api_key:
    print("Missing FRESHSERVICE_DOMAIN or FRESHSERVICE_API_KEY")
    exit(1)

# Remove protocol if included in domain
domain = domain.replace('https://', '').replace('http://', '')

base_url = f"https://{domain}/api/v2"
auth_token = b64encode(f"{api_key}:X".encode()).decode()

headers = {
    'Authorization': f'Basic {auth_token}',
    'Content-Type': 'application/json'
}

print(f"Testing connection to: {base_url}")

try:
    # Test basic tickets endpoint
    url = f"{base_url}/tickets"
    params = {'page': 1, 'per_page': 5}
    
    print(f"Making request to: {url}")
    print(f"Parameters: {params}")
    
    response = requests.get(url, headers=headers, params=params, timeout=30)
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        tickets = data.get('tickets', [])
        print(f"Successfully fetched {len(tickets)} tickets")
        
        if tickets:
            print("\nFirst ticket details:")
            ticket = tickets[0]
            print(f"ID: {ticket.get('id')}")
            print(f"Subject: {ticket.get('subject')}")
            print(f"Status: {ticket.get('status')}")
            print(f"Category: {ticket.get('category')}")
            print(f"Sub Category: {ticket.get('sub_category')}")
            print(f"Created: {ticket.get('created_at')}")
            
            # Look for Security/Phishing tickets
            security_phishing = []
            for ticket in tickets:
                if (ticket.get('category') == 'Security' and 
                    ticket.get('sub_category') == 'Phishing'):
                    security_phishing.append(ticket)
            
            print(f"\nFound {len(security_phishing)} Security/Phishing tickets in this batch")
            
            # Show all categories and subcategories found
            categories = set()
            subcategories = set()
            for ticket in tickets:
                if ticket.get('category'):
                    categories.add(ticket.get('category'))
                if ticket.get('sub_category'):
                    subcategories.add(ticket.get('sub_category'))
            
            print(f"\nCategories found: {sorted(categories)}")
            print(f"Subcategories found: {sorted(subcategories)}")
        
    else:
        print(f"Error: {response.status_code}")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")
