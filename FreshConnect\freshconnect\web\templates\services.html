{% extends "base.html" %}

{% block title %}FreshConnect - Service Catalog{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-cogs me-2"></i>Service Catalog</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#fetchServicesModal">
                <i class="fas fa-sync-alt me-1"></i> Fetch Services
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="analyzeServices">
                <i class="fas fa-chart-pie me-1"></i> Analyze Catalog
            </button>
        </div>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="categoryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-filter me-1"></i> Filter by Category
            </button>
            <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
                <li><a class="dropdown-item" href="{{ url_for('services') }}">All Categories</a></li>
                <li><hr class="dropdown-divider"></li>
                {% for category in categories|default([]) %}
                <li><a class="dropdown-item" href="{{ url_for('services', category=category) }}">{{ category }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>

<!-- Service Catalog Stats -->
{% if analysis %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-1"></i> Catalog Analysis
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-primary">{{ analysis.total_items }}</div>
                                <div class="stat-label">Total Items</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-success">{{ analysis.categories|length }}</div>
                                <div class="stat-label">Categories</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-warning">{{ analysis.with_attachments }}</div>
                                <div class="stat-label">With Attachments</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card dashboard-stat">
                            <div class="card-body">
                                <div class="stat-value text-info">{{ analysis.without_attachments }}</div>
                                <div class="stat-label">Without Attachments</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Distribution Chart -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-1"></i> Category Distribution
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-1"></i> Visibility Distribution
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="visibilityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search and Filter Bar -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" name="q" placeholder="Search service items..." value="{{ request.args.get('q', '') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="visibilityFilter" name="visibility">
                            <option value="">All Visibility</option>
                            <option value="all" {% if request.args.get('visibility') == 'all' %}selected{% endif %}>All Users</option>
                            <option value="agents" {% if request.args.get('visibility') == 'agents' %}selected{% endif %}>Agents Only</option>
                            <option value="groups" {% if request.args.get('visibility') == 'groups' %}selected{% endif %}>Specific Groups</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Apply</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Service Items -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-1"></i> Service Items
                {% if selected_category %}
                <span class="badge bg-primary ms-2">{{ selected_category }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if service_items %}
                <div class="row">
                    {% for item in service_items %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ item.name }}</h5>
                            </div>
                            <div class="card-body">
                                <h6 class="card-subtitle mb-2 text-muted">
                                    <i class="fas fa-folder me-1"></i> {{ item.category.name|default('Uncategorized') }}
                                </h6>
                                <p class="card-text">{{ item.description|truncate(100) }}</p>
                                
                                <div class="mb-2">
                                    {% if item.visibility == 'all' %}
                                    <span class="badge bg-success">All Users</span>
                                    {% elif item.visibility == 'agents' %}
                                    <span class="badge bg-warning">Agents Only</span>
                                    {% elif item.visibility == 'groups' %}
                                    <span class="badge bg-info">Specific Groups</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ item.visibility }}</span>
                                    {% endif %}
                                    
                                    {% if item.attachments %}
                                    <span class="badge bg-primary">
                                        <i class="fas fa-paperclip me-1"></i> {{ item.attachments|length }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="{{ url_for('service_detail', service_id=item.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if pagination and pagination.total_pages > 1 %}
                <nav aria-label="Service item pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item {% if pagination.current_page == 1 %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('services', page=pagination.current_page-1, category=selected_category) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        {% for page in range(1, pagination.total_pages + 1) %}
                        <li class="page-item {% if page == pagination.current_page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('services', page=page, category=selected_category) }}">{{ page }}</a>
                        </li>
                        {% endfor %}
                        
                        <li class="page-item {% if pagination.current_page == pagination.total_pages %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('services', page=pagination.current_page+1, category=selected_category) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="alert alert-info">
                    <p>No service items found. Click "Fetch Services" to retrieve the service catalog.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Fetch Services Modal -->
<div class="modal fade" id="fetchServicesModal" tabindex="-1" aria-labelledby="fetchServicesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fetchServicesModalLabel">Fetch Service Catalog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('fetch_services') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="maxItems" class="form-label">Maximum Items</label>
                        <input type="number" class="form-control" id="maxItems" name="max_items" min="1" value="100">
                        <div class="form-text">Maximum number of service items to fetch</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="analyzeAfterFetch" name="analyze" value="true" checked>
                        <label class="form-check-label" for="analyzeAfterFetch">Analyze catalog after fetching</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Fetch Services</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Analyze services button
        document.getElementById('analyzeServices').addEventListener('click', function() {
            window.location.href = "{{ url_for('analyze_services') }}";
        });
        
        {% if analysis %}
        // Category distribution chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: {{ analysis.categories.keys()|list|tojson }},
                datasets: [{
                    label: 'Number of Items',
                    data: {{ analysis.categories.values()|list|tojson }},
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Visibility distribution chart
        const visibilityCtx = document.getElementById('visibilityChart').getContext('2d');
        const visibilityChart = new Chart(visibilityCtx, {
            type: 'doughnut',
            data: {
                labels: {{ analysis.visibility.keys()|list|tojson }},
                datasets: [{
                    data: {{ analysis.visibility.values()|list|tojson }},
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
