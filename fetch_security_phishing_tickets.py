#!/usr/bin/env python3
"""
FreshService Security Phishing Tickets Fetcher

This standalone script fetches closed tickets from FreshService with:
- Category: Security
- Subcategory: Phishing
- Status: Closed
- Limited to 10 results for testing

Usage:
    python fetch_security_phishing_tickets.py

Environment Variables:
    FRESHSERVICE_DOMAIN: Your FreshService domain (e.g., company.freshservice.com)
    FRESHSERVICE_API_KEY: Your FreshService API key

Author: IT Team
Date: 2025-01-03
"""

import os
import sys
import json
import requests
from base64 import b64encode
from datetime import datetime
from dotenv import load_dotenv

class FreshServiceTicketFetcher:
    """A class to fetch tickets from FreshService API."""
    
    def __init__(self, domain=None, api_key=None):
        """
        Initialize the FreshService ticket fetcher.
        
        Args:
            domain (str): FreshService domain (e.g., 'company.freshservice.com')
            api_key (str): FreshService API key
        """
        # Load environment variables from .env file if it exists
        load_dotenv()
        
        self.domain = domain or os.getenv('FRESHSERVICE_DOMAIN')
        self.api_key = api_key or os.getenv('FRESHSERVICE_API_KEY')
        
        if not self.domain:
            raise ValueError("FRESHSERVICE_DOMAIN must be provided as parameter or environment variable")
        
        if not self.api_key:
            raise ValueError("FRESHSERVICE_API_KEY must be provided as parameter or environment variable")
        
        # Remove protocol if included in domain
        self.domain = self.domain.replace('https://', '').replace('http://', '')
        
        self.base_url = f"https://{self.domain}/api/v2"
        self.session = self._create_session()
        
        print(f"Initialized FreshService client for domain: {self.domain}")
    
    def _create_session(self):
        """Create and configure a requests session for API calls."""
        session = requests.Session()
        auth_token = b64encode(f"{self.api_key}:X".encode()).decode()
        session.headers.update({
            'Authorization': f'Basic {auth_token}',
            'Content-Type': 'application/json'
        })
        return session
    
    def _make_request(self, method, endpoint, params=None, retry_count=0):
        """
        Make an HTTP request to the FreshService API with retry logic.
        
        Args:
            method (str): HTTP method (GET, POST, PUT, DELETE)
            endpoint (str): API endpoint (without base URL)
            params (dict, optional): Query parameters
            retry_count (int): Current retry attempt
            
        Returns:
            dict: JSON response from the API
            
        Raises:
            requests.exceptions.RequestException: If the request fails after retries
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        max_retries = 3
        retry_delay = 2  # seconds
        
        try:
            print(f"Making {method} request to: {url}")
            if params:
                print(f"Parameters: {params}")
            
            response = self.session.request(method, url, params=params, timeout=30)
            
            # Handle rate limiting
            if response.status_code == 429:
                if retry_count < max_retries:
                    print(f"Rate limited. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                    import time
                    time.sleep(retry_delay)
                    return self._make_request(method, endpoint, params, retry_count + 1)
                else:
                    raise requests.exceptions.RequestException("Rate limit exceeded after maximum retries")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            if retry_count < max_retries:
                print(f"Request failed: {e}. Retrying in {retry_delay} seconds... (attempt {retry_count + 1})")
                import time
                time.sleep(retry_delay)
                return self._make_request(method, endpoint, params, retry_count + 1)
            else:
                print(f"Request failed after {max_retries} retries: {e}")
                raise
    
    def get_tickets(self, page=1, per_page=30):
        """
        Get tickets from FreshService.

        Args:
            page (int): Page number for pagination
            per_page (int): Number of tickets per page (max 100)

        Returns:
            dict: JSON response containing tickets
        """
        params = {
            'page': page,
            'per_page': min(per_page, 100)  # FreshService API limit
        }

        print(f"Fetching tickets (page {page}, {per_page} per page)")
        return self._make_request('GET', 'tickets', params=params)
    
    def get_ticket(self, ticket_id):
        """
        Get a specific ticket by ID.
        
        Args:
            ticket_id (int): Ticket ID
            
        Returns:
            dict: Ticket details
        """
        print(f"Fetching ticket {ticket_id}")
        return self._make_request('GET', f'tickets/{ticket_id}')
    
    def fetch_security_phishing_tickets(self, limit=10):
        """
        Fetch closed Security/Phishing tickets.

        Args:
            limit (int): Maximum number of tickets to fetch

        Returns:
            list: List of ticket dictionaries
        """
        try:
            # Status codes in FreshService:
            # 2 = Open, 3 = Pending, 4 = Resolved, 5 = Closed

            print("Fetching tickets from FreshService...")
            all_matching_tickets = []
            all_categories = set()
            all_subcategories = set()
            closed_tickets_count = 0
            page = 1
            per_page = 100  # Get more tickets per page to find matches

            while len(all_matching_tickets) < limit:
                print(f"Fetching page {page}...")
                response = self.get_tickets(
                    page=page,
                    per_page=per_page
                )

                tickets = response.get('tickets', [])

                if not tickets:
                    print("No more tickets found.")
                    break

                print(f"Checking {len(tickets)} tickets for Security/Phishing matches...")

                # Collect categories and subcategories for analysis
                for ticket in tickets:
                    if ticket.get('category'):
                        all_categories.add(ticket.get('category'))
                    if ticket.get('sub_category'):
                        all_subcategories.add(ticket.get('sub_category'))
                    if ticket.get('status') == 5:  # Count closed tickets
                        closed_tickets_count += 1

                # Filter for closed Security/Phishing tickets
                for ticket in tickets:
                    # Check if ticket is closed (status 5) and matches category/subcategory
                    if (ticket.get('status') == 5 and  # Closed
                        ticket.get('category') == 'Security' and
                        ticket.get('sub_category') == 'Phishing'):
                        all_matching_tickets.append(ticket)
                        print(f"Found matching ticket: #{ticket.get('id')} - {ticket.get('subject', 'No subject')}")

                        if len(all_matching_tickets) >= limit:
                            break

                page += 1

                # Safety check to avoid infinite loop
                if page > 3:  # Don't fetch more than 3 pages (300 tickets) for testing
                    print("Reached maximum page limit (3 pages)")
                    break

            # Print analysis of what we found
            print(f"\nAnalysis of {(page-1) * per_page} tickets checked:")
            print(f"Closed tickets found: {closed_tickets_count}")
            print(f"Available categories: {sorted(all_categories)}")
            print(f"Available subcategories: {sorted(all_subcategories)}")

            # Limit results to requested number
            result_tickets = all_matching_tickets[:limit]

            print(f"\nSuccessfully found {len(result_tickets)} Security/Phishing tickets")
            return result_tickets

        except Exception as e:
            print(f"Error fetching tickets: {e}")
            return []

def print_ticket_details(tickets):
    """
    Print detailed information about tickets.
    
    Args:
        tickets (list): List of ticket dictionaries
    """
    if not tickets:
        print("No tickets found.")
        return
    
    print(f"\n{'='*80}")
    print(f"SECURITY PHISHING TICKETS - {len(tickets)} CLOSED TICKETS")
    print(f"{'='*80}")
    
    # Status mapping for display
    status_map = {
        2: "Open",
        3: "Pending", 
        4: "Resolved",
        5: "Closed"
    }
    
    # Priority mapping
    priority_map = {
        1: "Low",
        2: "Medium",
        3: "High",
        4: "Urgent"
    }
    
    for i, ticket in enumerate(tickets, 1):
        print(f"\n{'-'*60}")
        print(f"TICKET #{i} - ID: {ticket.get('id', 'N/A')}")
        print(f"{'-'*60}")
        
        # Basic Information
        print(f"Subject: {ticket.get('subject', 'N/A')}")
        print(f"Description: {ticket.get('description_text', 'N/A')[:200]}{'...' if len(ticket.get('description_text', '')) > 200 else ''}")
        print(f"Status: {status_map.get(ticket.get('status'), ticket.get('status', 'N/A'))}")
        print(f"Priority: {priority_map.get(ticket.get('priority'), ticket.get('priority', 'N/A'))}")
        print(f"Category: {ticket.get('category', 'N/A')}")
        print(f"Sub Category: {ticket.get('sub_category', 'N/A')}")
        
        # People
        print(f"Requester ID: {ticket.get('requester_id', 'N/A')}")
        print(f"Responder ID: {ticket.get('responder_id', 'N/A')}")
        print(f"Group ID: {ticket.get('group_id', 'N/A')}")
        
        # Dates
        print(f"Created At: {ticket.get('created_at', 'N/A')}")
        print(f"Updated At: {ticket.get('updated_at', 'N/A')}")
        print(f"Due By: {ticket.get('due_by', 'N/A')}")
        print(f"Resolved At: {ticket.get('resolved_at', 'N/A')}")
        print(f"Closed At: {ticket.get('closed_at', 'N/A')}")
        
        # Additional fields
        print(f"Source: {ticket.get('source', 'N/A')}")
        print(f"Type: {ticket.get('type', 'N/A')}")
        print(f"Tags: {', '.join(ticket.get('tags', []))}")
        
        # Custom Fields (if any)
        custom_fields = ticket.get('custom_fields', {})
        if custom_fields:
            print(f"Custom Fields:")
            for field, value in custom_fields.items():
                print(f"  {field}: {value}")

def save_tickets_to_file(tickets, filename=None):
    """
    Save tickets data to a JSON file.
    
    Args:
        tickets (list): List of ticket dictionaries
        filename (str): Output filename (optional)
    """
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"security_phishing_tickets_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(tickets, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\nTickets data saved to: {filename}")
        
    except Exception as e:
        print(f"Error saving tickets to file: {e}")

def main():
    """Main function to fetch and display Security Phishing tickets."""
    print("FreshService Security Phishing Tickets Fetcher")
    print("=" * 60)
    
    try:
        # Initialize the fetcher
        fetcher = FreshServiceTicketFetcher()
        
        # Fetch Security/Phishing tickets (limited to 10 for testing)
        print("\nFetching closed Security/Phishing tickets from FreshService...")
        tickets = fetcher.fetch_security_phishing_tickets(limit=10)
        
        if tickets:
            # Display ticket details
            print_ticket_details(tickets)
            
            # Save to file
            save_tickets_to_file(tickets)
            
            print(f"\n{'='*80}")
            print(f"SUMMARY: Successfully fetched {len(tickets)} Security/Phishing tickets")
            print(f"{'='*80}")
            
            # Additional summary statistics
            if tickets:
                print(f"Date range:")
                created_dates = [t.get('created_at') for t in tickets if t.get('created_at')]
                if created_dates:
                    print(f"  Oldest: {min(created_dates)}")
                    print(f"  Newest: {max(created_dates)}")
                
                # Count by priority
                priorities = {}
                priority_map = {1: "Low", 2: "Medium", 3: "High", 4: "Urgent"}
                for ticket in tickets:
                    priority = priority_map.get(ticket.get('priority'), 'Unknown')
                    priorities[priority] = priorities.get(priority, 0) + 1
                
                print(f"Priority breakdown:")
                for priority, count in priorities.items():
                    print(f"  {priority}: {count}")
            
        else:
            print("No Security/Phishing tickets were found.")
            print("This could mean:")
            print("- No tickets exist with category 'Security' and subcategory 'Phishing'")
            print("- No closed tickets in this category")
            print("- API permissions may not allow access to tickets")
            
    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("\nPlease ensure you have set the following environment variables:")
        print("- FRESHSERVICE_DOMAIN (e.g., company.freshservice.com)")
        print("- FRESHSERVICE_API_KEY (your FreshService API key)")
        print("\nOr create a .env file with these variables.")
        sys.exit(1)
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
