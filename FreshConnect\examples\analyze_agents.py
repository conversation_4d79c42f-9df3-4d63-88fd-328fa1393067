"""
Example script to analyze agent activity.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the parent directory to the path so we can import freshconnect
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file
load_dotenv()

# Import freshconnect modules
from freshconnect.processors.agents import analyze_agent_activity, generate_agent_report
from freshconnect.config import settings

# Set up logging
settings.setup_logging()
logger = logging.getLogger(__name__)

def main():
    """Main function to demonstrate agent activity analysis."""
    try:
        # Analyze agent activity
        logger.info("Analyzing agent activity...")
        results = analyze_agent_activity(output_file='agent_activity.html')
        
        if results:
            logger.info("Agent activity analysis complete")
            logger.info(f"Total actions: {results['action_count']}")
            logger.info(f"Unique agents: {results['unique_actors']}")
            
            # Print top agents
            logger.info("Top agents by activity:")
            for agent, count in sorted(results['actor_counts'].items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"  {agent}: {count} actions")
        else:
            logger.error("Failed to analyze agent activity")
            return 1
        
        # Generate a comprehensive report
        logger.info("\nGenerating comprehensive agent report...")
        report = generate_agent_report(output_file='agent_report.html')
        
        if report:
            logger.info("Agent report generated")
            logger.info(f"Report timestamp: {report['timestamp']}")
            logger.info("Top group setters:")
            for agent, count in report['top_group_setters'].items():
                logger.info(f"  {agent}: {count} group changes")
        else:
            logger.error("Failed to generate agent report")
            return 1
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
