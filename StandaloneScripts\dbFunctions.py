import os
from tinydb import TinyDB, Query

class DatabaseManager:
    def __init__(self, db_file='db.json', encoding='utf-8'):
        # Ensure Storage directory exists
        storage_dir = os.path.join(os.path.dirname(__file__), 'Storage')
        os.makedirs(storage_dir, exist_ok=True)
        
        # Create full path for database file
        db_path = os.path.join(storage_dir, db_file)
        self.db = TinyDB(db_path, encoding=encoding)
        self.encoding = encoding

    def get_table(self, table_name):
        return self.db.table(table_name)

    def clear_and_insert_items(self, table_name, items):
        """
        Clear a table and insert new items
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
        """
        table = self.get_table(table_name)
        table.truncate()
        table.insert_multiple(items)
        return len(items)

    def insert_items(self, table_name, items):
        """
        Insert new items without clearing the table
        
        Args:
            table_name (str): Name of the table
            items (list): List of items to insert
        """
        table = self.get_table(table_name)
        table.insert_multiple(items)
        return len(items)

def initialize_db(db_file='db.json', encoding='utf-8'):
    try:
        db_manager = DatabaseManager(db_file, encoding=encoding)
        print(f"Successfully initialized database: {db_file}")
        print(f"Database location: {os.path.join(os.path.dirname(__file__), 'Storage', db_file)}")
        return db_manager
    except Exception as e:
        print(f"Error initializing database {db_file}: {str(e)}")
        raise

# Initialize default database
default_db = initialize_db()
